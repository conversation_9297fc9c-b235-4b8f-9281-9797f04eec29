# Guide de Contribution - LuminaHR Global

Merci de votre intérêt pour contribuer à LuminaHR Global ! Ce guide vous aidera à comprendre comment participer efficacement au développement du projet.

## 🤝 Comment Contribuer

### Types de Contributions
- 🐛 **Correction de bugs** : Signalement et correction d'erreurs
- ✨ **Nouvelles fonctionnalités** : Proposition et développement de nouvelles features
- 📚 **Documentation** : Amélioration de la documentation
- 🧪 **Tests** : Ajout ou amélioration des tests
- 🎨 **Design/UX** : Améliorations de l'interface utilisateur
- 🚀 **Performance** : Optimisations de performance
- 🔒 **Sécurité** : Améliorations de sécurité

### Processus de Contribution
1. **Fork** du repository
2. **Création** d'une branche feature
3. **Développement** avec tests
4. **Pull Request** avec description détaillée
5. **Code Review** et validation
6. **Merge** après approbation

## 🛠️ Configuration de l'Environnement de Développement

### Prérequis
- Node.js 18+
- pnpm (recommandé) ou npm
- Git
- VS Code (recommandé)

### Installation
```bash
# Cloner votre fork
git clone https://github.com/VOTRE-USERNAME/lumina-hr-global.git
cd lumina-hr-global

# Ajouter le repository original comme remote
git remote add upstream https://github.com/lumina-hr/lumina-hr-global.git

# Installer les dépendances
pnpm install

# Démarrer le serveur de développement
pnpm start
```

### Extensions VS Code Recommandées
```json
{
  "recommendations": [
    "angular.ng-template",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ]
}
```

## 📝 Standards de Code

### Style de Code
- **ESLint** : Configuration stricte avec règles Angular
- **Prettier** : Formatage automatique du code
- **TypeScript** : Typage strict activé
- **Naming Conventions** : PascalCase pour les classes, camelCase pour les variables

### Structure des Fichiers
```
feature/
├── components/
│   ├── feature-list/
│   │   ├── feature-list.component.ts
│   │   ├── feature-list.component.html
│   │   ├── feature-list.component.scss
│   │   └── feature-list.component.spec.ts
│   └── feature-detail/
├── services/
│   ├── feature.service.ts
│   └── feature.service.spec.ts
├── models/
│   └── feature.model.ts
└── feature.routes.ts
```

### Conventions de Nommage
- **Composants** : `kebab-case` (ex: `employee-list.component.ts`)
- **Services** : `camelCase` avec suffix `.service` (ex: `employeeService`)
- **Interfaces** : `PascalCase` (ex: `Employee`, `DocumentModel`)
- **Enums** : `PascalCase` (ex: `DocumentStatus`)
- **Constants** : `UPPER_SNAKE_CASE` (ex: `API_ENDPOINTS`)

## 🧪 Tests

### Types de Tests
- **Unit Tests** : Tests unitaires avec Jest
- **Component Tests** : Tests des composants Angular
- **Integration Tests** : Tests d'intégration des services
- **E2E Tests** : Tests end-to-end avec Cypress

### Exécution des Tests
```bash
# Tests unitaires
pnpm test

# Tests en mode watch
pnpm test:watch

# Tests avec couverture
pnpm test:coverage

# Tests E2E
pnpm e2e
```

### Standards de Tests
- **Couverture minimale** : 80%
- **Nommage** : `describe` pour les suites, `it` pour les cas
- **Mocking** : Utilisation de mocks pour les dépendances externes
- **Assertions** : Utilisation de `expect` avec des matchers explicites

### Exemple de Test
```typescript
describe('DocumentService', () => {
  let service: DocumentService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DocumentService]
    });
    service = TestBed.inject(DocumentService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  it('should retrieve documents', () => {
    const mockDocuments = [{ id: '1', name: 'Test Doc' }];
    
    service.getDocuments().subscribe(documents => {
      expect(documents).toEqual(mockDocuments);
    });

    const req = httpMock.expectOne('/api/documents');
    expect(req.request.method).toBe('GET');
    req.flush(mockDocuments);
  });
});
```

## 📋 Workflow Git

### Branches
- **main** : Branche principale stable
- **develop** : Branche de développement
- **feature/*** : Branches de fonctionnalités
- **bugfix/*** : Branches de correction de bugs
- **hotfix/*** : Corrections urgentes

### Convention de Nommage des Branches
```bash
# Nouvelles fonctionnalités
feature/document-management
feature/advanced-reporting

# Corrections de bugs
bugfix/login-validation-error
bugfix/document-upload-issue

# Corrections urgentes
hotfix/security-vulnerability
```

### Messages de Commit
Utilisation de [Conventional Commits](https://www.conventionalcommits.org/) :

```bash
# Format
<type>[optional scope]: <description>

# Exemples
feat(documents): add document upload functionality
fix(auth): resolve login validation issue
docs(readme): update installation instructions
test(reports): add unit tests for report service
refactor(components): extract reusable button component
```

### Types de Commits
- `feat` : Nouvelle fonctionnalité
- `fix` : Correction de bug
- `docs` : Documentation
- `style` : Formatage, point-virgules manquants, etc.
- `refactor` : Refactoring de code
- `test` : Ajout de tests
- `chore` : Maintenance, configuration

## 🔍 Code Review

### Checklist du Reviewer
- [ ] Le code respecte les standards de style
- [ ] Les tests sont présents et passent
- [ ] La documentation est mise à jour
- [ ] Pas de code dupliqué
- [ ] Gestion d'erreurs appropriée
- [ ] Performance acceptable
- [ ] Sécurité respectée
- [ ] Accessibilité prise en compte

### Checklist de l'Auteur
- [ ] Tests unitaires ajoutés/mis à jour
- [ ] Documentation mise à jour
- [ ] Code formaté avec Prettier
- [ ] Pas d'erreurs ESLint
- [ ] Build réussit
- [ ] Fonctionnalité testée manuellement

## 📚 Documentation

### Types de Documentation
- **README** : Vue d'ensemble et installation
- **TECHNICAL_DOCUMENTATION** : Architecture et patterns
- **API Documentation** : Documentation des services
- **User Guides** : Guides utilisateur
- **JSDoc** : Documentation du code

### Standards de Documentation
- **JSDoc** pour toutes les méthodes publiques
- **README** mis à jour pour les nouvelles fonctionnalités
- **Exemples de code** dans la documentation
- **Captures d'écran** pour les guides utilisateur

### Exemple de JSDoc
```typescript
/**
 * Uploads a document to the system
 * @param request - The document upload request containing file and metadata
 * @returns Observable that emits the uploaded document
 * @throws {Error} When file size exceeds limit
 * @example
 * ```typescript
 * const request: DocumentUploadRequest = {
 *   file: selectedFile,
 *   name: 'Contract Template',
 *   category: DocumentCategory.CONTRACTS
 * };
 * 
 * this.documentService.uploadDocument(request).subscribe(
 *   document => console.log('Uploaded:', document),
 *   error => console.error('Upload failed:', error)
 * );
 * ```
 */
uploadDocument(request: DocumentUploadRequest): Observable<DocumentModel> {
  // Implementation
}
```

## 🐛 Signalement de Bugs

### Template d'Issue
```markdown
## Description
Brève description du bug

## Étapes pour Reproduire
1. Aller à '...'
2. Cliquer sur '...'
3. Faire défiler jusqu'à '...'
4. Voir l'erreur

## Comportement Attendu
Description de ce qui devrait se passer

## Comportement Actuel
Description de ce qui se passe réellement

## Captures d'Écran
Si applicable, ajoutez des captures d'écran

## Environnement
- OS: [ex: Windows 10]
- Navigateur: [ex: Chrome 91]
- Version: [ex: 1.2.0]

## Informations Supplémentaires
Tout autre contexte utile
```

## ✨ Proposition de Fonctionnalités

### Template de Feature Request
```markdown
## Résumé de la Fonctionnalité
Brève description de la fonctionnalité proposée

## Motivation
Pourquoi cette fonctionnalité est-elle nécessaire ?

## Description Détaillée
Description complète de la fonctionnalité

## Alternatives Considérées
Autres solutions envisagées

## Critères d'Acceptation
- [ ] Critère 1
- [ ] Critère 2
- [ ] Critère 3

## Maquettes/Wireframes
Si applicable, ajoutez des maquettes
```

## 🚀 Déploiement

### Environnements
- **Development** : Déploiement automatique sur chaque commit
- **Staging** : Déploiement automatique sur merge vers develop
- **Production** : Déploiement manuel après validation

### Pipeline CI/CD
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm lint
      - run: pnpm test
      - run: pnpm build
```

## 📞 Support et Communication

### Canaux de Communication
- **GitHub Issues** : Bugs et feature requests
- **GitHub Discussions** : Questions et discussions
- **Email** : <EMAIL> pour les questions privées
- **Discord** : Serveur de développement (lien privé)

### Temps de Réponse
- **Issues critiques** : 24h
- **Pull Requests** : 48h
- **Questions générales** : 72h

## 🏆 Reconnaissance

### Contributeurs
Tous les contributeurs sont reconnus dans :
- **README.md** : Section contributeurs
- **CHANGELOG.md** : Mentions dans les releases
- **GitHub** : Labels et badges de contribution

### Niveaux de Contribution
- **Contributeur** : 1+ PR mergée
- **Collaborateur** : 5+ PRs mergées
- **Mainteneur** : Accès en écriture au repository

---

## 📄 Code de Conduite

Ce projet adhère au [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). En participant, vous vous engagez à respecter ce code.

## 📝 Licence

En contribuant à LuminaHR Global, vous acceptez que vos contributions soient sous licence MIT.

---

Merci de contribuer à LuminaHR Global ! 🎉
