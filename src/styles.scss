/* Polices modernes et professionnelles */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200;300;400;500;600;700;800&display=swap");

@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;
    --card: 210 40% 96%;
    --card-foreground: 240 0% 0%;
    --primary: 347 77% 50%;
    --primary-foreground: 0 0% 100%;
    --muted: 213 27% 84%;
    --muted-foreground: 215 16% 47%;
    --destructive: 350 100% 40%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 95%;
    --card: 0 0% 7%;
    --card-foreground: 0 0% 14.9%;
    --primary: 347 77% 50%;
    --primary-foreground: 0 0% 100%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --destructive: 350 100% 40%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
  }

  :root[data-theme="blue"] {
    --primary: 221.2 83.2% 53.3%;
  }

  .dark[data-theme="blue"] {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 47% 7%;
    --card-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --destructive: 350 100% 40%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
  }

  [type="text"],
  [type="email"],
  [type="url"],
  [type="password"],
  [type="number"],
  [type="date"],
  [type="datetime-local"],
  [type="month"],
  [type="search"],
  [type="tel"],
  [type="time"],
  [type="week"],
  [multiple],
  textarea,
  select {
    @apply w-full rounded-md border border-border bg-background px-3 py-3 text-xs text-foreground placeholder-muted-foreground focus:z-10 focus:border-primary focus:outline-none focus:ring-primary;
  }

  [type="checkbox"],
  [type="radio"] {
    @apply h-4 w-4 rounded border-border bg-background text-primary focus:ring-2 focus:ring-primary;
  }
}

@layer components {
  .dropdown-content {
    @apply pointer-events-none scale-95 opacity-0 duration-100 ease-in;
  }

  .dropdown:hover > .dropdown-content {
    @apply pointer-events-auto block scale-100 animate-fade-in-up opacity-100 duration-200;
  }
}

/** Scroll bar **/
@supports selector(::-webkit-scrollbar) {
  .supports-scrollbars\: pr-2 {
    padding-right: 0.5rem;
  }
}

/** Plugins **/
@import "./assets/styles/apexchart.scss";
