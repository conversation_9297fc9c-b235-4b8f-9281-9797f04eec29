import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { JobOffersComponent } from './components/job-offers/job-offers.component';
import { ApplicationsComponent } from './components/applications/applications.component';
import { ApplicationDetailsComponent } from './components/application-details/application-details.component';
import { RecruitmentRoutingModule } from './recruitment-routing.module';
import { JobOfferModalComponent } from './components/job-offer-form/job-offer-modal.component';

@NgModule({
  declarations: [
    JobOffersComponent,
    ApplicationsComponent,
    ApplicationDetailsComponent,
    JobOfferModalComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    RecruitmentRoutingModule,
  ],
})
export class RecruitmentModule {}
