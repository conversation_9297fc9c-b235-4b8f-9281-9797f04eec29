<div class="flex flex-col rounded-xl bg-white p-6 dark:bg-gray-800">
  <!-- Header -->
  <div class="mb-6 flex items-center justify-between">
    <button
      routerLink="/dashboard/recruitment/applications"
      class="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 19l-7-7m0 0l7-7m-7 7h18"
        />
      </svg>
      Retour à la liste
    </button>
    <div class="flex gap-3">
      <button
        *ngIf="application?.status === ApplicationStatusEnum.PENDING"
        (click)="updateStatus(ApplicationStatusEnum.APPROVED)"
        class="flex items-center gap-2 rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        Approuver
      </button>
      <button
        *ngIf="application?.status === ApplicationStatusEnum.PENDING"
        (click)="updateStatus(ApplicationStatusEnum.REJECTED)"
        class="flex items-center gap-2 rounded-lg bg-red-50 px-4 py-2 text-sm font-medium text-red-600 transition hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
        Rejeter
      </button>
    </div>
  </div>

  <!-- Loading state -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center py-12">
    <div
      class="h-10 w-10 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
    ></div>
    <p class="mt-3 text-sm text-gray-600 dark:text-gray-400">
      Chargement de la candidature...
    </p>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="mb-6 rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/30 dark:text-red-400"
  >
    <p>{{ error }}</p>
  </div>

  <!-- Application details -->
  <div
    *ngIf="!loading && !error && application"
    class="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
  >
    <div class="p-5">
      <!-- Application header -->
      <div class="flex items-start justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
            Candidature de {{ application.user?.firstName }}
            {{ application.user?.lastName }}
          </h3>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Pour le poste de {{ application.jobOffer?.title }}
          </p>
        </div>
        <span
          [class]="
            'px-3 py-1 rounded-full text-xs font-medium ' +
            getStatusColor(application.status)
          "
        >
          {{ getStatusLabel(application.status) }}
        </span>
      </div>

      <!-- Application details -->
      <div class="mt-6 space-y-4">
        <div
          class="flex flex-col sm:flex-row sm:items-center justify-between border-b border-gray-100 pb-4 dark:border-gray-700"
        >
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Date de candidature</span
          >
          <span class="text-sm text-gray-600 dark:text-gray-400">{{
            application.applicationDate | date : "dd/MM/yyyy"
          }}</span>
        </div>

        <div
          class="flex flex-col border-b border-gray-100 pb-4 dark:border-gray-700"
        >
          <span
            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Lettre de motivation</span
          >
          <p
            class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line"
          >
            {{ application.coverLetter }}
          </p>
        </div>

        <div
          class="flex flex-col sm:flex-row sm:items-center justify-between border-b border-gray-100 pb-4 dark:border-gray-700"
        >
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >CV</span
          >
          <a
            [href]="application.resume"
            target="_blank"
            class="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
              />
            </svg>
            Télécharger le CV
          </a>
        </div>

        <div *ngIf="application.references" class="flex flex-col">
          <span
            class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Références</span
          >
          <p
            class="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line"
          >
            {{ application.references }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Responses section -->
  <div *ngIf="!loading && !error && application" class="mt-8">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
      Réponses
    </h3>

    <!-- Responses list -->
    <div class="space-y-4">
      <div
        *ngFor="let response of responses"
        class="rounded-xl border border-gray-200 bg-white p-5 dark:border-gray-700 dark:bg-gray-800"
      >
        <div class="flex justify-between items-start">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ response.applicationResponseDate | date : "dd/MM/yyyy HH:mm" }}
          </div>
        </div>
        <div
          class="mt-2 text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line"
        >
          {{ response.content }}
        </div>
      </div>
    </div>

    <!-- New response form -->
    <div class="mt-6">
      <form (ngSubmit)="submitResponse()" class="space-y-4">
        <div>
          <label
            for="response"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >Nouvelle réponse</label
          >
          <textarea
            id="response"
            [(ngModel)]="newResponse"
            name="response"
            rows="4"
            class="mt-1 block w-full rounded-lg border-gray-200 shadow-sm focus:border-primary focus:ring-primary sm:text-sm dark:border-gray-700 dark:bg-gray-800 dark:text-white"
            placeholder="Écrivez votre réponse ici..."
          ></textarea>
        </div>
        <div class="flex justify-end">
          <button
            type="submit"
            [disabled]="!newResponse.trim() || loading"
            class="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white transition hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-primary dark:hover:bg-primary/80 dark:focus:ring-primary"
          >
            <svg
              *ngIf="loading"
              class="h-4 w-4 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <span>Envoyer la réponse</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
