import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApplicationService } from 'src/app/core/services/job-offer/application.service';
import { ApplicationResponseService } from 'src/app/core/services/job-offer/application-response.service';
import { Application, ApplicationStatusEnum, ApplicationResponse } from 'src/app/core/services/job-offer/job-offer.model';

@Component({
  selector: 'app-application-details',
  templateUrl: './application-details.component.html',
  standalone:false
})
export class ApplicationDetailsComponent implements OnInit {
  application: Application | null = null;
  responses: ApplicationResponse[] = [];
  loading = false;
  error: string | null = null;
  newResponse = '';
  ApplicationStatusEnum = ApplicationStatusEnum;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private applicationService: ApplicationService,
    private responseService: ApplicationResponseService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadApplication(id);
    }
  }

  loadApplication(id: string): void {
    this.loading = true;
    this.applicationService.findOne(id).subscribe({
      next: (application) => {
        this.application = application;
        this.loadResponses(id);
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement de la candidature';
        this.loading = false;
      }
    });
  }

  loadResponses(applicationId: string): void {
    this.responseService.findAll(applicationId).subscribe({
      next: (responses) => {
        this.responses = responses;
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement des réponses';
      }
    });
  }

  updateStatus(status: ApplicationStatusEnum): void {
    if (!this.application) return;

    this.loading = true;
    this.applicationService.update(this.application.id, { status }).subscribe({
      next: (updatedApplication) => {
        this.application = updatedApplication;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Erreur lors de la mise à jour du statut';
        this.loading = false;
      }
    });
  }

  submitResponse(): void {
    if (!this.application || !this.newResponse.trim()) return;

    this.loading = true;
    this.responseService.create({
      applicationId: this.application.id,
      content: this.newResponse
    }).subscribe({
      next: (response) => {
        this.responses.push(response);
        this.newResponse = '';
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Erreur lors de l\'envoi de la réponse';
        this.loading = false;
      }
    });
  }

  getStatusColor(status: ApplicationStatusEnum): string {
    switch (status) {
      case ApplicationStatusEnum.APPROVED:
        return 'bg-green-100 text-green-800';
      case ApplicationStatusEnum.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  }

  getStatusLabel(status: ApplicationStatusEnum): string {
    switch (status) {
      case ApplicationStatusEnum.APPROVED:
        return 'Approuvée';
      case ApplicationStatusEnum.REJECTED:
        return 'Rejetée';
      default:
        return 'En attente';
    }
  }
} 