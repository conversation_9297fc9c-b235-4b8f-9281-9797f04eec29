import { Component, OnInit } from '@angular/core';
import { select, Store } from '@ngrx/store';
import {
  JobOffersService,
  JobApplication,
  ApplicationStatus,
} from 'src/app/core/services/job-offer/job-offer.service';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-applications',
  templateUrl: './applications.component.html',
  standalone: false,
})
export class ApplicationsComponent implements OnInit {
  applications: JobApplication[] = [];
  loading = false;
  error: string | null = null;
  ApplicationStatus = ApplicationStatus;
  currentCompanyId!: string;

  constructor(
    private jobOffersService: JobOffersService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.loadApplications();
  }

  loadApplications(): void {
    this.loading = true;
    this.jobOffersService.getApplications(this.currentCompanyId).subscribe({
      next: (applications: JobApplication[]) => {
        this.applications = applications;
        this.loading = false;
      },
      error: () => {
        this.error = 'Erreur lors du chargement des candidatures';
        this.loading = false;
      },
    });
  }

  getStatusColor(status: ApplicationStatus): string {
    switch (status) {
      case ApplicationStatus.HIRED:
        return 'bg-green-100 text-green-800';
      case ApplicationStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      case ApplicationStatus.INTERVIEWED:
        return 'bg-blue-100 text-blue-800';
      case ApplicationStatus.SHORTLISTED:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  }

  getStatusLabel(status: ApplicationStatus): string {
    switch (status) {
      case ApplicationStatus.PENDING:
        return 'En attente';
      case ApplicationStatus.UNDER_REVIEW:
        return "En cours d'examen";
      case ApplicationStatus.SHORTLISTED:
        return 'Présélectionné';
      case ApplicationStatus.INTERVIEWED:
        return 'Entretien passé';
      case ApplicationStatus.OFFERED:
        return 'Offre envoyée';
      case ApplicationStatus.HIRED:
        return 'Embauché';
      case ApplicationStatus.REJECTED:
        return 'Rejeté';
      case ApplicationStatus.WITHDRAWN:
        return 'Retiré';
      default:
        return 'Inconnu';
    }
  }

  updateApplicationStatus(
    applicationId: string,
    status: ApplicationStatus
  ): void {
    this.jobOffersService
      .updateApplication(this.currentCompanyId, applicationId, { status })
      .subscribe({
        next: (updatedApplication: JobApplication) => {
          const index = this.applications.findIndex(
            (app) => app.id === applicationId
          );
          if (index !== -1) {
            this.applications[index] = updatedApplication;
          }
        },
        error: () => {
          this.error = 'Erreur lors de la mise à jour du statut';
        },
      });
  }

  downloadResume(applicationId: string): void {
    this.jobOffersService
      .downloadResume(this.currentCompanyId, applicationId)
      .subscribe({
        next: (blob: Blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `CV_${applicationId}.pdf`;
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: () => {
          this.error = 'Erreur lors du téléchargement du CV';
        },
      });
  }

  /**
   * Génère les initiales à partir du nom complet du candidat
   */
  getCandidateInitials(candidateName: string): string {
    if (!candidateName) return '';

    const names = candidateName.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }

    return (
      names[0].charAt(0).toUpperCase() +
      names[names.length - 1].charAt(0).toUpperCase()
    );
  }
}
