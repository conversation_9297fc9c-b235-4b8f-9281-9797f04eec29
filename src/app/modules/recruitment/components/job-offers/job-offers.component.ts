import { Component, OnInit } from '@angular/core';
import { JobOffersService } from 'src/app/core/services/job-offer/job-offer.service';
import {
  CreateJobOfferDto,
  JobOffer,
} from 'src/app/core/services/job-offer/job-offer.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-job-offers',
  templateUrl: './job-offers.component.html',
  standalone: false,
})
export class JobOffersComponent implements OnInit {
  offers: JobOffer[] = [];
  isLoading = false;
  error: string | null = null;

  showOfferModal = false;
  isEditingOffer = false;
  selectedOffer: JobOffer | null = null;
  openMenuId: string | null = null;
  currentCompanyId!: string;

  constructor(
    private jobOffersService: JobOffersService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.loadOffers();
  }

  loadOffers(): void {
    this.isLoading = true;
    this.error = null;

    this.jobOffersService.findAll(this.currentCompanyId).subscribe({
      next: (offers) => {
        this.offers = offers;
        this.isLoading = false;
      },
      error: (err) => {
        this.error = "Erreur lors du chargement des offres d'emploi";
        this.isLoading = false;
      },
    });
  }

  toggleOfferMenu(id: string): void {
    this.openMenuId = this.openMenuId === id ? null : id;
  }

  openOfferModal(isEditing = false, offer: JobOffer | null = null): void {
    this.isEditingOffer = isEditing;
    this.selectedOffer = offer;
    this.showOfferModal = true;
  }

  closeOfferModal(): void {
    this.showOfferModal = false;
    this.selectedOffer = null;
  }

  handleOfferSave(offer: CreateJobOfferDto | JobOffer): void {
    if (this.isEditingOffer && this.selectedOffer) {
      this.updateOffer(offer as JobOffer);
    } else {
      this.createOffer(offer);
    }
  }

  createOffer(offer: CreateJobOfferDto): void {
    this.jobOffersService.create(this.currentCompanyId, offer).subscribe({
      next: (newOffer) => {
        this.offers.push(newOffer);
        this.closeOfferModal();
      },
      error: (err) => {
        this.error = "Erreur lors de la création de l'offre d'emploi";
      },
    });
  }

  updateOffer(offer: JobOffer): void {
    this.jobOffersService
      .update(this.currentCompanyId, offer.id, offer)
      .subscribe({
        next: (updatedOffer) => {
          const index = this.offers.findIndex((o) => o.id === updatedOffer.id);
          if (index !== -1) {
            this.offers[index] = updatedOffer;
          }
          this.closeOfferModal();
        },
        error: (err) => {
          this.error = "Erreur lors de la mise à jour de l'offre d'emploi";
        },
      });
  }

  deleteOffer(id: string): void {
    if (confirm("Êtes-vous sûr de vouloir supprimer cette offre d'emploi ?")) {
      this.jobOffersService.remove(this.currentCompanyId, id).subscribe({
        next: () => {
          this.offers = this.offers.filter((offer) => offer.id !== id);
        },
        error: (err) => {
          this.error = "Erreur lors de la suppression de l'offre d'emploi";
        },
      });
    }
  }

  viewOfferDetails(id: string): void {
    // Naviguer vers la page de détails de l'offre
    // Cette méthode peut être implémentée selon votre routage
    console.log("Voir les détails de l'offre", id);
  }
}
