<!-- job-offers.component.html -->
<div class="flex flex-col rounded-xl">
  <!-- Header -->
  <div class="mb-6 flex items-center justify-between">
    <div class="flex flex-col">
      <h3 class="text-lg font-bold text-gray-800 dark:text-white">
        Offres d'emploi
      </h3>
      <span class="text-xs text-gray-500 dark:text-gray-400"
        >{{ offers.length }} offre(s)</span
      >
    </div>
    <button
      (click)="openOfferModal()"
      class="flex items-center gap-2 rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 4v16m8-8H4"
        />
      </svg>
      Nouvelle offre
    </button>
  </div>

  <!-- Loading state -->
  <div
    *ngIf="isLoading"
    class="flex flex-col items-center justify-center py-12"
  >
    <div
      class="h-10 w-10 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
    ></div>
    <p class="mt-3 text-sm text-gray-600 dark:text-gray-400">
      Chargement des offres d'emploi...
    </p>
  </div>

  <!-- Error state -->
  <div
    *ngIf="error"
    class="mb-6 rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/30 dark:text-red-400"
  >
    <p>{{ error }}</p>
  </div>

  <!-- Empty state -->
  <div
    *ngIf="!isLoading && !error && offers.length === 0"
    class="flex flex-col items-center justify-center py-12"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-12 w-12 text-gray-400"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="1.5"
        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
    <h3 class="mt-3 text-lg font-medium text-gray-700 dark:text-gray-300">
      Aucune offre d'emploi
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Commencez par créer une nouvelle offre d'emploi.
    </p>
    <button
      (click)="openOfferModal()"
      class="mt-4 flex items-center gap-2 rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 4v16m8-8H4"
        />
      </svg>
      Créer une offre
    </button>
  </div>

  <!-- Job offers grid -->
  <div
    *ngIf="!isLoading && !error && offers.length > 0"
    class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
  >
    <div
      *ngFor="let offer of offers"
      class="rounded-xl border border-gray-200 bg-background shadow-sm transition-all hover:shadow-md dark:border-gray-700"
    >
      <div class="p-5">
        <!-- Offer header -->
        <div class="flex items-start justify-between">
          <div>
            <h3
              class="text-lg font-semibold text-gray-800 dark:text-white"
              [title]="offer.title"
            >
              {{ offer.title }}
            </h3>
          </div>
          <div class="relative">
            <button
              (click)="toggleOfferMenu(offer.id)"
              class="rounded-lg p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:hover:bg-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"
                />
              </svg>
            </button>

            <!-- Offer dropdown menu -->
            <div
              *ngIf="openMenuId === offer.id"
              class="absolute right-0 z-10 mt-1 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700"
            >
              <button
                (click)="openOfferModal(true, offer); openMenuId = null"
                class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
                Modifier
              </button>
              <button
                (click)="viewOfferDetails(offer.id); openMenuId = null"
                class="flex w-full items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                Voir les détails
              </button>
              <button
                (click)="deleteOffer(offer.id); openMenuId = null"
                class="flex w-full items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
                Supprimer
              </button>
            </div>
          </div>
        </div>

        <!-- Offer description -->
        <p
          class="mt-2 line-clamp-3 text-sm text-gray-600 dark:text-gray-400"
          [title]="offer.description"
        >
          {{ offer.description }}
        </p>

        <!-- Offer dates and applications -->
        <div class="mt-4 grid grid-cols-2 gap-2">
          <div class="flex flex-col">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300"
              >Publié le:</span
            >
            <span class="text-xs text-gray-600 dark:text-gray-400">{{
              offer.publishDate | date : "dd/MM/yyyy"
            }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300"
              >Expire le:</span
            >
            <span class="text-xs text-gray-600 dark:text-gray-400">{{
              offer.expirationDate | date : "dd/MM/yyyy"
            }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4 flex items-center justify-between">
          <span class="text-xs font-medium text-gray-600 dark:text-gray-400">
            {{ offer.applications?.length || 0 }} candidature(s)
          </span>
          <button
            (click)="viewOfferDetails(offer.id)"
            class="flex items-center gap-1 rounded-lg bg-blue-50 px-3 py-1.5 text-xs font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-3.5 w-3.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            Voir les détails
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modale de création/édition d'offre -->
<app-job-offer-modal
  *ngIf="showOfferModal"
  [isEditing]="isEditingOffer"
  [existingOffer]="selectedOffer"
  (save)="handleOfferSave($event)"
  (close)="closeOfferModal()"
></app-job-offer-modal>
