import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  OnDestroy,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import {
  JobOffer,
  CreateJobOfferDto,
  ContractTypeEnum,
  JobOfferStatusEnum,
} from 'src/app/core/services/job-offer/job-offer.model';

import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import {
  Department,
  PositionResponseDto,
} from 'src/app/core/models/company.model';
import { DepartmentService } from 'src/app/core/services/department/department.service';
import { PositionService } from 'src/app/core/services/department/position.service';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-job-offer-modal',
  templateUrl: './job-offer-modal.component.html',
  standalone: false,
})
export class JobOfferModalComponent implements OnInit, OnDestroy {
  @Input() isEditing = false;
  @Input() existingOffer: JobOffer | null = null;

  @Output() save = new EventEmitter<CreateJobOfferDto & { id?: string }>();
  @Output() close = new EventEmitter<void>();

  offerForm!: FormGroup;
  departments: Department[] = [];
  positions: PositionResponseDto[] = [];
  requiredSkills: string[] = [];
  isSubmitting = false;
  currentCompanyId: string = '';

  // Enum for template usage
  public JobOfferStatusEnum = JobOfferStatusEnum;
  public ContractTypeEnum = ContractTypeEnum;
  contractTypes = Object.values(ContractTypeEnum);
  jobOfferStatuses = Object.values(JobOfferStatusEnum);

  private subscriptions: Subscription = new Subscription();

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private positionService: PositionService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.loadCurrentCompany();
    this.loadDepartments();
    this.initForm();

    if (this.isEditing && this.existingOffer) {
      this.patchFormValues();
    }

    // When department changes, load related positions
    const departmentSub = this.offerForm
      .get('departmentId')
      ?.valueChanges.subscribe((departmentId) => {
        if (departmentId) {
          this.loadPositionsByDepartment(departmentId);
        } else {
          this.positions = [];
        }
      });

    if (departmentSub) {
      this.subscriptions.add(departmentSub);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  loadCurrentCompany(): void {
    const companySub = this.store
      .pipe(select(selectCurrentCompany))
      .subscribe((company) => {
        if (company && company.id) {
          this.currentCompanyId = company.id;
        }
      });

    this.subscriptions.add(companySub);
  }

  initForm(): void {
    this.offerForm = this.fb.group({
      id: [null],
      title: ['', [Validators.required]],
      description: ['', [Validators.required]],
      departmentId: [''],
      positionId: [''],
      contractTypes: [[], [Validators.required]],
      publishDate: [this.formatDate(new Date()), [Validators.required]],
      expirationDate: [
        this.formatDate(this.getDefaultExpirationDate()),
        [Validators.required],
      ],
      location: ['', [Validators.required]],
      minSalary: [null],
      maxSalary: [null],
      status: [JobOfferStatusEnum.ACTIVE],
    });
  }

  patchFormValues(): void {
    if (!this.existingOffer) return;
    console.log(this.existingOffer);

    this.offerForm.patchValue({
      id: this.existingOffer.id,
      title: this.existingOffer.title,
      description: this.existingOffer.description,
      departmentId: this.existingOffer.departmentId || '',
      positionId: this.existingOffer.positionId || '',
      contractTypes: this.existingOffer.contractTypes || [],
      publishDate: this.formatDate(new Date(this.existingOffer.publishDate)),
      expirationDate: this.formatDate(
        new Date(this.existingOffer.expirationDate)
      ),
      location: this.existingOffer.location,
      minSalary: this.existingOffer.minSalary,
      maxSalary: this.existingOffer.maxSalary,
      status: this.existingOffer.status || JobOfferStatusEnum.ACTIVE,
    });

    // Load positions of the selected department
    if (this.existingOffer.departmentId) {
      this.loadPositionsByDepartment(this.existingOffer.departmentId);
    }

    // Set required skills
    if (
      this.existingOffer.requiredSkills &&
      this.existingOffer.requiredSkills.length > 0
    ) {
      this.requiredSkills = [...this.existingOffer.requiredSkills];
    }
  }

  loadDepartments(): void {
    const deptSub = this.departmentService
      .getAllDepartments(this.currentCompanyId)
      .subscribe({
        next: (departments) => {
          this.departments = departments;
        },
        error: (err) => {
          console.error('Erreur lors du chargement des départements', err);
        },
      });

    this.subscriptions.add(deptSub);
  }

  loadPositionsByDepartment(departmentId: string): void {
    const posSub = this.positionService
      .getAllPositions(this.currentCompanyId, departmentId)
      .subscribe({
        next: (positions) => {
          this.positions = positions;
        },
        error: (err) => {
          console.error('Erreur lors du chargement des postes', err);
          this.positions = [];
        },
      });

    this.subscriptions.add(posSub);
  }

  addSkill(skill: string): void {
    skill = skill?.trim();
    if (skill && !this.requiredSkills.includes(skill)) {
      this.requiredSkills.push(skill);
    }
  }

  removeSkill(skill: string): void {
    const index = this.requiredSkills.indexOf(skill);
    if (index !== -1) {
      this.requiredSkills.splice(index, 1);
    }
  }

  onSubmit(): void {
    if (this.offerForm.invalid) {
      this.markFormGroupTouched(this.offerForm);
      return;
    }

    this.isSubmitting = true;

    const formValues = this.offerForm.value;
    const jobOfferDto: CreateJobOfferDto & { id?: string } = {
      title: formValues.title,
      description: formValues.description,
      publishDate: new Date(formValues.publishDate),
      expirationDate: new Date(formValues.expirationDate),
      location: formValues.location,
      contractTypes: formValues.contractTypes,
      status: formValues.status,
      requiredSkills: this.requiredSkills,
      minSalary: formValues.minSalary || undefined,
      maxSalary: formValues.maxSalary || undefined,
      departmentId: formValues.departmentId || undefined,
      positionId: formValues.positionId || undefined,
    };
    if (this.isEditing && formValues.id) {
      jobOfferDto.id = formValues.id;
    }
    this.save.emit(jobOfferDto);
  }

  toggleContractType(type: ContractTypeEnum): void {
    const currentTypes =
      (this.offerForm.get('contractTypes')?.value as ContractTypeEnum[]) || [];

    if (currentTypes.includes(type)) {
      // Remove the type if already selected
      this.offerForm
        .get('contractTypes')
        ?.setValue(currentTypes.filter((t) => t !== type));
    } else {
      // Add the type if not selected
      this.offerForm.get('contractTypes')?.setValue([...currentTypes, type]);
    }
  }

  isContractTypeSelected(type: ContractTypeEnum): boolean {
    const currentTypes =
      (this.offerForm.get('contractTypes')?.value as ContractTypeEnum[]) || [];
    return currentTypes.includes(type);
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private getDefaultExpirationDate(): Date {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date;
  }
}
