import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { JobOffersComponent } from './components/job-offers/job-offers.component';
import { ApplicationsComponent } from './components/applications/applications.component';
import { ApplicationDetailsComponent } from './components/application-details/application-details.component';

const routes: Routes = [
  {
    path: 'job-offers',
    children: [
      { path: '', component: JobOffersComponent },
      { path: ':id', component: ApplicationDetailsComponent },
    ],
  },
  {
    path: 'applications',
    children: [
      { path: '', component: ApplicationsComponent },
      { path: ':id', component: ApplicationDetailsComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RecruitmentRoutingModule {}
