import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TourInitializerService } from '../../../../core/services/tour/tour-initializer.service';

interface DocSection {
  id: string;
  title: string;
  icon: string;
  description: string;
  content: DocContent[];
}

interface DocContent {
  type: 'text' | 'list' | 'code' | 'image' | 'video' | 'warning' | 'tip';
  content: string | string[];
  title?: string;
}

@Component({
  selector: 'app-documentation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './documentation.component.html',
  styles: [
    `
      .doc-content {
        scroll-margin-top: 100px;
      }

      .prose h3 {
        margin-top: 0;
      }

      /* Smooth scrolling */
      html {
        scroll-behavior: smooth;
      }
    `,
  ],
})
export class DocumentationComponent implements OnInit, OnDestroy {
  activeSection = 'getting-started';

  sections: DocSection[] = [
    {
      id: 'getting-started',
      title: 'Premiers pas',
      icon: '🚀',
      description:
        'Découvrez comment commencer avec LuminaHR et configurer votre environnement.',
      content: [
        {
          type: 'text',
          title: 'Bienvenue dans LuminaHR',
          content:
            'LuminaHR est votre solution complète de gestion des ressources humaines. Cette plateforme vous permet de gérer efficacement vos employés, leurs temps de travail, la paie, le recrutement et bien plus encore.',
        },
        {
          type: 'list',
          title: 'Fonctionnalités principales',
          content: [
            'Gestion complète des employés et de leurs profils',
            'Suivi des temps de travail et gestion des congés',
            'Système de paie intégré avec génération de fiches',
            'Module de recrutement avec suivi des candidatures',
            'Gestion des objectifs et évaluations de performance',
            'Gestion des contrats avec templates personnalisables',
            'Bibliothèque documentaire centralisée et sécurisée',
            'Rapports et analytics avancés avec export multi-format',
            'Tableaux de bord interactifs et personnalisables',
            "Gestion des départements et de l'organisation",
            'Interface responsive et moderne avec mode sombre',
          ],
        },
        {
          type: 'tip',
          title: 'Conseil',
          content:
            "Utilisez le tour guidé disponible dans le menu d'aide pour découvrir rapidement toutes les fonctionnalités de l'application.",
        },
      ],
    },
    {
      id: 'dashboard',
      title: 'Tableau de bord',
      icon: '📊',
      description:
        "Comprenez votre tableau de bord et les informations qu'il affiche.",
      content: [
        {
          type: 'text',
          title: "Vue d'ensemble",
          content:
            "Le tableau de bord est votre point central pour surveiller l'état de votre organisation. Il affiche les métriques clés, les tendances et les informations importantes en temps réel.",
        },
        {
          type: 'list',
          title: 'Éléments du tableau de bord',
          content: [
            'Cartes de statistiques des employés (total, nouveaux, actifs)',
            "Graphiques d'évolution des effectifs",
            'Répartition par département et postes',
            'Événements à venir (anniversaires, congés)',
            'Demandes de congés en attente',
            'Objectifs et évaluations en cours',
            'Documents récents et notifications',
            'Métriques de performance RH',
            'Liste rapide des employés récents',
          ],
        },
        {
          type: 'tip',
          title: 'Personnalisation',
          content:
            'Les données du tableau de bord se mettent à jour automatiquement. Vous pouvez cliquer sur les différentes cartes pour accéder aux détails.',
        },
      ],
    },
    {
      id: 'employees',
      title: 'Gestion des employés',
      icon: '👥',
      description:
        'Apprenez à gérer vos employés, leurs profils et leurs informations.',
      content: [
        {
          type: 'text',
          title: 'Module Employés',
          content:
            'Le module employés vous permet de gérer toutes les informations relatives à votre personnel. Vous pouvez ajouter, modifier, rechercher et organiser les données de vos employés.',
        },
        {
          type: 'list',
          title: 'Actions disponibles',
          content: [
            'Ajouter un nouvel employé avec toutes ses informations',
            'Modifier les profils existants',
            'Rechercher et filtrer les employés par critères multiples',
            'Gérer les contrats et les postes',
            'Assigner et suivre les objectifs de performance',
            'Gérer les documents personnels et professionnels',
            "Suivre l'historique des modifications et audit trail",
            'Exporter les données employés en différents formats',
            "Visualiser l'organigramme et les relations hiérarchiques",
          ],
        },
        {
          type: 'warning',
          title: 'Important',
          content:
            'Assurez-vous de respecter les réglementations RGPD lors de la saisie et du traitement des données personnelles des employés.',
        },
      ],
    },
    {
      id: 'time-management',
      title: 'Gestion du temps',
      icon: '⏰',
      description:
        'Gérez les horaires, les feuilles de temps et les demandes de congés.',
      content: [
        {
          type: 'text',
          title: 'Suivi du temps de travail',
          content:
            'Le module de gestion du temps permet de suivre les heures travaillées, gérer les congés et analyser la productivité de vos équipes.',
        },
        {
          type: 'list',
          title: 'Fonctionnalités temps',
          content: [
            'Feuilles de temps électroniques',
            "Demandes de congés avec workflow d'approbation",
            'Calendrier des absences',
            'Suivi des heures supplémentaires',
            'Rapports de présence',
            'Intégration avec la paie',
          ],
        },
        {
          type: 'tip',
          title: 'Automatisation',
          content:
            "Configurez des règles automatiques pour l'approbation des congés selon vos politiques d'entreprise.",
        },
      ],
    },
    {
      id: 'goals',
      title: 'Gestion des objectifs',
      icon: '🎯',
      description:
        'Définissez, suivez et évaluez les objectifs de performance de vos employés.',
      content: [
        {
          type: 'text',
          title: 'Module Objectifs',
          content:
            "Le module de gestion des objectifs vous permet de créer, assigner et suivre les objectifs de performance de vos employés. Il facilite les évaluations et améliore l'engagement des équipes.",
        },
        {
          type: 'list',
          title: 'Fonctionnalités objectifs',
          content: [
            "Création d'objectifs SMART (Spécifiques, Mesurables, Atteignables, Réalistes, Temporels)",
            "Assignation d'objectifs individuels et d'équipe",
            'Suivi de progression en temps réel avec indicateurs visuels',
            "Système d'évaluation avec notes et commentaires",
            'Historique complet des performances',
            'Rapports de performance personnalisés',
            'Notifications automatiques pour les échéances',
          ],
        },
        {
          type: 'tip',
          title: 'Bonnes pratiques',
          content:
            "Définissez des objectifs clairs et mesurables. Effectuez des points réguliers avec vos employés pour maintenir l'engagement et ajuster si nécessaire.",
        },
      ],
    },
    {
      id: 'contracts',
      title: 'Gestion des contrats',
      icon: '📋',
      description:
        'Créez, gérez et suivez les contrats de travail avec des templates personnalisables.',
      content: [
        {
          type: 'text',
          title: 'Module Contrats',
          content:
            'Le système de gestion des contrats vous permet de créer, personnaliser et gérer tous les types de contrats de travail. Utilisez des templates prédéfinis ou créez vos propres modèles.',
        },
        {
          type: 'list',
          title: 'Fonctionnalités contrats',
          content: [
            'Templates de contrats personnalisables (CDI, CDD, Stage, Freelance)',
            'Génération automatique avec variables dynamiques',
            "Workflow de validation et d'approbation",
            'Signatures électroniques intégrées',
            'Gestion du cycle de vie complet des contrats',
            'Alertes pour les renouvellements et échéances',
            'Archivage automatique et recherche avancée',
            'Export en PDF avec mise en forme professionnelle',
          ],
        },
        {
          type: 'warning',
          title: 'Conformité légale',
          content:
            'Assurez-vous que vos templates de contrats respectent la législation du travail en vigueur. Consultez un juriste si nécessaire.',
        },
      ],
    },
    {
      id: 'documents',
      title: 'Gestion documentaire',
      icon: '📁',
      description: 'Centralisez, organisez et sécurisez tous vos documents RH.',
      content: [
        {
          type: 'text',
          title: 'Bibliothèque documentaire',
          content:
            "La gestion documentaire centralisée vous permet de stocker, organiser et partager tous vos documents RH de manière sécurisée. Avec un système de catégorisation avancé et des contrôles d'accès granulaires.",
        },
        {
          type: 'list',
          title: 'Fonctionnalités documentaires',
          content: [
            'Upload par glisser-déposer avec support multi-fichiers',
            'Catégorisation automatique (Contrats, Politiques, Procédures, etc.)',
            'Recherche full-text dans le contenu et les métadonnées',
            "Système d'approbation et workflow de validation",
            'Gestion des versions avec historique complet',
            "Contrôle d'accès par utilisateur et rôle",
            'Audit trail de tous les accès et modifications',
            'Actions en lot (téléchargement, archivage, suppression)',
            'Templates de documents réutilisables',
            'Notifications pour documents expirants',
          ],
        },
        {
          type: 'tip',
          title: 'Organisation',
          content:
            'Utilisez les tags et catégories pour organiser vos documents. Définissez des règles de nommage cohérentes pour faciliter la recherche.',
        },
      ],
    },
    {
      id: 'payroll',
      title: 'Gestion de la paie',
      icon: '💰',
      description:
        'Traitez la paie, générez les fiches de paie et gérez les salaires.',
      content: [
        {
          type: 'text',
          title: 'Module Paie',
          content:
            'Le système de paie intégré vous permet de calculer automatiquement les salaires, générer les fiches de paie et gérer tous les aspects financiers liés aux employés.',
        },
        {
          type: 'list',
          title: 'Capacités de paie',
          content: [
            'Calcul automatique des salaires',
            'Gestion des cotisations sociales',
            'Génération de fiches de paie PDF',
            'Historique des paiements',
            'Déclarations sociales',
            'Intégration bancaire pour les virements',
          ],
        },
        {
          type: 'warning',
          title: 'Sécurité',
          content:
            'Les données de paie sont hautement sensibles. Assurez-vous que seules les personnes autorisées ont accès à ce module.',
        },
      ],
    },
    {
      id: 'recruitment',
      title: 'Recrutement',
      icon: '🎯',
      description:
        "Gérez vos offres d'emploi, candidatures et processus de recrutement.",
      content: [
        {
          type: 'text',
          title: 'Module Recrutement',
          content:
            "Le module de recrutement vous aide à gérer efficacement vos processus d'embauche, depuis la création d'offres d'emploi jusqu'à l'intégration des nouveaux employés.",
        },
        {
          type: 'list',
          title: 'Fonctionnalités de recrutement',
          content: [
            "Création et publication d'offres d'emploi",
            'Gestion des candidatures reçues',
            'Suivi du processus de sélection',
            'Planification des entretiens',
            'Évaluation des candidats',
            'Intégration des nouveaux employés',
          ],
        },
        {
          type: 'tip',
          title: 'Optimisation',
          content:
            "Utilisez les modèles d'offres d'emploi pour gagner du temps et maintenir la cohérence dans vos publications.",
        },
      ],
    },
    {
      id: 'reports',
      title: 'Rapports et Analytics',
      icon: '📈',
      description:
        'Générez des rapports avancés, exportez en multiple formats et analysez vos données RH.',
      content: [
        {
          type: 'text',
          title: 'Système de rapports avancé',
          content:
            "LuminaHR propose un système de rapports complet avec analytics avancées qui vous permet d'analyser tous les aspects de votre gestion RH et de prendre des décisions éclairées basées sur les données.",
        },
        {
          type: 'list',
          title: 'Types de rapports prédéfinis',
          content: [
            'Rapports Employés : effectifs, démographie, turnover, ancienneté',
            'Rapports Présence : taux de présence, retards, absences, heures supplémentaires',
            'Rapports Paie : masse salariale, analyses salariales, équité, distribution',
            'Rapports Recrutement : performance, sources, taux de conversion, coûts',
            'Rapports Formation : activité, compétences, ROI, participation',
            'Rapports Performance : évaluations, objectifs, progression, tendances',
          ],
        },
        {
          type: 'list',
          title: 'Fonctionnalités avancées',
          content: [
            'Export multi-format : PDF, Excel, CSV, HTML, JSON',
            'Impression optimisée avec mise en page professionnelle',
            'Planification automatique de génération de rapports',
            'Graphiques interactifs et visualisations dynamiques',
            'Rapports personnalisés avec builder visuel',
            'Filtres avancés par période, département, critères',
            'Comparaisons année sur année et analyses de tendances',
            'Partage sécurisé et notifications automatiques',
          ],
        },
        {
          type: 'tip',
          title: 'Optimisation',
          content:
            'Planifiez vos rapports récurrents pour automatiser la génération. Utilisez les filtres pour créer des vues spécifiques selon vos besoins métier.',
        },
      ],
    },
    {
      id: 'administration',
      title: 'Administration et Configuration',
      icon: '⚙️',
      description:
        'Configurez votre système, gérez les utilisateurs et personnalisez LuminaHR.',
      content: [
        {
          type: 'text',
          title: "Panneau d'administration",
          content:
            "Le panneau d'administration vous permet de configurer tous les aspects de LuminaHR selon les besoins de votre organisation. Gérez les utilisateurs, les permissions et les paramètres système.",
        },
        {
          type: 'list',
          title: "Fonctionnalités d'administration",
          content: [
            'Gestion des utilisateurs et des rôles',
            'Configuration des permissions granulaires',
            "Paramétrage des workflows d'approbation",
            'Personnalisation des templates et formulaires',
            'Configuration des notifications automatiques',
            "Gestion des départements et de l'organigramme",
            'Paramètres de sécurité et audit',
            'Intégrations avec systèmes externes',
            'Sauvegarde et restauration des données',
          ],
        },
        {
          type: 'warning',
          title: 'Sécurité',
          content:
            "L'accès au panneau d'administration doit être strictement contrôlé. Seuls les administrateurs système autorisés doivent avoir ces privilèges.",
        },
      ],
    },
    {
      id: 'integrations',
      title: 'Intégrations et API',
      icon: '🔗',
      description:
        'Connectez LuminaHR avec vos autres systèmes et utilisez notre API.',
      content: [
        {
          type: 'text',
          title: "Écosystème d'intégrations",
          content:
            "LuminaHR s'intègre facilement avec vos systèmes existants grâce à notre API REST complète et nos connecteurs prédéfinis pour les principales plateformes.",
        },
        {
          type: 'list',
          title: 'Intégrations disponibles',
          content: [
            'Systèmes de paie et comptabilité (SAP, Sage, etc.)',
            'Calendriers (Outlook, Google Calendar)',
            'Messagerie et collaboration (Slack, Teams)',
            "Systèmes d'authentification (LDAP, Active Directory)",
            'Plateformes de signature électronique (DocuSign, Adobe Sign)',
            'Outils de recrutement (LinkedIn, Indeed)',
            'Systèmes de formation (LMS)',
          ],
        },
        {
          type: 'list',
          title: 'API REST',
          content: [
            'Documentation complète avec exemples',
            'Authentification sécurisée par tokens JWT',
            'Endpoints pour toutes les fonctionnalités',
            'Webhooks pour les notifications en temps réel',
            'Rate limiting et monitoring',
            'Support des formats JSON et XML',
          ],
        },
      ],
    },
    {
      id: 'support',
      title: 'Support et aide',
      icon: '🆘',
      description: "Obtenez de l'aide et contactez notre équipe de support.",
      content: [
        {
          type: 'text',
          title: "Obtenir de l'aide",
          content:
            "Notre équipe de support est là pour vous aider à tirer le meilleur parti de LuminaHR. Plusieurs options s'offrent à vous pour obtenir de l'assistance.",
        },
        {
          type: 'list',
          title: 'Canaux de support',
          content: [
            "Tour guidé intégré dans l'application",
            'Documentation technique complète (README, guides)',
            'Support par email : <EMAIL>',
            'Chat en direct (heures ouvrables 9h-18h)',
            'Base de connaissances et FAQ détaillée',
            'Webinaires de formation et démonstrations',
            'Forum communautaire pour les développeurs',
            'Documentation API avec exemples de code',
            'Vidéos tutoriels pour chaque module',
          ],
        },
        {
          type: 'tip',
          title: 'Conseil',
          content:
            'Avant de contacter le support, consultez cette documentation et utilisez le tour guidé. La plupart des questions courantes y trouvent leur réponse.',
        },
      ],
    },
    {
      id: 'whats-new',
      title: 'Nouveautés et Roadmap',
      icon: '🆕',
      description:
        'Découvrez les dernières fonctionnalités et les évolutions à venir.',
      content: [
        {
          type: 'text',
          title: 'Version 1.3.0 - Janvier 2024',
          content:
            'Cette version majeure introduit la gestion documentaire complète et un système de rapports avancé, transformant LuminaHR en une solution RH encore plus complète.',
        },
        {
          type: 'list',
          title: 'Nouvelles fonctionnalités v1.3.0',
          content: [
            '📁 Module de gestion documentaire avec bibliothèque centralisée',
            '📊 Système de rapports avancé avec 6 types de rapports prédéfinis',
            '🎯 Gestion des objectifs et évaluations de performance',
            '📋 Gestion des contrats avec templates personnalisables',
            '🔍 Recherche full-text et filtres avancés',
            '📈 Export multi-format (PDF, Excel, CSV, HTML, JSON)',
            "🔒 Système d'approbation et audit trail complet",
            '⚡ Performance améliorée avec lazy loading',
          ],
        },
        {
          type: 'list',
          title: 'Prochaines versions (Roadmap)',
          content: [
            'v1.4.0 (Q1 2024) : Module de recrutement avancé et IA prédictive',
            'v1.5.0 (Q2 2024) : Module e-learning et chatbot RH intelligent',
            'v2.0.0 (Q3 2024) : Application mobile native et API GraphQL',
            'v2.1.0 (Q4 2024) : Plateforme de talent management complète',
            "v3.0.0 (2025) : Architecture microservices et marketplace d'intégrations",
          ],
        },
        {
          type: 'tip',
          title: 'Feedback',
          content:
            "Vos retours sont précieux ! N'hésitez pas à nous faire part de vos suggestions pour les prochaines versions via notre canal de support.",
        },
      ],
    },
  ];

  constructor(
    private router: Router,
    private tourInitializer: TourInitializerService
  ) {}

  ngOnInit(): void {
    // Observer le scroll pour mettre à jour la section active
    window.addEventListener('scroll', this.onScroll.bind(this));
  }

  ngOnDestroy(): void {
    window.removeEventListener('scroll', this.onScroll.bind(this));
  }

  private onScroll(): void {
    const sections = this.sections.map((s) => s.id);
    const scrollPosition = window.scrollY + 150;

    for (let i = sections.length - 1; i >= 0; i--) {
      const element = document.getElementById(sections[i]);
      if (element && element.offsetTop <= scrollPosition) {
        this.activeSection = sections[i];
        break;
      }
    }
  }

  scrollToSection(sectionId: string, event: Event): void {
    event.preventDefault();
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 100;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/dashboard']);
  }

  startTour(): void {
    this.router.navigate(['/dashboard']).then(() => {
      setTimeout(() => {
        this.tourInitializer.resetAndStartTour();
      }, 500);
    });
  }

  getContentAsArray(content: string | string[]): string[] {
    return Array.isArray(content) ? content : [content];
  }
}
