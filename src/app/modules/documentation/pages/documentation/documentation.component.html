<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Header -->
  <header
    class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <button
            (click)="goBack()"
            class="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              ></path>
            </svg>
          </button>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            📚 Documentation LuminaHR
          </h1>
        </div>
        <button
          (click)="startTour()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors"
        >
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            ></path>
          </svg>
          Tour guidé
        </button>
      </div>
    </div>
  </header>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
      <!-- Sidebar Navigation -->
      <aside class="lg:w-64 flex-shrink-0">
        <nav class="sticky top-8">
          <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
          >
            <h2
              class="text-lg font-semibold text-gray-900 dark:text-white mb-4"
            >
              Sommaire
            </h2>
            <div class="space-y-2">
              <div
                *ngFor="let section of sections"
                (click)="scrollToSection(section.id, $event)"
                class="cursor-pointer flex items-center p-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                <span class="mr-2">{{ section.icon }}</span>
                {{ section.title }}
              </div>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 min-w-0">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <!-- Introduction -->
          <div class="p-8 border-b border-gray-200 dark:border-gray-700">
            <div class="text-center">
              <div
                class="mx-auto w-16 h-16 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  class="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m4 0V9a1 1 0 011-1h4a1 1 0 011 1v12M9 7h6m-6 4h6m-6 4h6"
                  ></path>
                </svg>
              </div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Guide d'utilisation LuminaHR
              </h1>
              <p
                class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto"
              >
                Découvrez comment utiliser efficacement votre solution de
                gestion des ressources humaines. Ce guide vous accompagne dans
                toutes les fonctionnalités de LuminaHR.
              </p>
            </div>
          </div>

          <!-- Sections -->
          <div class="p-8">
            <div
              *ngFor="let section of sections"
              [id]="section.id"
              class="mb-12"
            >
              <div class="flex items-center mb-6">
                <span class="text-2xl mr-3">{{ section.icon }}</span>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ section.title }}
                </h2>
              </div>

              <p class="text-gray-600 dark:text-gray-400 mb-6">
                {{ section.description }}
              </p>

              <div class="space-y-4">
                <div
                  *ngFor="let content of section.content"
                  class="doc-content"
                >
                  <!-- Text Content -->
                  <div
                    *ngIf="content.type === 'text'"
                    class="prose dark:prose-invert max-w-none"
                  >
                    <h3
                      *ngIf="content.title"
                      class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
                    >
                      {{ content.title }}
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {{ content.content }}
                    </p>
                  </div>

                  <!-- List Content -->
                  <div
                    *ngIf="content.type === 'list'"
                    class="prose dark:prose-invert max-w-none"
                  >
                    <h3
                      *ngIf="content.title"
                      class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
                    >
                      {{ content.title }}
                    </h3>
                    <ul
                      class="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300"
                    >
                      <li
                        *ngFor="let item of getContentAsArray(content.content)"
                      >
                        {{ item }}
                      </li>
                    </ul>
                  </div>

                  <!-- Warning Content -->
                  <div
                    *ngIf="content.type === 'warning'"
                    class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 rounded-r-lg"
                  >
                    <div class="flex">
                      <svg
                        class="w-5 h-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        ></path>
                      </svg>
                      <div>
                        <h3
                          *ngIf="content.title"
                          class="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-1"
                        >
                          {{ content.title }}
                        </h3>
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">
                          {{ content.content }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- Tip Content -->
                  <div
                    *ngIf="content.type === 'tip'"
                    class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 rounded-r-lg"
                  >
                    <div class="flex">
                      <svg
                        class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                        ></path>
                      </svg>
                      <div>
                        <h3
                          *ngIf="content.title"
                          class="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-1"
                        >
                          {{ content.title }}
                        </h3>
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                          {{ content.content }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Footer -->
  <footer
    class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="text-center">
        <p class="text-gray-600 dark:text-gray-400">
          © 2024 LuminaHR. Besoin d'aide ?
          <a
            href="mailto:<EMAIL>"
            class="text-primary hover:text-primary/80 transition-colors"
          >
            Contactez notre support
          </a>
        </p>
      </div>
    </div>
  </footer>
</div>
