import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet, NavigationEnd } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { CommonModule } from '@angular/common';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
  imports: [AngularSvgIconModule, RouterOutlet, CommonModule],
})
export class AuthComponent implements OnInit {
  isCreateCompanyRoute = false;

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Détecter la route actuelle
    this.checkRoute();

    // Écouter les changements de route
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
      });
  }

  private checkRoute(): void {
    this.isCreateCompanyRoute = this.router.url.includes('/create-company');
  }
}
