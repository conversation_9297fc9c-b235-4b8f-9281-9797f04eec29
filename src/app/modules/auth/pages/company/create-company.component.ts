import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
  FormArray,
  AbstractControl,
  FormControl,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { CompanyService } from 'src/app/core/services/company/company.service';
import {
  updateForm,
  updateStep,
} from 'src/app/core/store/company/company-form.actions';
import { ToastService } from 'src/app/shared/services/toast.service';
import { ButtonComponent } from 'src/app/shared/components/button/button.component';

@Component({
  selector: 'app-create-company',
  templateUrl: './create-company.component.html',
  styleUrl: './create-company.component.scss',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    ButtonComponent,
  ],
})
export class CreateCompanyComponent implements OnInit {
  form!: FormGroup;
  currentStep$ = this.store.select((state) => state.companyForm.currentStep);
  form$ = this.store.select((state) => state.companyForm.form);
  steps = [
    { label: 'Informations', completed: false },
    { label: 'Adresse', completed: false },
    { label: 'Paramètres', completed: false },
    { label: 'Configuration', completed: false },
    { label: 'Finalisation', completed: false },
  ];
  isLoading = false;
  errorMessage: string | null = null;
  submitted = false;
  currentStep!: number;
  termsAgreed = false;
  logoPreview: string | ArrayBuffer | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private store: Store<AppState>,
    private companyService: CompanyService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.subscribeToStore();
  }

  private initForm(): void {
    this.form = this.fb.group({
      // Étape 1 : Informations de base
      companyName: ['', [Validators.required, Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email]],
      website: [''], // Optionnel maintenant
      logo: [''],

      // Étape 2 : Adresse
      address: this.fb.group({
        street: ['', Validators.required],
        city: ['', Validators.required],
        postalCode: ['', Validators.required],
        country: ['République Démocratique du Congo', Validators.required], // Valeur par défaut
      }),

      // Champs avec valeurs par défaut pour la RDC
      phoneNumbers: this.fb.array([
        this.fb.control('+243', Validators.required),
      ]),

      // Paramètres fiscaux avec valeurs par défaut pour la RDC
      taxSettings: this.fb.group({
        incomeTaxRate: [30], // Taux d'impôt sur le revenu standard RDC
        socialSecurityRate: [3.5], // Taux CNSS RDC
        unEmploymentInsuranceRate: [0], // Pas d'assurance chômage en RDC
        healthInsuranceRate: [0], // Géré différemment en RDC
        pensionContributionRate: [3.5], // Taux pension CNSS RDC
        incomeTaxThreshold: [0],
        socialSecurityThreshold: [0],
        taxPaymentFrequency: ['MONTHLY'],
      }),

      // Configuration paie avec valeurs par défaut
      payrollConfiguration: this.fb.group({
        payrollFrequency: ['MONTHLY'],
        paymentDay: [30], // Fin du mois
        overtimeMultiplier: [1.5],
        maxOvertimeHours: [40],
        bonusType: ['PERCENTAGE_OF_SALARY'],
        performanceBonusRate: [0],
      }),

      // Informations supplémentaires avec valeurs par défaut
      officialName: [''], // Sera rempli automatiquement avec companyName
      taxIdentificationNumber: [''],
      industry: ['Services'], // Valeur par défaut
      description: [''],
    });
  }

  onFileChange(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.logoPreview = reader.result;
        this.form.patchValue({ logo: reader.result });
      };
      reader.readAsDataURL(file);
    }
  }

  getPayrollCycleLabel(cycle: string): string {
    switch (cycle) {
      case 'MONTHLY':
        return 'Mensuel';
      case 'QUARTERLY':
        return 'Trimestriel';
      case 'BIWEEKLY':
        return 'Bi-hebdomadaire';
      case 'WEEKLY':
        return 'Hebdomadaire';
      default:
        return cycle;
    }
  }

  private subscribeToStore(): void {
    this.form$.subscribe((form) =>
      this.form.patchValue(form, { emitEvent: false })
    );
    this.currentStep$.subscribe((step) => {
      this.currentStep = step;
      this.updateStepsCompletion();
    });
  }

  get phoneNumbers(): FormArray<FormControl<string>> {
    return this.form.get('phoneNumbers') as FormArray<FormControl<string>>;
  }

  addPhoneNumber(): void {
    this.phoneNumbers.push(
      new FormControl<string>('', {
        nonNullable: true,
        validators: Validators.required,
      })
    );
  }

  removePhoneNumber(index: number): void {
    if (this.phoneNumbers.length > 1) {
      this.phoneNumbers.removeAt(index);
    }
  }

  getFormGroup(name: string): FormGroup {
    return this.form.get(name) as FormGroup;
  }

  getControl(name: string | string[]): AbstractControl {
    if (Array.isArray(name)) {
      return this.form.get(name) as AbstractControl;
    }
    return this.form.get(name) as AbstractControl;
  }

  nextStep(): void {
    if (this.isCurrentStepValid()) {
      this.store.dispatch(updateForm({ form: this.form.value }));
      this.store.dispatch(updateStep({ step: this.currentStep + 1 }));
      this.markCurrentStepAsComplete();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      this.markAllAsTouched();
      this.toastService.error(
        'Validation échouée',
        'Veuillez corriger les erreurs avant de continuer'
      );
    }
  }

  prevStep(): void {
    this.store.dispatch(updateStep({ step: this.currentStep - 1 }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  goToStep(step: number): void {
    if (step <= this.currentStep) {
      this.store.dispatch(updateStep({ step }));
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  private isCurrentStepValid(): boolean | undefined {
    switch (this.currentStep) {
      case 1:
        return this.isStep1Valid();
      case 2:
        return this.isStep2Valid();
      case 3:
        return this.isStep3Valid();
      case 4:
        return this.isStep4Valid();
      case 5:
        return this.isStep5Valid();
      default:
        return true;
    }
  }

  private isStep1Valid(): boolean | undefined {
    return (
      this.form.get('companyName')?.valid &&
      this.form.get('email')?.valid &&
      this.phoneNumbers.valid &&
      this.form.get('website')?.valid
    );
  }

  private isStep2Valid(): boolean {
    return this.getFormGroup('address').valid;
  }

  private isStep3Valid(): boolean {
    return this.getFormGroup('taxSettings').valid;
  }

  private isStep4Valid(): boolean {
    return this.getFormGroup('payrollConfiguration').valid;
  }

  private isStep5Valid(): boolean | undefined {
    return (
      this.form.get('officialName')?.valid &&
      this.form.get('taxIdentificationNumber')?.valid &&
      this.form.get('industry')?.valid
    );
  }

  private markAllAsTouched(): void {
    this.submitted = true;
    Object.values(this.form.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach((c) => c.markAsTouched());
      }
    });
  }

  private markCurrentStepAsComplete(): void {
    this.steps[this.currentStep - 1].completed = true;
  }

  private updateStepsCompletion(): void {
    // Marquer toutes les étapes précédentes comme complétées
    for (let i = 0; i < this.currentStep - 1; i++) {
      this.steps[i].completed = true;
    }
  }

  onSubmit(): void {
    this.submitted = true;

    // Pour le design simplifié, on n'a pas de termsAgreed, donc on l'ignore
    if (this.form.invalid) {
      this.markAllAsTouched();
      this.toastService.error(
        'Formulaire invalide',
        'Veuillez corriger les erreurs dans le formulaire'
      );
      console.log('❌ Formulaire invalide:', this.getFormErrors());
      return;
    }

    this.isLoading = true;
    const formData = this.prepareFormData();

    // Logs détaillés pour vérifier les données
    console.log("=== DONNÉES COMPLÈTES DE L'ENTREPRISE ===");
    console.log(
      "Données de l'entreprise à envoyer:",
      JSON.stringify(formData, null, 2)
    );

    console.log('=== VÉRIFICATION DES DONNÉES ESSENTIELLES ===');
    console.log("✓ Nom de l'entreprise:", formData.companyName);
    console.log('✓ Email:', formData.email);
    console.log('✓ Téléphones:', formData.phoneNumbers);
    console.log('✓ Site web:', formData.website || 'Non renseigné');
    console.log('✓ Adresse:', formData.address);
    console.log('✓ Paramètres fiscaux (auto):', formData.taxSettings);
    console.log('✓ Configuration paie (auto):', formData.payrollConfiguration);
    console.log('✓ Nom officiel (auto):', formData.officialName);
    console.log('✓ Numéro fiscal (auto):', formData.taxIdentificationNumber);
    console.log('✓ Secteur (auto):', formData.industry);
    console.log('✓ Devise:', formData.currency);
    console.log('✓ Fuseau horaire:', formData.timezone);

    this.companyService.createCompany(formData).subscribe({
      next: () => {
        this.isLoading = false;
        console.log('✅ Entreprise créée avec succès !');
        console.log('✅ Toutes les données ont été envoyées correctement');
        this.toastService.success(
          'Entreprise créée avec succès',
          'Redirection vers le tableau de bord...'
        );
        this.router.navigate(['dashboard/overview']);
      },
      error: (err) => {
        this.isLoading = false;
        this.errorMessage = err.error?.message;
        console.log('❌ Erreur lors de la création:', err);
        // L'erreur sera gérée automatiquement par l'intercepteur, mais on peut ajouter un message spécifique
        if (this.errorMessage) {
          this.toastService.error('Erreur de création', this.errorMessage);
        }
      },
    });
  }

  get f() {
    return this.form.controls;
  }

  get address() {
    return this.getFormGroup('address').controls;
  }

  get taxSettings() {
    return this.getFormGroup('taxSettings').controls;
  }

  get payrollConfig() {
    return this.getFormGroup('payrollConfiguration').controls;
  }

  private prepareFormData(): any {
    const formValue = this.form.value;

    // Remplir automatiquement le nom officiel s'il n'est pas fourni
    if (!formValue.officialName) {
      formValue.officialName = formValue.companyName;
    }

    // Générer un numéro d'identification fiscale temporaire s'il n'est pas fourni
    if (!formValue.taxIdentificationNumber) {
      formValue.taxIdentificationNumber = 'TEMP_' + Date.now();
    }

    return {
      ...formValue,
      phoneNumbers: formValue.phoneNumbers.filter(
        (num: string) => num && num.trim() !== '+243'
      ),
      // S'assurer que tous les champs requis sont présents
      createdAt: new Date().toISOString(),
      status: 'ACTIVE',
      currency: 'CDF', // Franc congolais
      timezone: 'Africa/Kinshasa',
    };
  }

  private getFormErrors(): any {
    let formErrors: any = {};

    Object.keys(this.form.controls).forEach((key) => {
      const controlErrors = this.form.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });

    return formErrors;
  }
}
