/* ===== ANIMATIONS ULTRA-MODERNES ===== */
.animate-fade-in {
  animation: fadeInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Animation de pulsation pour les éléments actifs */
@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

/* Animation de brillance pour les boutons */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(200%) skewX(-15deg);
  }
}

/* ===== STYLES PREMIUM POUR LES FORMULAIRES ===== */
.group {
  position: relative;
  margin-bottom: 2rem;
}

/* Effet de focus ultra-sophistiqué */
input:focus,
select:focus,
textarea:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.2);
}

/* Effet de survol pour les champs */
input:hover:not(:focus),
select:hover:not(:focus),
textarea:hover:not(:focus) {
  transform: translateY(-0.5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

/* Styles pour les labels avec effet de typing */
label {
  position: relative;
  overflow: hidden;
}

label::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  transition: width 0.3s ease;
}

.group:focus-within label::after {
  width: 100%;
}

/* ===== BOUTONS PREMIUM AVEC EFFETS AVANCÉS ===== */
button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de brillance sur les boutons */
button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

button:hover::before {
  left: 100%;
}

/* ===== GLASSMORPHISM ET EFFETS VISUELS ===== */
.backdrop-blur-xl {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Effet de glassmorphism pour les cartes */
.bg-white\/80 {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== EFFETS DE SURVOL SOPHISTIQUÉS ===== */
.hover\:scale-105:hover {
  transform: scale(1.05) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de parallaxe subtil pour les éléments de fond */
.bg-gradient-to-br {
  background-attachment: fixed;
}

/* ===== STYLES POUR LES ÉTAPES ULTRA-MODERNES ===== */
.step-card {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.step-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.step-card:hover::after {
  opacity: 1;
}

/* Animation de pulsation pour l'étape active */
.animate-bounce {
  animation: bounce-glow 2s infinite;
}

@keyframes bounce-glow {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0) scale(1.05);
    box-shadow: 0 5px 25px rgba(59, 130, 246, 0.6);
  }
}

/* ===== EFFETS DE TEXTE PREMIUM ===== */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Effet de typing pour les titres */
.text-5xl {
  position: relative;
  overflow: hidden;
}

.text-5xl::after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #3b82f6, #6366f1);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* ===== SCROLLBARS ULTRA-MODERNES ===== */
.confirmation-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) rgba(241, 245, 249, 0.5);
}

.confirmation-scroll::-webkit-scrollbar {
  width: 6px;
}

.confirmation-scroll::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 10px;
}

.confirmation-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #6366f1);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.confirmation-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #4f46e5);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* ===== EFFETS DE PARTICULES ET ANIMATIONS ===== */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.animate-pulse {
  animation: pulse-premium 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-premium {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* ===== EFFETS DE PROGRESSION SOPHISTIQUÉS ===== */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer-progress 2s infinite;
}

@keyframes shimmer-progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ===== RESPONSIVE DESIGN ULTRA-MODERNE ===== */
.form-container {
  min-height: 60vh;
  position: relative;
}

/* Breakpoints sophistiqués */
@media (max-width: 1024px) {
  .text-5xl {
    font-size: 3rem;
    line-height: 1.1;
  }

  .max-w-6xl {
    max-width: 90%;
  }
}

@media (max-width: 768px) {
  .grid-cols-6 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .text-5xl {
    font-size: 2.5rem;
  }

  .p-8 {
    padding: 1.5rem;
  }

  .rounded-3xl {
    border-radius: 1.5rem;
  }
}

@media (max-width: 640px) {
  .grid-cols-6 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .text-5xl {
    font-size: 2rem;
  }

  .px-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .py-8 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

/* ===== EFFETS DE PERFORMANCE ===== */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.transform {
  transform: translateZ(0);
  will-change: transform;
}

/* Optimisation GPU */
.backdrop-blur-xl,
.bg-white\/80,
.shadow-2xl {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .bg-white\/80 {
    background: rgba(15, 23, 42, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .text-gray-900 {
    color: #f1f5f9;
  }

  .text-gray-600 {
    color: #cbd5e1;
  }
}
