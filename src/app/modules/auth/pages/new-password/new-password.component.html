<div class="w-full max-w-md mx-auto">
  <!-- En-tête -->
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Nouveau mot de passe</h1>
    <p class="text-gray-600">
      Entrez le code reçu et votre nouveau mot de passe
    </p>
    <p class="text-sm text-gray-500 mt-2">
      Déjà réinitialisé ?
      <a class="text-blue-600 hover:text-blue-700" routerLink="/auth/sign-in"
        >Se connecter</a
      >
    </p>
  </div>

  <!-- Messages d'erreur et de succès -->
  <div
    *ngIf="errorMessage"
    class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
  >
    <div class="flex items-center">
      <svg-icon
        src="assets/icons/heroicons/outline/exclamation-triangle.svg"
        class="w-5 h-5 text-red-500 mr-2"
      >
      </svg-icon>
      <p class="text-red-700 text-sm">{{ errorMessage }}</p>
    </div>
  </div>

  <div
    *ngIf="successMessage"
    class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
  >
    <div class="flex items-center">
      <svg-icon
        src="assets/icons/heroicons/outline/check-circle.svg"
        class="w-5 h-5 text-green-500 mr-2"
      >
      </svg-icon>
      <p class="text-green-700 text-sm">{{ successMessage }}</p>
    </div>
  </div>

  <!-- Formulaire -->
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Champ OTP -->
    <div>
      <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
        Code de vérification
      </label>
      <input
        id="otp"
        type="text"
        formControlName="otp"
        placeholder="Entrez le code à 6 chiffres"
        maxlength="6"
        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-center text-xl tracking-widest"
        [class.border-red-500]="submitted && f['otp'].errors"
      />
      <div *ngIf="submitted && f['otp'].errors" class="mt-2">
        <p *ngIf="f['otp'].errors?.['required']" class="text-red-600 text-sm">
          Le code de vérification est requis
        </p>
        <p *ngIf="f['otp'].errors?.['pattern']" class="text-red-600 text-sm">
          Le code doit contenir exactement 6 chiffres
        </p>
      </div>
    </div>

    <!-- Champ Nouveau mot de passe -->
    <div>
      <label
        for="newPassword"
        class="block text-sm font-medium text-gray-700 mb-2"
      >
        Nouveau mot de passe
      </label>
      <div class="relative">
        <input
          id="newPassword"
          [type]="showPassword ? 'text' : 'password'"
          formControlName="newPassword"
          placeholder="Entrez votre nouveau mot de passe"
          class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          [class.border-red-500]="submitted && f['newPassword'].errors"
        />
        <button
          type="button"
          (click)="togglePasswordVisibility()"
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
        >
          <svg-icon
            [src]="
              showPassword
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            class="h-5 w-5 text-gray-400 hover:text-gray-600"
          >
          </svg-icon>
        </button>
      </div>
      <div *ngIf="submitted && f['newPassword'].errors" class="mt-2">
        <p
          *ngIf="f['newPassword'].errors?.['required']"
          class="text-red-600 text-sm"
        >
          Le mot de passe est requis
        </p>
        <p
          *ngIf="f['newPassword'].errors?.['minlength']"
          class="text-red-600 text-sm"
        >
          Le mot de passe doit contenir au moins 8 caractères
        </p>
      </div>
      <p class="text-xs text-gray-500 mt-1">
        Utilisez 8 caractères ou plus avec un mélange de lettres, chiffres et
        symboles.
      </p>
    </div>

    <!-- Champ Confirmer mot de passe -->
    <div>
      <label
        for="confirmPassword"
        class="block text-sm font-medium text-gray-700 mb-2"
      >
        Confirmer le mot de passe
      </label>
      <div class="relative">
        <input
          id="confirmPassword"
          [type]="showConfirmPassword ? 'text' : 'password'"
          formControlName="confirmPassword"
          placeholder="Confirmez votre nouveau mot de passe"
          class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          [class.border-red-500]="submitted && f['confirmPassword'].errors"
        />
        <button
          type="button"
          (click)="toggleConfirmPasswordVisibility()"
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
        >
          <svg-icon
            [src]="
              showConfirmPassword
                ? 'assets/icons/heroicons/outline/eye-off.svg'
                : 'assets/icons/heroicons/outline/eye.svg'
            "
            class="h-5 w-5 text-gray-400 hover:text-gray-600"
          >
          </svg-icon>
        </button>
      </div>
      <div *ngIf="submitted && f['confirmPassword'].errors" class="mt-2">
        <p
          *ngIf="f['confirmPassword'].errors?.['required']"
          class="text-red-600 text-sm"
        >
          La confirmation du mot de passe est requise
        </p>
        <p
          *ngIf="f['confirmPassword'].errors?.['passwordMismatch']"
          class="text-red-600 text-sm"
        >
          Les mots de passe ne correspondent pas
        </p>
      </div>
    </div>

    <!-- Bouton de soumission -->
    <button
      type="submit"
      [disabled]="isLoading"
      class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
    >
      <svg
        *ngIf="isLoading"
        class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      {{ isLoading ? "Réinitialisation..." : "Réinitialiser le mot de passe" }}
    </button>
  </form>
</div>
