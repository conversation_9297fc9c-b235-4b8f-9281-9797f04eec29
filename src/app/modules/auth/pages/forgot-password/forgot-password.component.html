<div class="w-full max-w-md mx-auto">
  <!-- En-tête -->
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      Mot de passe oublié <span class="text-blue-600">?</span>
    </h1>
    <p class="text-gray-600">
      Entrez votre email pour recevoir un code de réinitialisation
    </p>
  </div>

  <!-- Messages d'erreur et de succès -->
  <div
    *ngIf="errorMessage"
    class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
  >
    <div class="flex items-center">
      <svg-icon
        src="assets/icons/heroicons/outline/exclamation-triangle.svg"
        class="w-5 h-5 text-red-500 mr-2"
      >
      </svg-icon>
      <p class="text-red-700 text-sm">{{ errorMessage }}</p>
    </div>
  </div>

  <div
    *ngIf="successMessage"
    class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
  >
    <div class="flex items-center">
      <svg-icon
        src="assets/icons/heroicons/outline/check-circle.svg"
        class="w-5 h-5 text-green-500 mr-2"
      >
      </svg-icon>
      <p class="text-green-700 text-sm">{{ successMessage }}</p>
    </div>
  </div>

  <!-- Formulaire -->
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Champ Email -->
    <div>
      <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
        Adresse email
      </label>
      <input
        id="email"
        type="email"
        formControlName="email"
        placeholder="<EMAIL>"
        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
        [class.border-red-500]="submitted && f['email'].errors"
      />
      <div *ngIf="submitted && f['email'].errors" class="mt-2">
        <p *ngIf="f['email'].errors?.['required']" class="text-red-600 text-sm">
          L'email est requis
        </p>
        <p *ngIf="f['email'].errors?.['email']" class="text-red-600 text-sm">
          Veuillez entrer un email valide
        </p>
      </div>
    </div>

    <!-- Boutons -->
    <div class="flex justify-between space-x-4">
      <button
        type="submit"
        [disabled]="isLoading"
        class="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
      >
        <svg
          *ngIf="isLoading"
          class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {{ isLoading ? "Envoi..." : "Envoyer" }}
      </button>

      <a
        routerLink="/auth/sign-in"
        class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 text-center"
      >
        Annuler
      </a>
    </div>
  </form>
</div>
