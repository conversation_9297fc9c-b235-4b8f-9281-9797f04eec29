<div class="w-full max-w-md mx-auto">
  <!-- En-tête -->
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      Vérification du compte
    </h1>
    <p class="text-gray-600">
      Entrez le code de vérification envoyé à votre email
    </p>
  </div>

  <!-- Messages d'erreur et de succès -->
  <div *ngIf="errorMessage" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <svg-icon 
        src="assets/icons/heroicons/outline/exclamation-triangle.svg" 
        class="w-5 h-5 text-red-500 mr-2">
      </svg-icon>
      <p class="text-red-700 text-sm">{{ errorMessage }}</p>
    </div>
  </div>

  <div *ngIf="successMessage" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
    <div class="flex items-center">
      <svg-icon 
        src="assets/icons/heroicons/outline/check-circle.svg" 
        class="w-5 h-5 text-green-500 mr-2">
      </svg-icon>
      <p class="text-green-700 text-sm">{{ successMessage }}</p>
    </div>
  </div>

  <!-- Formulaire de vérification -->
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Champ OTP -->
    <div>
      <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
        Code de vérification
      </label>
      <input
        id="otp"
        type="text"
        formControlName="otp"
        placeholder="Entrez le code à 6 chiffres"
        maxlength="6"
        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-center text-2xl tracking-widest"
        [class.border-red-500]="submitted && f['otp'].errors"
      />
      <div *ngIf="submitted && f['otp'].errors" class="mt-2">
        <p *ngIf="f['otp'].errors?.['required']" class="text-red-600 text-sm">
          Le code de vérification est requis
        </p>
        <p *ngIf="f['otp'].errors?.['pattern']" class="text-red-600 text-sm">
          Le code doit contenir exactement 6 chiffres
        </p>
      </div>
    </div>

    <!-- Bouton de soumission -->
    <button
      type="submit"
      [disabled]="isLoading"
      class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
    >
      <svg *ngIf="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      {{ isLoading ? 'Vérification...' : 'Vérifier le compte' }}
    </button>

    <!-- Lien pour renvoyer le code -->
    <div class="text-center">
      <button
        type="button"
        (click)="resendOtp()"
        class="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors duration-200"
      >
        Renvoyer le code de vérification
      </button>
    </div>

    <!-- Lien de retour -->
    <div class="text-center">
      <a
        routerLink="/auth/sign-in"
        class="text-gray-600 hover:text-gray-700 text-sm transition-colors duration-200"
      >
        Retour à la connexion
      </a>
    </div>
  </form>
</div>
