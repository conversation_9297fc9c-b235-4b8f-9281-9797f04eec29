/* Styles pour le composant d'authentification - Style Twenty */

/* Animations pour le style Twenty */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Classes d'animation */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideInFromBottom 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Effet glassmorphism pour les conteneurs */
.glass-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Effet de survol pour les boutons */
.btn-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Effet de focus pour les inputs */
.input-focus-effect {
  transition: all 0.3s ease;
}

.input-focus-effect:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.15);
}

/* Responsive */
@media (max-width: 768px) {
  .glass-container {
    margin: 1rem;
    padding: 1.5rem;
  }
}

/* Effet de parallaxe pour l'arrière-plan */
.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

@media (max-width: 768px) {
  .parallax-bg {
    background-attachment: scroll;
  }
}

/* Styles spécifiques pour les formulaires Twenty */
.twenty-form {
  .form-input {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    
    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }
    
    &:focus {
      outline: none;
      ring: 2px;
      ring-color: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.4);
    }
  }
  
  .form-button {
    background: white;
    color: #1f2937;
    font-weight: 600;
    
    &:hover {
      background: rgba(255, 255, 255, 0.9);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .form-button-secondary {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

/* Styles pour les indicateurs d'étapes */
.step-indicator {
  .step-item {
    transition: all 0.3s ease;
    
    &.active {
      .step-circle {
        background: white;
        border-color: white;
        color: #1f2937;
      }
      
      .step-label {
        color: white;
      }
    }
    
    &.completed {
      .step-circle {
        background: white;
        border-color: white;
        color: #1f2937;
      }
    }
    
    &.inactive {
      .step-circle {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.6);
      }
      
      .step-label {
        color: rgba(255, 255, 255, 0.7);
      }
    }
    
    &:hover {
      .step-label {
        color: white;
      }
    }
  }
  
  .progress-bar {
    background: rgba(255, 255, 255, 0.2);
    
    .progress-fill {
      background: white;
      transition: width 0.5s ease-out;
    }
  }
}

/* Styles pour les messages d'erreur */
.error-message {
  color: #fca5a5;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Styles pour les liens */
.auth-link {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
  
  &:hover {
    color: rgba(255, 255, 255, 0.9);
  }
}

/* Styles pour les checkboxes */
.auth-checkbox {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:checked {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  &:focus {
    ring: 2px;
    ring-color: rgba(255, 255, 255, 0.3);
  }
}

/* Styles pour les selects */
.auth-select {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  
  &:focus {
    outline: none;
    ring: 2px;
    ring-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
  }
  
  option {
    background: #1f2937;
    color: white;
  }
}

/* Styles pour les spinners de chargement */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
