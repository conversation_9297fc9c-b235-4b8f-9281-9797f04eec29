import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import {
  PerformanceService,
  PerformanceStatistics,
  PerformanceReviewStatus,
  GoalStatus,
} from '../../../../../core/services/performance/performance.service';

@Component({
  selector: 'app-performance-statistics',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './performance-statistics.component.html',
})
export class PerformanceStatisticsComponent implements OnInit, OnDestroy {
  statistics: PerformanceStatistics | null = null;
  isLoading = false;
  error: string | null = null;

  // Exposer Math pour le template
  Math = Math;

  private subscriptions = new Subscription();

  constructor(private performanceService: PerformanceService) {}

  ngOnInit(): void {
    this.loadStatistics();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadStatistics(): void {
    this.isLoading = true;
    this.error = null;

    this.subscriptions.add(
      this.performanceService.getStatistics().subscribe({
        next: (stats) => {
          this.statistics = stats;
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des statistiques';
          this.isLoading = false;
          console.error('Erreur:', error);
        },
      })
    );
  }

  // Méthodes utilitaires pour les calculs
  getCompletionRate(): number {
    if (!this.statistics || this.statistics.totalReviews === 0) return 0;
    const stats = this.statistics;
    if (!stats.completedReviews || !stats.totalReviews) return 0;
    return Math.round((stats.completedReviews / stats.totalReviews) * 100);
  }

  getGoalCompletionRate(): number {
    if (!this.statistics || this.statistics.totalGoals === 0) return 0;
    const stats = this.statistics;
    if (!stats.completedGoals || !stats.totalGoals) return 0;
    return Math.round((stats.completedGoals / stats.totalGoals) * 100);
  }

  getOverdueRate(): number {
    if (!this.statistics || this.statistics.totalGoals === 0) return 0;
    const stats = this.statistics;
    if (!stats.overDueGoals || !stats.totalGoals) return 0;
    return Math.round((stats.overDueGoals / stats.totalGoals) * 100);
  }

  getReviewStatusLabel(status: PerformanceReviewStatus): string {
    const labels: { [key in PerformanceReviewStatus]: string } = {
      [PerformanceReviewStatus.DRAFT]: 'Brouillon',
      [PerformanceReviewStatus.IN_PROGRESS]: 'En cours',
      [PerformanceReviewStatus.COMPLETED]: 'Terminé',
      [PerformanceReviewStatus.APPROVED]: 'Approuvé',
      [PerformanceReviewStatus.REJECTED]: 'Rejeté',
    };
    return labels[status];
  }

  getGoalStatusLabel(status: GoalStatus): string {
    const labels: { [key in GoalStatus]: string } = {
      [GoalStatus.NOT_STARTED]: 'Non commencé',
      [GoalStatus.IN_PROGRESS]: 'En cours',
      [GoalStatus.COMPLETED]: 'Terminé',
      [GoalStatus.OVERDUE]: 'En retard',
      [GoalStatus.CANCELLED]: 'Annulé',
    };
    return labels[status];
  }

  getStatusColor(status: string): string {
    switch (status) {
      case PerformanceReviewStatus.COMPLETED:
      case PerformanceReviewStatus.APPROVED:
      case GoalStatus.COMPLETED:
        return 'text-green-600';
      case PerformanceReviewStatus.IN_PROGRESS:
      case GoalStatus.IN_PROGRESS:
        return 'text-blue-600';
      case PerformanceReviewStatus.REJECTED:
      case GoalStatus.OVERDUE:
        return 'text-red-600';
      case PerformanceReviewStatus.DRAFT:
      case GoalStatus.NOT_STARTED:
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  }

  getProgressBarColor(percentage: number): string {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-blue-500';
    if (percentage >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  }

  refreshStatistics(): void {
    this.loadStatistics();
  }
}
