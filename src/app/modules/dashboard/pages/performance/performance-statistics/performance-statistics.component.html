<div class="p-6">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Statistiques de performance
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Analyse des performances et objectifs de votre équipe
        </p>
      </div>
      <button
        (click)="refreshStatistics()"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
        Actualiser
      </button>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
  >
    {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
  </div>

  <!-- Statistiques -->
  <div *ngIf="!isLoading && statistics" class="space-y-6">
    <!-- Cartes de résumé -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total évaluations -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-blue-600 dark:text-blue-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Total évaluations
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ statistics.totalReviews }}
            </p>
          </div>
        </div>
      </div>

      <!-- Évaluations terminées -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Évaluations terminées
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ statistics.completedReviews }}
            </p>
            <p class="text-sm text-green-600 dark:text-green-400">
              {{ getCompletionRate() }}% du total
            </p>
          </div>
        </div>
      </div>

      <!-- Total objectifs -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-purple-600 dark:text-purple-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Total objectifs
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ statistics.totalGoals }}
            </p>
          </div>
        </div>
      </div>

      <!-- Score moyen -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-yellow-600 dark:text-yellow-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Score moyen
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ statistics.averageScore }}/100
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Graphiques et détails -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Répartition des évaluations par statut -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Évaluations par statut
        </h3>
        <div class="space-y-3">
          <div
            *ngFor="let item of statistics.reviewsByStatus"
            class="flex items-center justify-between"
          >
            <div class="flex items-center">
              <div
                [class]="
                  'w-3 h-3 rounded-full mr-3 ' + getStatusColor(item.status)
                "
              ></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{
                getReviewStatusLabel(item.status)
              }}</span>
            </div>
            <div class="flex items-center">
              <span
                class="text-sm font-medium text-gray-900 dark:text-white mr-2"
                >{{ item.count }}</span
              >
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  [class]="
                    'h-2 rounded-full ' +
                    getProgressBarColor(
                      (item.count / statistics.totalReviews) * 100
                    )
                  "
                  [style.width.%]="(item.count / statistics.totalReviews) * 100"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Répartition des objectifs par statut -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Objectifs par statut
        </h3>
        <div class="space-y-3">
          <div
            *ngFor="let item of statistics.goalsByStatus"
            class="flex items-center justify-between"
          >
            <div class="flex items-center">
              <div
                [class]="
                  'w-3 h-3 rounded-full mr-3 ' + getStatusColor(item.status)
                "
              ></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{
                getGoalStatusLabel(item.status)
              }}</span>
            </div>
            <div class="flex items-center">
              <span
                class="text-sm font-medium text-gray-900 dark:text-white mr-2"
                >{{ item.count }}</span
              >
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  [class]="
                    'h-2 rounded-full ' +
                    getProgressBarColor(
                      (item.count / (statistics?.totalGoals || 1)) * 100
                    )
                  "
                  [style.width.%]="
                    (item.count / (statistics?.totalGoals || 1)) * 100
                  "
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Métriques de productivité -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Métriques de productivité
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Taux de completion des objectifs -->
        <div class="text-center">
          <div
            class="relative inline-flex items-center justify-center w-24 h-24"
          >
            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                stroke-width="8"
                fill="transparent"
                class="text-gray-200 dark:text-gray-700"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                stroke-width="8"
                fill="transparent"
                [class]="getProgressBarColor(getGoalCompletionRate())"
                stroke-linecap="round"
                [attr.stroke-dasharray]="2 * Math.PI * 40"
                [attr.stroke-dashoffset]="
                  2 * Math.PI * 40 * (1 - getGoalCompletionRate() / 100)
                "
              />
            </svg>
            <span
              class="absolute text-lg font-semibold text-gray-900 dark:text-white"
              >{{ getGoalCompletionRate() }}%</span
            >
          </div>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Objectifs terminés
          </p>
        </div>

        <!-- Taux d'évaluations en retard -->
        <div class="text-center">
          <div
            class="relative inline-flex items-center justify-center w-24 h-24"
          >
            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                stroke-width="8"
                fill="transparent"
                class="text-gray-200 dark:text-gray-700"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                stroke-width="8"
                fill="transparent"
                class="text-red-500"
                stroke-linecap="round"
                [attr.stroke-dasharray]="2 * Math.PI * 40"
                [attr.stroke-dashoffset]="
                  2 * Math.PI * 40 * (1 - getOverdueRate() / 100)
                "
              />
            </svg>
            <span
              class="absolute text-lg font-semibold text-gray-900 dark:text-white"
              >{{ getOverdueRate() }}%</span
            >
          </div>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Objectifs en retard
          </p>
        </div>

        <!-- Moyenne de completion -->
        <div class="text-center">
          <div
            class="relative inline-flex items-center justify-center w-24 h-24"
          >
            <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                stroke-width="8"
                fill="transparent"
                class="text-gray-200 dark:text-gray-700"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                stroke-width="8"
                fill="transparent"
                [class]="
                  getProgressBarColor(statistics?.averageGoalCompletion || 0)
                "
                stroke-linecap="round"
                [attr.stroke-dasharray]="2 * Math.PI * 40"
                [attr.stroke-dashoffset]="
                  2 *
                  Math.PI *
                  40 *
                  (1 - (statistics?.averageGoalCompletion || 0) / 100)
                "
              />
            </svg>
            <span
              class="absolute text-lg font-semibold text-gray-900 dark:text-white"
              >{{ statistics.averageGoalCompletion }}%</span
            >
          </div>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Progression moyenne
          </p>
        </div>
      </div>
    </div>

    <!-- Résumé des tendances -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Résumé des tendances
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Évaluations
          </h4>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• {{ statistics.pendingReviews }} évaluations en attente</li>
            <li>• {{ getCompletionRate() }}% de taux de completion</li>
            <li>• Score moyen de {{ statistics.averageScore }}/100</li>
          </ul>
        </div>
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Objectifs
          </h4>
          <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li>• {{ statistics.completedGoals }} objectifs terminés</li>
            <li>• {{ statistics.overDueGoals }} objectifs en retard</li>
            <li>
              • {{ statistics.averageGoalCompletion }}% de progression moyenne
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Message si pas de données -->
  <div *ngIf="!isLoading && !statistics" class="text-center py-8">
    <svg
      class="mx-auto h-12 w-12 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
      ></path>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
      Aucune donnée disponible
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Les statistiques apparaîtront une fois que vous aurez des évaluations et
      objectifs.
    </p>
  </div>
</div>
