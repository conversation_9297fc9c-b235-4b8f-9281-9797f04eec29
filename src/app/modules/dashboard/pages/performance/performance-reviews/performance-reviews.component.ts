import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import {
  PerformanceService,
  PerformanceReview,
  PerformanceReviewStatus,
  PerformanceReviewType,
  PerformanceQueryOptions,
} from '../../../../../core/services/performance/performance.service';
import { EmployeeService } from '../../../../../core/services/employee/employee.service';
import { EmployeeData } from '../../../models/employee';
import { PaginatedResult } from '../../../../../core/models/employee.model';

@Component({
  selector: 'app-performance-reviews',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './performance-reviews.component.html',
})
export class PerformanceReviewsComponent implements OnInit, OnDestroy {
  reviews: PerformanceReview[] = [];
  filteredReviews: PerformanceReview[] = [];
  employees: EmployeeData[] = [];

  // Filtres
  filterForm: FormGroup;
  searchTerm = '';

  // Modal
  showCreateModal = false;
  showDetailsModal = false;
  selectedReview: PerformanceReview | null = null;

  // États
  isLoading = false;
  error: string | null = null;

  // Énumérations
  reviewStatuses = Object.values(PerformanceReviewStatus);
  reviewTypes = Object.values(PerformanceReviewType);

  private subscriptions = new Subscription();

  constructor(
    private performanceService: PerformanceService,
    private employeeService: EmployeeService,
    private fb: FormBuilder,
    private router: Router
  ) {
    this.filterForm = this.fb.group({
      status: [''],
      reviewType: [''],
      employeeId: [''],
      reviewerId: [''],
      dateRange: [''],
    });
  }

  ngOnInit(): void {
    this.loadEmployees();
    this.loadReviews();
    this.setupFilterSubscription();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadEmployees(): void {
    // TODO: Récupérer le companyId depuis le store
    const companyId = 'current-company-id';
    this.subscriptions.add(
      this.employeeService.getEmployees(companyId).subscribe({
        next: (employees: PaginatedResult<any>) => {
          this.employees = employees.data;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des employés:', error);
        },
      })
    );
  }

  private loadReviews(): void {
    this.isLoading = true;
    this.error = null;

    const options: PerformanceQueryOptions = this.buildQueryOptions();

    this.subscriptions.add(
      this.performanceService.getReviews(options).subscribe({
        next: (reviews) => {
          this.reviews = reviews;
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des évaluations';
          this.isLoading = false;
          console.error('Erreur:', error);
        },
      })
    );
  }

  private buildQueryOptions(): PerformanceQueryOptions {
    const formValue = this.filterForm.value;
    const options: PerformanceQueryOptions = {};

    if (formValue.status) options.status = formValue.status;
    if (formValue.reviewType) options.reviewType = formValue.reviewType;
    if (formValue.employeeId) options.employeeId = formValue.employeeId;
    if (formValue.reviewerId) options.reviewerId = formValue.reviewerId;

    return options;
  }

  private setupFilterSubscription(): void {
    this.subscriptions.add(
      this.filterForm.valueChanges.subscribe(() => {
        this.applyFilters();
      })
    );
  }

  applyFilters(): void {
    let filtered = [...this.reviews];

    // Filtre par terme de recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (review) =>
          review.title.toLowerCase().includes(term) ||
          this.getEmployeeName(review.employeeId)
            .toLowerCase()
            .includes(term) ||
          this.getReviewerName(review.reviewerId).toLowerCase().includes(term)
      );
    }

    this.filteredReviews = filtered;
  }

  // Actions
  createReview(): void {
    this.showCreateModal = true;
  }

  editReview(review: PerformanceReview): void {
    this.router.navigate(['/dashboard/performance/reviews/edit', review.id]);
  }

  viewReview(review: PerformanceReview): void {
    this.selectedReview = review;
    this.showDetailsModal = true;
  }

  deleteReview(review: PerformanceReview): void {
    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer l'évaluation "${review.title}" ?`
      )
    ) {
      this.subscriptions.add(
        this.performanceService.deleteReview(review.id).subscribe({
          next: () => {
            this.loadReviews();
          },
          error: (error) => {
            this.error = 'Erreur lors de la suppression';
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  submitReview(review: PerformanceReview): void {
    this.subscriptions.add(
      this.performanceService.submitReview(review.id).subscribe({
        next: (updatedReview) => {
          const index = this.reviews.findIndex((r) => r.id === review.id);
          if (index !== -1) {
            this.reviews[index] = updatedReview;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la soumission';
          console.error('Erreur:', error);
        },
      })
    );
  }

  approveReview(review: PerformanceReview): void {
    const comments = prompt("Commentaires d'approbation (optionnel):");
    this.subscriptions.add(
      this.performanceService
        .approveReview(review.id, comments || undefined)
        .subscribe({
          next: (updatedReview) => {
            const index = this.reviews.findIndex((r) => r.id === review.id);
            if (index !== -1) {
              this.reviews[index] = updatedReview;
              this.applyFilters();
            }
          },
          error: (error) => {
            this.error = "Erreur lors de l'approbation";
            console.error('Erreur:', error);
          },
        })
    );
  }

  rejectReview(review: PerformanceReview): void {
    const comments = prompt('Raison du rejet:');
    if (comments) {
      this.subscriptions.add(
        this.performanceService.rejectReview(review.id, comments).subscribe({
          next: (updatedReview) => {
            const index = this.reviews.findIndex((r) => r.id === review.id);
            if (index !== -1) {
              this.reviews[index] = updatedReview;
              this.applyFilters();
            }
          },
          error: (error) => {
            this.error = 'Erreur lors du rejet';
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  // Modals
  closeCreateModal(): void {
    this.showCreateModal = false;
  }

  closeDetailsModal(): void {
    this.showDetailsModal = false;
    this.selectedReview = null;
  }

  onReviewCreated(): void {
    this.closeCreateModal();
    this.loadReviews();
  }

  // Filtres
  resetFilters(): void {
    this.filterForm.reset();
    this.searchTerm = '';
    this.applyFilters();
  }

  // Méthodes utilitaires
  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employé #${employeeId}`;
  }

  getReviewerName(reviewerId: string): string {
    const reviewer = this.employees.find((emp) => emp.id === reviewerId);
    return reviewer
      ? `${reviewer.user?.profile?.firstName} ${reviewer.user?.profile?.lastName}`
      : `Évaluateur #${reviewerId}`;
  }

  getStatusLabel(status: PerformanceReviewStatus): string {
    const labels: { [key in PerformanceReviewStatus]: string } = {
      [PerformanceReviewStatus.DRAFT]: 'Brouillon',
      [PerformanceReviewStatus.IN_PROGRESS]: 'En cours',
      [PerformanceReviewStatus.COMPLETED]: 'Terminé',
      [PerformanceReviewStatus.APPROVED]: 'Approuvé',
      [PerformanceReviewStatus.REJECTED]: 'Rejeté',
    };
    return labels[status];
  }

  getStatusClasses(status: PerformanceReviewStatus): string {
    const classes: { [key in PerformanceReviewStatus]: string } = {
      [PerformanceReviewStatus.DRAFT]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [PerformanceReviewStatus.IN_PROGRESS]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [PerformanceReviewStatus.COMPLETED]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [PerformanceReviewStatus.APPROVED]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [PerformanceReviewStatus.REJECTED]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    };
    return classes[status];
  }

  getTypeLabel(type: PerformanceReviewType): string {
    const labels: { [key in PerformanceReviewType]: string } = {
      [PerformanceReviewType.ANNUAL]: 'Annuelle',
      [PerformanceReviewType.QUARTERLY]: 'Trimestrielle',
      [PerformanceReviewType.MONTHLY]: 'Mensuelle',
      [PerformanceReviewType.PROJECT_BASED]: 'Par projet',
      [PerformanceReviewType.PROBATION]: "Période d'essai",
      [PerformanceReviewType.PROMOTION]: 'Promotion',
    };
    return labels[type];
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  isOverdue(review: PerformanceReview): boolean {
    return (
      new Date(review.dueDate) < new Date() &&
      review.status !== PerformanceReviewStatus.COMPLETED &&
      review.status !== PerformanceReviewStatus.APPROVED
    );
  }

  canEdit(review: PerformanceReview): boolean {
    return (
      review.status === PerformanceReviewStatus.DRAFT ||
      review.status === PerformanceReviewStatus.IN_PROGRESS
    );
  }

  canSubmit(review: PerformanceReview): boolean {
    return (
      review.status === PerformanceReviewStatus.DRAFT ||
      review.status === PerformanceReviewStatus.IN_PROGRESS
    );
  }

  canApprove(review: PerformanceReview): boolean {
    return review.status === PerformanceReviewStatus.COMPLETED;
  }
}
