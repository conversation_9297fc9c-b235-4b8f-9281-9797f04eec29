import { Routes } from '@angular/router';

export const PERFORMANCE_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'reviews',
    pathMatch: 'full',
  },
  {
    path: 'reviews',
    loadComponent: () =>
      import('./performance-reviews/performance-reviews.component').then(
        (m) => m.PerformanceReviewsComponent
      ),
    data: {
      title: 'Évaluations de performance',
      description: 'Gestion des évaluations de performance des employés',
    },
  },
  {
    path: 'goals',
    loadComponent: () =>
      import('./performance-goals/performance-goals.component').then(
        (m) => m.PerformanceGoalsComponent
      ),
    data: {
      title: 'Objectifs',
      description: 'Gestion des objectifs des employés',
    },
  },
  {
    path: 'statistics',
    loadComponent: () =>
      import('./performance-statistics/performance-statistics.component').then(
        (m) => m.PerformanceStatisticsComponent
      ),
    data: {
      title: 'Statistiques de performance',
      description: 'Analyse et statistiques de performance',
    },
  },
];
