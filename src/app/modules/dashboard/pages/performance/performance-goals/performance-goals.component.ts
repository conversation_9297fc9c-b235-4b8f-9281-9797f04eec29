import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  PerformanceService,
  Goal,
  GoalStatus,
  GoalPriority,
  CreateGoalDto,
  UpdateGoalDto,
  GoalQueryOptions,
} from '../../../../../core/services/performance/performance.service';
import { EmployeeService } from '../../../../../core/services/employee/employee.service';
import { EmployeeData } from '../../../models/employee';
import { PaginatedResult } from '../../../../../core/models/employee.model';

@Component({
  selector: 'app-performance-goals',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './performance-goals.component.html',
})
export class PerformanceGoalsComponent implements OnInit, OnDestroy {
  goals: Goal[] = [];
  filteredGoals: Goal[] = [];
  employees: EmployeeData[] = [];

  // Filtres
  filterForm: FormGroup;
  searchTerm = '';

  // Modal
  showCreateModal = false;
  showEditModal = false;
  selectedGoal: Goal | null = null;
  goalForm: FormGroup;

  // États
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;

  // Énumérations
  goalStatuses = Object.values(GoalStatus);
  goalPriorities = Object.values(GoalPriority);

  private subscriptions = new Subscription();

  constructor(
    private performanceService: PerformanceService,
    private employeeService: EmployeeService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      status: [''],
      priority: [''],
      employeeId: [''],
      assignedById: [''],
      category: [''],
    });

    this.goalForm = this.fb.group({
      title: ['', Validators.required],
      description: [''],
      priority: [GoalPriority.MEDIUM, Validators.required],
      targetDate: ['', Validators.required],
      employeeId: ['', Validators.required],
      assignedById: ['', Validators.required],
      category: [''],
      metrics: [[]],
    });
  }

  ngOnInit(): void {
    this.loadEmployees();
    this.loadGoals();
    this.setupFilterSubscription();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadEmployees(): void {
    // TODO: Récupérer le companyId depuis le store
    const companyId = 'current-company-id';
    this.subscriptions.add(
      this.employeeService.getEmployees(companyId).subscribe({
        next: (employees: PaginatedResult<any>) => {
          this.employees = employees.data;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des employés:', error);
        },
      })
    );
  }

  private loadGoals(): void {
    this.isLoading = true;
    this.error = null;

    const options: GoalQueryOptions = this.buildQueryOptions();

    this.subscriptions.add(
      this.performanceService.getGoals(options).subscribe({
        next: (goals) => {
          this.goals = goals;
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des objectifs';
          this.isLoading = false;
          console.error('Erreur:', error);
        },
      })
    );
  }

  private buildQueryOptions(): GoalQueryOptions {
    const formValue = this.filterForm.value;
    const options: GoalQueryOptions = {};

    if (formValue.status) options.status = formValue.status;
    if (formValue.priority) options.priority = formValue.priority;
    if (formValue.employeeId) options.employeeId = formValue.employeeId;
    if (formValue.assignedById) options.assignedById = formValue.assignedById;
    if (formValue.category) options.category = formValue.category;

    return options;
  }

  private setupFilterSubscription(): void {
    this.subscriptions.add(
      this.filterForm.valueChanges.subscribe(() => {
        this.applyFilters();
      })
    );
  }

  private applyFilters(): void {
    let filtered = [...this.goals];

    // Filtre par terme de recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (goal) =>
          goal.title.toLowerCase().includes(term) ||
          (goal.description && goal.description.toLowerCase().includes(term)) ||
          this.getEmployeeName(goal.employeeId).toLowerCase().includes(term)
      );
    }

    this.filteredGoals = filtered;
  }

  // Actions CRUD
  createGoal(): void {
    this.selectedGoal = null;
    this.goalForm.reset({
      priority: GoalPriority.MEDIUM,
    });
    this.showCreateModal = true;
  }

  editGoal(goal: Goal): void {
    this.selectedGoal = goal;
    this.goalForm.patchValue({
      title: goal.title,
      description: goal.description,
      priority: goal.priority,
      targetDate: this.formatDateForInput(goal.targetDate),
      employeeId: goal.employeeId,
      assignedById: goal.assignedById,
      category: goal.category,
      metrics: goal.metrics || [],
    });
    this.showEditModal = true;
  }

  deleteGoal(goal: Goal): void {
    if (
      confirm(`Êtes-vous sûr de vouloir supprimer l'objectif "${goal.title}" ?`)
    ) {
      this.subscriptions.add(
        this.performanceService.deleteGoal(goal.id).subscribe({
          next: () => {
            this.loadGoals();
          },
          error: (error) => {
            this.error = 'Erreur lors de la suppression';
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  onSubmitGoal(): void {
    if (this.goalForm.valid) {
      this.isSubmitting = true;
      const formData = this.goalForm.value;

      const goalData: CreateGoalDto | UpdateGoalDto = {
        title: formData.title,
        description: formData.description,
        priority: formData.priority,
        targetDate: formData.targetDate,
        employeeId: formData.employeeId,
        assignedById: formData.assignedById,
        category: formData.category,
        metrics: formData.metrics,
      };

      const operation = this.selectedGoal
        ? this.performanceService.updateGoal(
            this.selectedGoal.id,
            goalData as UpdateGoalDto
          )
        : this.performanceService.createGoal(goalData as CreateGoalDto);

      this.subscriptions.add(
        operation.subscribe({
          next: () => {
            this.closeModals();
            this.loadGoals();
            this.isSubmitting = false;
          },
          error: (error) => {
            this.error = 'Erreur lors de la sauvegarde';
            this.isSubmitting = false;
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  updateProgress(goal: Goal, progress: number): void {
    this.subscriptions.add(
      this.performanceService.updateGoalProgress(goal.id, progress).subscribe({
        next: (updatedGoal) => {
          const index = this.goals.findIndex((g) => g.id === goal.id);
          if (index !== -1) {
            this.goals[index] = updatedGoal;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la mise à jour du progrès';
          console.error('Erreur:', error);
        },
      })
    );
  }

  // Modals
  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.selectedGoal = null;
    this.goalForm.reset();
  }

  // Filtres
  resetFilters(): void {
    this.filterForm.reset();
    this.searchTerm = '';
    this.applyFilters();
  }

  // Méthodes utilitaires
  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employé #${employeeId}`;
  }

  getStatusLabel(status: GoalStatus): string {
    const labels: { [key in GoalStatus]: string } = {
      [GoalStatus.NOT_STARTED]: 'Non commencé',
      [GoalStatus.IN_PROGRESS]: 'En cours',
      [GoalStatus.COMPLETED]: 'Terminé',
      [GoalStatus.OVERDUE]: 'En retard',
      [GoalStatus.CANCELLED]: 'Annulé',
    };
    return labels[status];
  }

  getStatusClasses(status: GoalStatus): string {
    const classes: { [key in GoalStatus]: string } = {
      [GoalStatus.NOT_STARTED]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [GoalStatus.IN_PROGRESS]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [GoalStatus.COMPLETED]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [GoalStatus.OVERDUE]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [GoalStatus.CANCELLED]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
    };
    return classes[status];
  }

  getPriorityLabel(priority: GoalPriority): string {
    const labels: { [key in GoalPriority]: string } = {
      [GoalPriority.LOW]: 'Faible',
      [GoalPriority.MEDIUM]: 'Moyenne',
      [GoalPriority.HIGH]: 'Élevée',
      [GoalPriority.CRITICAL]: 'Critique',
    };
    return labels[priority];
  }

  getPriorityClasses(priority: GoalPriority): string {
    const classes: { [key in GoalPriority]: string } = {
      [GoalPriority.LOW]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [GoalPriority.MEDIUM]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [GoalPriority.HIGH]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      [GoalPriority.CRITICAL]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    };
    return classes[priority];
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  formatDateForInput(date: string): string {
    return new Date(date).toISOString().split('T')[0];
  }

  isOverdue(goal: Goal): boolean {
    return (
      new Date(goal.targetDate) < new Date() &&
      goal.status !== GoalStatus.COMPLETED &&
      goal.status !== GoalStatus.CANCELLED
    );
  }

  getProgressColor(progress: number): string {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  }

  onProgressChange(goal: Goal, event: any): void {
    const progress = parseInt(event.target.value);
    if (progress !== goal.progress) {
      this.updateProgress(goal, progress);
    }
  }
}
