<div
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4 transform transition-transform"
  >
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold text-gray-800 dark:text-white">
        {{ isEditing ? "Modifier le département" : "Créer un département" }}
      </h3>
      <button
        (click)="closeModal()"
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <form [formGroup]="departmentForm" (ngSubmit)="saveDepartment()">
      <div class="grid grid-cols-1 gap-6">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1"
            >Nom du département *</label
          >
          <input
            id="name"
            type="text"
            formControlName="name"
            class="w-full px-3 py-2 border rounded-md shadow-sm focus:ring-2 focus:ring-blue-500"
            placeholder="Entrez le nom du département"
          />
          <div
            *ngIf="
              departmentForm.get('name')?.invalid &&
              departmentForm.get('name')?.touched
            "
            class="text-red-500 text-xs mt-1"
          >
            <span *ngIf="departmentForm.get('name')?.errors?.['required']">
              Le nom du département est requis
            </span>
            <span *ngIf="departmentForm.get('name')?.errors?.['minlength']">
              Le nom doit contenir au moins 3 caractères
            </span>
          </div>
        </div>

        <div>
          <label
            for="description"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Description</label
          >
          <textarea
            id="description"
            formControlName="description"
            rows="3"
            class="w-full px-3 py-2 border rounded-md shadow-sm focus:ring-2 focus:ring-blue-500"
            placeholder="Description du département (optionnel)"
          ></textarea>
        </div>
      </div>

      <div class="flex justify-end space-x-2 mt-6">
        <button
          type="button"
          (click)="closeModal()"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
        >
          Annuler
        </button>
        <button
          type="submit"
          [disabled]="departmentForm.invalid"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 dark:bg-blue-700 dark:hover:bg-blue-800"
        >
          {{ isEditing ? "Modifier" : "Créer" }}
        </button>
      </div>
    </form>
  </div>
</div>
