import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DepartmentResponseDto } from 'src/app/core/models/company.model';

@Component({
  selector: 'app-department-create-modal',
  templateUrl: './department-create-modal.component.html',
  imports: [CommonModule, ReactiveFormsModule],
  standalone: true,
})
export class DepartmentCreateModalComponent implements OnInit {
  @Input() isEditing = false;
  @Input() existingDepartment: DepartmentResponseDto | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<{
    name: string;
    description?: string;
  }>();

  departmentForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.departmentForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
    });

    if (this.existingDepartment && this.isEditing) {
      this.departmentForm.patchValue({
        name: this.existingDepartment.name,
        description: this.existingDepartment.description,
      });
    }
  }

  closeModal(): void {
    this.close.emit();
  }

  saveDepartment(): void {
    if (this.departmentForm.valid) {
      const formValue = this.departmentForm.value;
      this.save.emit({
        name: formValue.name,
        description: formValue.description,
      });
      this.closeModal();
    }
  }
}
