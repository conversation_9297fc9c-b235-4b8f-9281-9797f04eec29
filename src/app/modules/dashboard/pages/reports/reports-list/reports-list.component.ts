import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { 
  ReportsService, 
  Report,
  ReportTemplate,
  ReportType,
  ReportStatus,
  ReportFormat,
  CreateReportDto,
  ReportQueryOptions
} from '../../../../../core/services/reports/reports.service';

@Component({
  selector: 'app-reports-list',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './reports-list.component.html'
})
export class ReportsListComponent implements OnInit, OnDestroy {
  reports: Report[] = [];
  filteredReports: Report[] = [];
  templates: ReportTemplate[] = [];
  
  // Filtres
  filterForm: FormGroup;
  searchTerm = '';
  
  // Modal
  showCreateModal = false;
  reportForm: FormGroup;
  
  // États
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;
  
  // Énumérations
  reportTypes = Object.values(ReportType);
  reportStatuses = Object.values(ReportStatus);
  reportFormats = Object.values(ReportFormat);
  
  private subscriptions = new Subscription();

  constructor(
    private reportsService: ReportsService,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      reportType: [''],
      status: [''],
      format: ['']
    });

    this.reportForm = this.fb.group({
      name: ['', Validators.required],
      description: [''],
      reportType: [ReportType.EMPLOYEE_REPORT, Validators.required],
      format: [ReportFormat.PDF, Validators.required],
      parameters: this.fb.group({
        dateRange: this.fb.group({
          start: ['', Validators.required],
          end: ['', Validators.required]
        }),
        includeInactive: [false],
        departmentIds: [[]],
        employeeIds: [[]]
      })
    });
  }

  ngOnInit(): void {
    this.loadReports();
    this.loadTemplates();
    this.setupFilterSubscription();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadReports(): void {
    this.isLoading = true;
    this.error = null;
    
    const options: ReportQueryOptions = this.buildQueryOptions();
    
    this.subscriptions.add(
      this.reportsService.getReports(options).subscribe({
        next: (reports) => {
          this.reports = reports;
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des rapports';
          this.isLoading = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  private loadTemplates(): void {
    this.subscriptions.add(
      this.reportsService.getTemplates().subscribe({
        next: (templates) => {
          this.templates = templates;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des templates:', error);
        }
      })
    );
  }

  private buildQueryOptions(): ReportQueryOptions {
    const formValue = this.filterForm.value;
    const options: ReportQueryOptions = {};
    
    if (formValue.reportType) options.reportType = formValue.reportType;
    if (formValue.status) options.status = formValue.status;
    if (formValue.format) options.format = formValue.format;
    
    return options;
  }

  private setupFilterSubscription(): void {
    this.subscriptions.add(
      this.filterForm.valueChanges.subscribe(() => {
        this.applyFilters();
      })
    );
  }

  private applyFilters(): void {
    let filtered = [...this.reports];
    
    // Filtre par terme de recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(report => 
        report.name.toLowerCase().includes(term) ||
        (report.description && report.description.toLowerCase().includes(term))
      );
    }
    
    this.filteredReports = filtered;
  }

  // Actions CRUD
  createReport(): void {
    this.reportForm.reset({
      reportType: ReportType.EMPLOYEE_REPORT,
      format: ReportFormat.PDF,
      parameters: {
        dateRange: {
          start: this.getDefaultStartDate(),
          end: this.getDefaultEndDate()
        },
        includeInactive: false,
        departmentIds: [],
        employeeIds: []
      }
    });
    this.showCreateModal = true;
  }

  onSubmitReport(): void {
    if (this.reportForm.valid) {
      this.isSubmitting = true;
      const formData = this.reportForm.value;
      
      // TODO: Récupérer le companyId depuis le store
      const reportData: CreateReportDto = {
        name: formData.name,
        description: formData.description,
        reportType: formData.reportType,
        format: formData.format,
        parameters: formData.parameters,
        companyId: 'current-company-id'
      };

      this.subscriptions.add(
        this.reportsService.createReport(reportData).subscribe({
          next: (report) => {
            this.closeModal();
            this.loadReports();
            this.isSubmitting = false;
            // Générer automatiquement le rapport
            this.generateReport(report);
          },
          error: (error) => {
            this.error = 'Erreur lors de la création du rapport';
            this.isSubmitting = false;
            console.error('Erreur:', error);
          }
        })
      );
    }
  }

  generateReport(report: Report): void {
    this.subscriptions.add(
      this.reportsService.generateReport(report.id).subscribe({
        next: (updatedReport) => {
          const index = this.reports.findIndex(r => r.id === report.id);
          if (index !== -1) {
            this.reports[index] = updatedReport;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la génération du rapport';
          console.error('Erreur:', error);
        }
      })
    );
  }

  downloadReport(report: Report): void {
    if (report.status !== ReportStatus.COMPLETED) {
      this.error = 'Le rapport n\'est pas encore prêt pour le téléchargement';
      return;
    }

    this.subscriptions.add(
      this.reportsService.downloadReport(report.id).subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${report.name}.${report.format.toLowerCase()}`;
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          this.error = 'Erreur lors du téléchargement du rapport';
          console.error('Erreur:', error);
        }
      })
    );
  }

  deleteReport(report: Report): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le rapport "${report.name}" ?`)) {
      this.subscriptions.add(
        this.reportsService.deleteReport(report.id).subscribe({
          next: () => {
            this.loadReports();
          },
          error: (error) => {
            this.error = 'Erreur lors de la suppression';
            console.error('Erreur:', error);
          }
        })
      );
    }
  }

  // Modal
  closeModal(): void {
    this.showCreateModal = false;
    this.reportForm.reset();
  }

  // Filtres
  resetFilters(): void {
    this.filterForm.reset();
    this.searchTerm = '';
    this.applyFilters();
  }

  // Méthodes utilitaires
  getReportTypeLabel(type: ReportType): string {
    const labels: { [key in ReportType]: string } = {
      [ReportType.EMPLOYEE_REPORT]: 'Rapport employés',
      [ReportType.PAYROLL_REPORT]: 'Rapport paie',
      [ReportType.ATTENDANCE_REPORT]: 'Rapport présence',
      [ReportType.LEAVE_REPORT]: 'Rapport congés',
      [ReportType.PERFORMANCE_REPORT]: 'Rapport performance',
      [ReportType.RECRUITMENT_REPORT]: 'Rapport recrutement',
      [ReportType.TURNOVER_REPORT]: 'Rapport rotation',
      [ReportType.CUSTOM_REPORT]: 'Rapport personnalisé'
    };
    return labels[type];
  }

  getStatusLabel(status: ReportStatus): string {
    const labels: { [key in ReportStatus]: string } = {
      [ReportStatus.PENDING]: 'En attente',
      [ReportStatus.PROCESSING]: 'En cours',
      [ReportStatus.COMPLETED]: 'Terminé',
      [ReportStatus.FAILED]: 'Échec'
    };
    return labels[status];
  }

  getStatusClasses(status: ReportStatus): string {
    const classes: { [key in ReportStatus]: string } = {
      [ReportStatus.PENDING]: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [ReportStatus.PROCESSING]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [ReportStatus.COMPLETED]: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [ReportStatus.FAILED]: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
    };
    return classes[status];
  }

  getFormatLabel(format: ReportFormat): string {
    return format;
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private getDefaultStartDate(): string {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    return date.toISOString().split('T')[0];
  }

  private getDefaultEndDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  canDownload(report: Report): boolean {
    return report.status === ReportStatus.COMPLETED && !!report.filePath;
  }

  canRegenerate(report: Report): boolean {
    return report.status === ReportStatus.FAILED || report.status === ReportStatus.COMPLETED;
  }
}
