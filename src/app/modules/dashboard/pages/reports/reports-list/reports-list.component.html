<div class="p-6">
  <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
    Gestion des Rapports
  </h1>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
  </div>

  <!-- Error -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
  >
    {{ error }}
  </div>

  <!-- Content -->
  <div *ngIf="!isLoading && !error">
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <p class="text-gray-600 dark:text-gray-400">
        Module de gestion des rapports en cours de développement...
      </p>
    </div>
  </div>
</div>
