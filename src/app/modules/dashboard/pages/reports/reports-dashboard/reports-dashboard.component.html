<div class="p-6">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard RH
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Vue d'ensemble des métriques et analytics de votre entreprise
        </p>
      </div>
      <div class="flex gap-3">
        <button
          (click)="refreshData()"
          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            ></path>
          </svg>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Filtres de date -->
  <div
    class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6"
  >
    <div class="flex items-center gap-4">
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Date de début</label
        >
        <input
          type="date"
          [(ngModel)]="dateRange.start"
          (change)="onDateRangeChange()"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Date de fin</label
        >
        <input
          type="date"
          [(ngModel)]="dateRange.end"
          (change)="onDateRangeChange()"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6 flex items-center justify-between"
  >
    <span>{{ error }}</span>
    <button (click)="clearError()" class="text-red-500 hover:text-red-700">
      <svg
        class="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        ></path>
      </svg>
    </button>
  </div>

  <!-- Métriques principales -->
  <div
    *ngIf="dashboardMetrics"
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"
  >
    <!-- Total employés -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Total employés
          </p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            {{ dashboardMetrics.totalEmployees }}
          </p>
          <p class="text-sm text-green-600 dark:text-green-400">
            {{ dashboardMetrics.activeEmployees }} actifs
          </p>
        </div>
      </div>
    </div>

    <!-- Nouvelles embauches -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-green-600 dark:text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Embauches ce mois
          </p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            {{ dashboardMetrics.newHiresThisMonth }}
          </p>
        </div>
      </div>
    </div>

    <!-- Masse salariale -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-yellow-600 dark:text-yellow-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Masse salariale
          </p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            {{ formatCurrency(dashboardMetrics.totalPayroll) }}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Moy: {{ formatCurrency(dashboardMetrics.averageSalary) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Congés en attente -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-orange-600 dark:text-orange-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Congés en attente
          </p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            {{ dashboardMetrics.pendingLeaves }}
          </p>
        </div>
      </div>
    </div>

    <!-- Taux de rotation -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Taux de rotation
          </p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">
            {{ formatPercentage(dashboardMetrics.employeeTurnoverRate) }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Graphiques et analytics -->
  <div *ngIf="analyticsData" class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Croissance des employés -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Croissance des employés
      </h3>
      <div class="space-y-3">
        <div
          *ngFor="let item of analyticsData.employeeGrowth"
          class="flex items-center justify-between"
        >
          <span class="text-sm text-gray-600 dark:text-gray-400">{{
            getMonthName(item.month)
          }}</span>
          <div class="flex items-center">
            <div
              class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3"
            >
              <div
                class="h-2 rounded-full bg-blue-500"
                [style.width.%]="
                  getBarWidth(
                    item.count,
                    getMaxValue(analyticsData.employeeGrowth)
                  )
                "
              ></div>
            </div>
            <span
              class="text-sm font-medium text-gray-900 dark:text-white w-8"
              >{{ item.count }}</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Répartition par département -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Répartition par département
      </h3>
      <div class="space-y-3">
        <div
          *ngFor="let item of analyticsData.departmentDistribution"
          class="flex items-center justify-between"
        >
          <span class="text-sm text-gray-600 dark:text-gray-400">{{
            item.department
          }}</span>
          <div class="flex items-center">
            <div
              class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3"
            >
              <div
                class="h-2 rounded-full bg-green-500"
                [style.width.%]="
                  getBarWidth(
                    item.count,
                    getMaxValue(analyticsData.departmentDistribution)
                  )
                "
              ></div>
            </div>
            <span
              class="text-sm font-medium text-gray-900 dark:text-white w-8"
              >{{ item.count }}</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Tendance des congés -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Tendance des congés
      </h3>
      <div class="space-y-4">
        <div
          *ngFor="let item of analyticsData.leavesTrend"
          class="border-b border-gray-200 dark:border-gray-700 pb-3 last:border-b-0"
        >
          <div class="flex justify-between items-center mb-2">
            <span
              class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >{{ getMonthName(item.month) }}</span
            >
            <span class="text-xs text-gray-500 dark:text-gray-400"
              >Total: {{ item.approved + item.pending + item.rejected }}</span
            >
          </div>
          <div class="grid grid-cols-3 gap-2 text-xs">
            <div class="text-center">
              <div class="text-green-600 dark:text-green-400 font-medium">
                {{ item.approved }}
              </div>
              <div class="text-gray-500 dark:text-gray-400">Approuvés</div>
            </div>
            <div class="text-center">
              <div class="text-yellow-600 dark:text-yellow-400 font-medium">
                {{ item.pending }}
              </div>
              <div class="text-gray-500 dark:text-gray-400">En attente</div>
            </div>
            <div class="text-center">
              <div class="text-red-600 dark:text-red-400 font-medium">
                {{ item.rejected }}
              </div>
              <div class="text-gray-500 dark:text-gray-400">Rejetés</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Métriques de recrutement -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Métriques de recrutement
      </h3>
      <div class="space-y-3">
        <div
          *ngFor="let item of analyticsData.recruitmentMetrics"
          class="flex items-center justify-between"
        >
          <span class="text-sm text-gray-600 dark:text-gray-400">{{
            getMonthName(item.month)
          }}</span>
          <div class="flex items-center gap-4">
            <div class="text-center">
              <div class="text-sm font-medium text-blue-600 dark:text-blue-400">
                {{ item.applications }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Candidatures
              </div>
            </div>
            <div class="text-center">
              <div
                class="text-sm font-medium text-green-600 dark:text-green-400"
              >
                {{ item.hired }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Embauches
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Rapports rapides -->
  <div
    class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
  >
    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
      Rapports rapides
    </h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <button
        (click)="generateQuickReport('EMPLOYEE_REPORT')"
        class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-left"
      >
        <div class="flex items-center mb-2">
          <svg
            class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            ></path>
          </svg>
          <span class="font-medium text-gray-900 dark:text-white"
            >Employés</span
          >
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Rapport détaillé des employés
        </p>
      </button>

      <button
        (click)="generateQuickReport('PAYROLL_REPORT')"
        class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-left"
      >
        <div class="flex items-center mb-2">
          <svg
            class="w-5 h-5 text-green-600 dark:text-green-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            ></path>
          </svg>
          <span class="font-medium text-gray-900 dark:text-white">Paie</span>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Rapport de la masse salariale
        </p>
      </button>

      <button
        (click)="generateQuickReport('ATTENDANCE_REPORT')"
        class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-left"
      >
        <div class="flex items-center mb-2">
          <svg
            class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <span class="font-medium text-gray-900 dark:text-white"
            >Présence</span
          >
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Rapport de présence
        </p>
      </button>

      <button
        (click)="generateQuickReport('LEAVE_REPORT')"
        class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-left"
      >
        <div class="flex items-center mb-2">
          <svg
            class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            ></path>
          </svg>
          <span class="font-medium text-gray-900 dark:text-white">Congés</span>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Rapport des congés
        </p>
      </button>
    </div>
  </div>
</div>
