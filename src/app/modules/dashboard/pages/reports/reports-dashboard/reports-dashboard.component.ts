import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { 
  ReportsService, 
  DashboardMetrics,
  AnalyticsData,
  ReportStatistics
} from '../../../../../core/services/reports/reports.service';

@Component({
  selector: 'app-reports-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './reports-dashboard.component.html'
})
export class ReportsDashboardComponent implements OnInit, OnDestroy {
  dashboardMetrics: DashboardMetrics | null = null;
  analyticsData: AnalyticsData | null = null;
  reportStats: ReportStatistics | null = null;
  
  // Filtres de date
  dateRange = {
    start: this.getDefaultStartDate(),
    end: this.getDefaultEndDate()
  };
  
  // États
  isLoadingMetrics = false;
  isLoadingAnalytics = false;
  isLoadingStats = false;
  error: string | null = null;
  
  private subscriptions = new Subscription();
  private currentCompanyId = 'current-company-id'; // TODO: Récupérer depuis le store

  constructor(private reportsService: ReportsService) {}

  ngOnInit(): void {
    this.loadDashboardMetrics();
    this.loadAnalyticsData();
    this.loadReportStatistics();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadDashboardMetrics(): void {
    this.isLoadingMetrics = true;
    this.subscriptions.add(
      this.reportsService.getDashboardMetrics(this.currentCompanyId).subscribe({
        next: (metrics) => {
          this.dashboardMetrics = metrics;
          this.isLoadingMetrics = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des métriques';
          this.isLoadingMetrics = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  private loadAnalyticsData(): void {
    this.isLoadingAnalytics = true;
    this.subscriptions.add(
      this.reportsService.getAnalyticsData(this.currentCompanyId, this.dateRange).subscribe({
        next: (analytics) => {
          this.analyticsData = analytics;
          this.isLoadingAnalytics = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des analytics';
          this.isLoadingAnalytics = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  private loadReportStatistics(): void {
    this.isLoadingStats = true;
    this.subscriptions.add(
      this.reportsService.getReportStatistics().subscribe({
        next: (stats) => {
          this.reportStats = stats;
          this.isLoadingStats = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des statistiques';
          this.isLoadingStats = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  // Actions
  onDateRangeChange(): void {
    this.loadAnalyticsData();
  }

  refreshData(): void {
    this.loadDashboardMetrics();
    this.loadAnalyticsData();
    this.loadReportStatistics();
  }

  generateQuickReport(reportType: string): void {
    const parameters = {
      companyId: this.currentCompanyId,
      dateRange: this.dateRange
    };

    this.subscriptions.add(
      this.reportsService.generateQuickReport(reportType as any, parameters).subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `rapport_${reportType}_${new Date().toISOString().split('T')[0]}.pdf`;
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          this.error = 'Erreur lors de la génération du rapport';
          console.error('Erreur:', error);
        }
      })
    );
  }

  // Méthodes utilitaires
  private getDefaultStartDate(): string {
    const date = new Date();
    date.setMonth(date.getMonth() - 3); // 3 mois en arrière
    return date.toISOString().split('T')[0];
  }

  private getDefaultEndDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'USD' // TODO: Récupérer la devise depuis les paramètres
    }).format(amount);
  }

  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  getGrowthIcon(current: number, previous: number): string {
    if (current > previous) return '↗️';
    if (current < previous) return '↘️';
    return '➡️';
  }

  getGrowthColor(current: number, previous: number): string {
    if (current > previous) return 'text-green-600 dark:text-green-400';
    if (current < previous) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  }

  calculateGrowthPercentage(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  getMaxValue(data: Array<{ count?: number; applications?: number; hired?: number }>): number {
    return Math.max(...data.map(item => 
      Math.max(item.count || 0, item.applications || 0, item.hired || 0)
    ));
  }

  getBarWidth(value: number, maxValue: number): number {
    return maxValue > 0 ? (value / maxValue) * 100 : 0;
  }

  getMonthName(monthStr: string): string {
    const months = [
      'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
      'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
    ];
    const monthIndex = parseInt(monthStr.split('-')[1]) - 1;
    return months[monthIndex] || monthStr;
  }

  clearError(): void {
    this.error = null;
  }
}
