import { Routes } from '@angular/router';

export const REPORTS_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    loadComponent: () =>
      import('./reports-dashboard/reports-dashboard.component').then(
        (m) => m.ReportsDashboardComponent
      ),
    data: {
      title: 'Dashboard RH',
      description: "Vue d'ensemble des métriques et analytics",
    },
  },
  {
    path: 'list',
    loadComponent: () =>
      import('./reports-list/reports-list.component').then(
        (m) => m.ReportsListComponent
      ),
    data: {
      title: 'Gestion des rapports',
      description: 'Créer et gérer vos rapports personnalisés',
    },
  },
  {
    path: 'hr',
    loadComponent: () =>
      import('./hr-reports/hr-reports.component').then(
        (m) => m.HrReportsComponent
      ),
    title: 'Rapports RH - LuminaHR',
  },
  {
    path: 'analytics',
    loadComponent: () =>
      import('./analytics/analytics.component').then(
        (m) => m.AnalyticsComponent
      ),
    title: 'Analytics RH - LuminaHR',
  },
  {
    path: 'create',
    loadComponent: () =>
      import('./report-create/report-create.component').then(
        (m) => m.ReportCreateComponent
      ),
    title: 'Créer un Rapport - LuminaHR',
  },
  {
    path: 'view/:id',
    loadComponent: () =>
      import('./report-view/report-view.component').then(
        (m) => m.ReportViewComponent
      ),
    title: 'Détails du Rapport - LuminaHR',
  },
];
