import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ReportService } from '../../../../../core/services/report/report.service';
import {
  Report,
  ReportRequest,
  ReportExportRequest,
  ExportFormat,
  ReportType,
} from '../../../../../core/models/report.model';

@Component({
  selector: 'app-hr-reports',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './hr-reports.component.html',
})
export class HrReportsComponent implements OnInit, OnDestroy {
  recentReports: Report[] = [];

  private subscriptions = new Subscription();

  constructor(private reportService: ReportService, private router: Router) {}

  ngOnInit(): void {
    this.loadRecentReports();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadRecentReports(): void {
    this.subscriptions.add(
      this.reportService.getReports().subscribe((reports) => {
        this.recentReports = reports
          .sort(
            (a, b) =>
              b.lastGeneratedAt!.getTime() - a.lastGeneratedAt!.getTime()
          )
          .slice(0, 5);
      })
    );
  }

  // Génération de rapports prédéfinis
  generateEmployeeReport(): void {
    const request: ReportRequest = {
      name: 'Rapport Employés - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Rapport mensuel des employés',
      parameters: {
        dateRange: 'current_month',
        includeInactive: false,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateDemographicsReport(): void {
    const request: ReportRequest = {
      name: 'Rapport Démographique - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Analyse démographique des employés',
      parameters: {
        includeAge: true,
        includeGender: true,
        includeTenure: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateAttendanceReport(): void {
    const dateRange = this.getCurrentMonthRange();

    this.subscriptions.add(
      this.reportService
        .generateAttendanceReport(dateRange)
        .subscribe((attendanceData) => {
          const request: ReportRequest = {
            name:
              'Rapport Présence - ' + new Date().toLocaleDateString('fr-FR'),
            description: 'Rapport mensuel des présences',
            parameters: {
              dateFrom: dateRange.from,
              dateTo: dateRange.to,
            },
            filters: [],
            includeCharts: true,
            includeSummary: true,
          };

          this.subscriptions.add(
            this.reportService.generateReport(request).subscribe((report) => {
              this.router.navigate(['/dashboard/reports/view', report.id]);
            })
          );
        })
    );
  }

  generateLeaveReport(): void {
    const request: ReportRequest = {
      name: 'Rapport Congés - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Analyse des congés et absences',
      parameters: {
        year: new Date().getFullYear(),
        includeTypes: true,
        includeBalances: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generatePayrollReport(): void {
    const period = {
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
    };

    this.subscriptions.add(
      this.reportService
        .generatePayrollReport(period)
        .subscribe((payrollData) => {
          const request: ReportRequest = {
            name: 'Rapport Paie - ' + new Date().toLocaleDateString('fr-FR'),
            description: 'Analyse de la masse salariale',
            parameters: {
              month: period.month,
              year: period.year,
            },
            filters: [],
            includeCharts: true,
            includeSummary: true,
          };

          this.subscriptions.add(
            this.reportService.generateReport(request).subscribe((report) => {
              this.router.navigate(['/dashboard/reports/view', report.id]);
            })
          );
        })
    );
  }

  generateSalaryAnalysis(): void {
    const request: ReportRequest = {
      name: 'Analyse Salariale - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Analyse détaillée des salaires',
      parameters: {
        includeEquity: true,
        includeBenchmarks: true,
        includeDistribution: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateRecruitmentReport(): void {
    const request: ReportRequest = {
      name: 'Rapport Recrutement - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Performance du recrutement',
      parameters: {
        period: 'last_quarter',
        includeMetrics: true,
        includeFunnel: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateSourceAnalysis(): void {
    const request: ReportRequest = {
      name:
        'Analyse Sources Recrutement - ' +
        new Date().toLocaleDateString('fr-FR'),
      description: 'Efficacité des sources de recrutement',
      parameters: {
        includeCosts: true,
        includeConversion: true,
        includeQuality: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateTrainingReport(): void {
    const request: ReportRequest = {
      name: 'Rapport Formation - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Activité de formation',
      parameters: {
        year: new Date().getFullYear(),
        includeHours: true,
        includeCosts: true,
        includeParticipation: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateSkillsReport(): void {
    const request: ReportRequest = {
      name:
        'Cartographie Compétences - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Analyse des compétences',
      parameters: {
        includeGaps: true,
        includeNeeds: true,
        includeDevelopment: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generatePerformanceReport(): void {
    const request: ReportRequest = {
      name: 'Rapport Performance - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Évaluations de performance',
      parameters: {
        year: new Date().getFullYear(),
        includeRatings: true,
        includeDistribution: true,
        includeComments: false,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  generateGoalsReport(): void {
    const request: ReportRequest = {
      name: 'Suivi Objectifs - ' + new Date().toLocaleDateString('fr-FR'),
      description: 'Atteinte des objectifs',
      parameters: {
        year: new Date().getFullYear(),
        includeProgress: true,
        includeAchievement: true,
      },
      filters: [],
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.generateReport(request).subscribe((report) => {
        this.router.navigate(['/dashboard/reports/view', report.id]);
      })
    );
  }

  // Actions sur les rapports existants
  viewReport(id: string): void {
    this.router.navigate(['/dashboard/reports/view', id]);
  }

  downloadReport(id: string): void {
    const exportRequest: ReportExportRequest = {
      reportId: id,
      format: ExportFormat.PDF,
      includeCharts: true,
      includeSummary: true,
    };

    this.subscriptions.add(
      this.reportService.exportReport(exportRequest).subscribe((blob) => {
        const report = this.recentReports.find((r) => r.id === id);
        const url = window.URL.createObjectURL(blob);
        const link = window.document.createElement('a');
        link.href = url;
        link.download = `${report?.name || 'rapport'}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      })
    );
  }

  printReport(id: string): void {
    this.subscriptions.add(
      this.reportService.printReport(id).subscribe((success) => {
        if (!success) {
          alert("Erreur lors de l'impression du rapport");
        }
      })
    );
  }

  createCustomReport(): void {
    this.router.navigate(['/dashboard/reports/create']);
  }

  // Méthodes utilitaires
  private getCurrentMonthRange(): { from: Date; to: Date } {
    const now = new Date();
    const from = new Date(now.getFullYear(), now.getMonth(), 1);
    const to = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    return { from, to };
  }
}
