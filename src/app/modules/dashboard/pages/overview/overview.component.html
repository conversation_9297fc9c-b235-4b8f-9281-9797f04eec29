<div>
  <!-- Header -->
  <app-overview-header data-tour="overview-header"></app-overview-header>
  <!-- end Header -->

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
    <span class="ml-3 text-gray-600 dark:text-gray-400"
      >Chargement des données...</span
    >
  </div>

  <!-- Error State -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
  >
    <div class="flex items-center">
      <svg
        class="w-5 h-5 mr-2"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        ></path>
      </svg>
      <span>{{ error }}</span>
      <button
        (click)="refreshDashboard()"
        class="ml-auto text-red-600 hover:text-red-800"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && !error">
    <!-- Employee Statistics Cards -->
    <div
      class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 mb-6"
    >
      @for (item of employeeData; track $index) {
      <app-employee-card
        [employeeData]="item"
        data-tour="employee-cards"
      ></app-employee-card>
      }
    </div>

    <!-- Enhanced Metrics Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Task Completion Rate -->
      <div class="bg-background rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-blue-600 dark:text-blue-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Tâches complétées
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ getTaskCompletionRate() }}%
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ getOverdueTasksCount() }} en retard
            </p>
          </div>
        </div>
      </div>

      <!-- Performance Score -->
      <div class="bg-background rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Score performance
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ getPerformanceAverageScore() }}/5
            </p>
            <p class="text-sm text-green-600 dark:text-green-400">
              Moyenne équipe
            </p>
          </div>
        </div>
      </div>

      <!-- Payroll -->
      <div class="bg-background rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-yellow-600 dark:text-yellow-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Masse salariale
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ formatCurrency(getTotalPayroll()) }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Ce mois</p>
          </div>
        </div>
      </div>

      <!-- Pending Leaves -->
      <div class="bg-background rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-orange-600 dark:text-orange-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                ></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Congés en attente
            </p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">
              {{ getPendingLeavesCount() }}
            </p>
            <p class="text-sm text-orange-600 dark:text-orange-400">
              À approuver
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Tables Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2">
        <div overview-chart-card data-tour="charts-section"></div>
      </div>
      <div>
        <app-upcoming-events data-tour="upcoming-events"></app-upcoming-events>
      </div>
    </div>

    <!-- Tables Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <div overview-leave-request-table data-tour="leave-requests"></div>
      <div overview-employees-table data-tour="employees-table"></div>
    </div>
  </div>
</div>
