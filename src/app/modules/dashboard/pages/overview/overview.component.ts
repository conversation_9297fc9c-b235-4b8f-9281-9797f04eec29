import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription, forkJoin } from 'rxjs';
import { EmployeeStat } from '../../models/employee';
import { OverviewHeaderComponent } from '../../components/overview/overview-header/overview-header.component';
import { EmployeeDataCardComponent } from '../../components/overview/employee-data-card/employee-data-card.component';
import { OverviewChartCardComponent } from '../../components/overview/overview-chart-card/overview-chart-card.component';
import { OverviewEmployeesTableComponent } from '../../components/overview/overview-employees-table/overview-employees-table.component';
import { OverviewLeaveRequestTableComponent } from '../../components/overview/overview-leave-request-table/overview-leave-request-table.component';
import { UpcomingEventsComponent } from '../../components/overview/upcoming-events/upcoming-events.component';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import {
  TaskService,
  TaskStatistics,
  TaskStatus,
} from 'src/app/core/services/task/task.service';
import {
  PerformanceService,
  PerformanceStatistics,
} from 'src/app/core/services/performance/performance.service';
import {
  ReportsService,
  DashboardMetrics,
} from 'src/app/core/services/reports/reports.service';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { Company } from 'src/app/core/models/company.model';

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  imports: [
    CommonModule,
    OverviewHeaderComponent,
    EmployeeDataCardComponent,
    OverviewChartCardComponent,
    OverviewEmployeesTableComponent,
    OverviewLeaveRequestTableComponent,
    UpcomingEventsComponent,
  ],
})
export class OverviewComponent implements OnInit, OnDestroy {
  employeeData!: EmployeeStat[];
  company!: Company;

  // Nouvelles données du dashboard
  taskStatistics: TaskStatistics | null = null;
  performanceStatistics: PerformanceStatistics | null = null;
  dashboardMetrics: DashboardMetrics | null = null;

  // États
  isLoading = false;
  error: string | null = null;

  private subscriptions = new Subscription();

  constructor(
    private employeeService: EmployeeService,
    private taskService: TaskService,
    private performanceService: PerformanceService,
    private reportsService: ReportsService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.subscriptions.add(
      this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
        this.company = company;
        if (company?.id) {
          this.loadAllDashboardData();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Charge toutes les données du dashboard en parallèle
   */
  private loadAllDashboardData(): void {
    this.isLoading = true;
    this.error = null;

    const companyId = this.company.id!;

    // Charger toutes les données en parallèle
    this.subscriptions.add(
      forkJoin({
        employeeStats: this.employeeService.getEmployeeStatistics(companyId),
        taskStats: this.taskService.getTaskStatistics(companyId),
        performanceStats: this.performanceService.getStatistics(),
        dashboardMetrics: this.reportsService.getDashboardMetrics(companyId),
      }).subscribe({
        next: (data) => {
          this.processEmployeeStatistics(data.employeeStats);
          this.taskStatistics = data.taskStats;
          this.performanceStatistics = data.performanceStats;
          this.dashboardMetrics = data.dashboardMetrics;
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des données du dashboard';
          this.isLoading = false;
          console.error('Erreur dashboard:', error);
        },
      })
    );
  }

  /**
   * Charge les statistiques des employés et calcule les pourcentages
   * en comparant le mois actuel avec le mois précédent
   */
  /**
   * Traite les statistiques des employés pour créer les données des cartes
   */
  private processEmployeeStatistics(stats: any): void {
    // Utilisation des propriétés correctes du backend avec fallback sur les anciennes
    this.employeeData = [
      {
        icon: 'assets/icons/heroicons/outline/users.svg',
        title: 'Total des employés',
        value: stats.total || stats.totalEmployees || 0,
        percentage: 5.2, // TODO: Calculer la vraie comparaison
        change: 'augmentation',
        progress: this.calculateProgressValue(5.2),
        color: 'purple',
      },
      {
        icon: 'assets/icons/heroicons/outline/user-check.svg',
        title: 'Employés actifs',
        value: stats.active || stats.activeEmployees || 0,
        percentage: 2.1,
        change: 'augmentation',
        progress: this.calculateProgressValue(2.1),
        color: 'green',
      },
      {
        icon: 'assets/icons/heroicons/outline/user-plus.svg',
        title: 'Nouvelles embauches',
        value: stats.recentHires || stats.newHiresThisMonth || 0,
        percentage: 12.5,
        change: 'augmentation',
        progress: this.calculateProgressValue(12.5),
        color: 'blue',
      },
      {
        icon: 'assets/icons/heroicons/outline/user-minus.svg',
        title: 'Employés inactifs',
        value: stats.inactive || stats.inactiveEmployees || 0,
        percentage: -1.8,
        change: 'diminution',
        progress: this.calculateProgressValue(-1.8),
        color: 'orange',
      },
    ];
  }

  /**
   * Calcule une valeur de progression pour la barre de progrès
   * basée sur le pourcentage de changement
   * @param percentageChange Le pourcentage de changement
   * @returns Une valeur entre 0 et 100 pour la barre de progrès
   */
  private calculateProgressValue(percentageChange: number): number {
    // Une approche simple est de transformer le pourcentage en valeur entre 0 et 100
    const absPercentage = Math.abs(percentageChange);

    // Limiter la valeur maximale à 100% pour la barre de progrès
    return Math.min(absPercentage, 100);
  }

  /**
   * Méthodes utilitaires pour le dashboard amélioré
   */
  refreshDashboard(): void {
    if (this.company?.id) {
      this.loadAllDashboardData();
    }
  }

  getTaskCompletionRate(): number {
    if (!this.taskStatistics || this.taskStatistics.totalTasks === 0) return 0;
    // Utiliser completionRate du backend ou calculer depuis byStatus
    if (this.taskStatistics.completionRate !== undefined) {
      return Math.round(this.taskStatistics.completionRate);
    }
    // Fallback: calculer depuis byStatus
    const completedTasks =
      this.taskStatistics.byStatus?.[TaskStatus.COMPLETED] || 0;
    return Math.round((completedTasks / this.taskStatistics.totalTasks) * 100);
  }

  getPerformanceAverageScore(): number {
    // Utiliser averageOverallRating du backend avec fallback sur averageScore
    return (
      this.performanceStatistics?.averageOverallRating ||
      this.performanceStatistics?.averageScore ||
      0
    );
  }

  getOverdueTasksCount(): number {
    return this.taskStatistics?.overdueTasks || 0;
  }

  getPendingLeavesCount(): number {
    return this.dashboardMetrics?.pendingLeaves || 0;
  }

  getTotalPayroll(): number {
    return this.dashboardMetrics?.totalPayroll || 0;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  getTasksByStatusCount(status: TaskStatus): number {
    if (!this.taskStatistics?.byStatus) return 0;
    return this.taskStatistics.byStatus[status] || 0;
  }

  getEmployeeTurnoverRate(): number {
    return this.dashboardMetrics?.employeeTurnoverRate || 0;
  }

  getNewHiresThisMonth(): number {
    return this.dashboardMetrics?.newHiresThisMonth || 0;
  }
}
