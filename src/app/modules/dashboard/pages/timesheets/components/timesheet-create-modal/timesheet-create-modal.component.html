<div class="container mx-auto p-4">
  <h2 class="text-2xl font-semibold mb-6">
    {{
      timesheet ? "Modifier la feuille de temps" : "Nouvelle feuille de temps"
    }}
  </h2>

  <form [formGroup]="timesheetForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label
          for="employeeId"
          class="block text-sm font-medium text-gray-700 mb-1"
          >Employé</label
        >
        <select
          id="employeeId"
          formControlName="employeeId"
          class="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          [ngClass]="{
            'border-red-500':
              timesheetForm.get('employeeId')?.invalid &&
              timesheetForm.get('employeeId')?.touched
          }"
        >
          <option value="">Sélectionner un employé</option>
          <option *ngFor="let employee of employees" [value]="employee.id">
            {{ employee._personalInfo?.firstName }}
            {{ employee._personalInfo?.lastName }}
          </option>
        </select>
        <p
          *ngIf="
            timesheetForm.get('employeeId')?.invalid &&
            timesheetForm.get('employeeId')?.touched
          "
          class="mt-1 text-sm text-red-600"
        >
          L'employé est requis.
        </p>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label
          for="periodStart"
          class="block text-sm font-medium text-gray-700 mb-1"
          >Date de début</label
        >
        <input
          type="date"
          id="periodStart"
          formControlName="periodStart"
          class="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          [ngClass]="{
            'border-red-500':
              timesheetForm.get('periodStart')?.invalid &&
              timesheetForm.get('periodStart')?.touched
          }"
        />
        <p
          *ngIf="
            timesheetForm.get('periodStart')?.invalid &&
            timesheetForm.get('periodStart')?.touched
          "
          class="mt-1 text-sm text-red-600"
        >
          La date de début est requise.
        </p>
      </div>

      <div>
        <label
          for="periodEnd"
          class="block text-sm font-medium text-gray-700 mb-1"
          >Date de fin</label
        >
        <input
          type="date"
          id="periodEnd"
          formControlName="periodEnd"
          class="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          [ngClass]="{
            'border-red-500':
              (timesheetForm.get('periodEnd')?.invalid || !isEndDateValid()) &&
              timesheetForm.get('periodEnd')?.touched
          }"
        />
        <p
          *ngIf="
            timesheetForm.get('periodEnd')?.invalid &&
            timesheetForm.get('periodEnd')?.touched
          "
          class="mt-1 text-sm text-red-600"
        >
          La date de fin est requise.
        </p>
        <p
          *ngIf="
            timesheetForm.get('periodEnd')?.valid &&
            !isEndDateValid() &&
            timesheetForm.get('periodEnd')?.touched
          "
          class="mt-1 text-sm text-red-600"
        >
          La date de fin doit être après la date de début.
        </p>
      </div>
    </div>

    <div class="flex justify-end space-x-3 mt-8">
      <button
        type="button"
        class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        (click)="onCancel()"
      >
        Annuler
      </button>
      <button
        type="submit"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        [disabled]="timesheetForm.invalid || !isEndDateValid()"
        [ngClass]="{
          'opacity-50 cursor-not-allowed':
            timesheetForm.invalid || !isEndDateValid()
        }"
      >
        {{
          timesheet
            ? "Enregistrer les modifications"
            : "Créer la feuille de temps"
        }}
      </button>
    </div>
  </form>

  <!-- Afficher le formulaire des jours de travail uniquement si on est en mode édition et que le timesheet a un ID -->
  <div
    *ngIf="timesheet && timesheet.id"
    class="mt-12 pt-8 border-t border-gray-200"
  >
    <app-workday-form
      [timesheet]="timesheet"
      [existingWorkDays]="timesheet.workDays"
      (save)="onWorkdaysSaved($event)"
    >
    </app-workday-form>
  </div>
</div>
