// timesheet-form.component.ts
import { <PERSON><PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, NgF<PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Timesheet,
  WorkDay,
} from 'src/app/core/services/timesheet/timesheet.service';
import { EmployeeResponse } from 'src/app/core/models/employee.model';
import { WorkdayFormComponent } from '../workday-form/workday-form.component';

@Component({
  selector: 'app-timesheet-create-modal',
  templateUrl: './timesheet-create-modal.component.html',
  imports: [
    NgClass,
    NgIf,
    NgFor,
    FormsModule,
    ReactiveFormsModule,
    WorkdayFormComponent,
  ],
})
export class TimesheetFormComponent implements OnInit {
  @Input() timesheet: Timesheet | null = null;
  @Input() employees: EmployeeResponse[] = [];
  @Input() employeeId!: number;
  @Output() save = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<void>();
  workdays: WorkDay[] = [];

  timesheetForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.timesheetForm = this.fb.group({
      employeeId: ['', Validators.required],
      periodStart: ['', Validators.required],
      periodEnd: ['', Validators.required],
      totalRegularHours: [0, [Validators.min(0)]],
      totalOvertimeHours: [0, [Validators.min(0)]],
      totalUndertimeHours: [0, [Validators.min(0)]],
    });
  }

  ngOnInit(): void {
    if (this.timesheet) {
      // Pour l'édition, tous les champs sont requis
      this.timesheetForm = this.fb.group({
        employeeId: [this.timesheet.employeeId, Validators.required],
        periodStart: [
          this.formatDateForInput(new Date(this.timesheet.periodStart)),
          Validators.required,
        ],
        periodEnd: [
          this.formatDateForInput(new Date(this.timesheet.periodEnd)),
          Validators.required,
        ],
        totalRegularHours: [
          this.timesheet.totalRegularHours,
          [Validators.required, Validators.min(0)],
        ],
        totalOvertimeHours: [
          this.timesheet.totalOvertimeHours,
          [Validators.required, Validators.min(0)],
        ],
        totalUndertimeHours: [
          this.timesheet.totalUndertimeHours,
          [Validators.required, Validators.min(0)],
        ],
      });
    } else {
      // Pour la création, seulement les champs de base sont nécessaires
      this.timesheetForm = this.fb.group({
        employeeId: [this.employeeId || '', Validators.required],
        periodStart: ['', Validators.required],
        periodEnd: ['', Validators.required],
        totalRegularHours: [0],
        totalOvertimeHours: [0],
        totalUndertimeHours: [0],
      });
    }
  }

  // Formater la date pour l'input de type date (YYYY-MM-DD)
  private formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  onSubmit(): void {
    if (this.timesheetForm.invalid) {
      Object.keys(this.timesheetForm.controls).forEach((key) => {
        this.timesheetForm.get(key)?.markAsTouched();
      });
      return;
    }

    const formData = {
      ...this.timesheetForm.value,
      employeeId: this.timesheetForm.value.employeeId,
      periodStart: new Date(this.timesheetForm.value.periodStart).toISOString(),
      periodEnd: new Date(this.timesheetForm.value.periodEnd).toISOString(),
      workdays: this.workdays, // Ajouter les workdays ici
    };

    this.save.emit(formData);
  }

  onWorkdaysSaved(workdays: WorkDay[]): void {
    this.workdays = workdays;
    this.onSubmit(); // Soumettre le timesheet avec les workdays
  }

  onCancel(): void {
    this.cancel.emit();
  }

  // Vérifier si la date de fin est après la date de début
  isEndDateValid(): boolean {
    const start = this.timesheetForm.get('periodStart')?.value;
    const end = this.timesheetForm.get('periodEnd')?.value;

    if (!start || !end) return true;

    return new Date(end) >= new Date(start);
  }
}
