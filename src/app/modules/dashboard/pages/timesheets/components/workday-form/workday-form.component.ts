import { CommonModule, DatePipe, NgClass, Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Timesheet,
  WorkDay,
} from 'src/app/core/services/timesheet/timesheet.service';

@Component({
  selector: 'app-workday-form',
  templateUrl: './workday-form.component.html',
  standalone: true,
  imports: [NgClass, CommonModule, ReactiveFormsModule, DatePipe, NgFor, NgIf],
})
export class WorkdayFormComponent implements OnInit, OnChanges {
  @Input() timesheet!: Timesheet;
  @Input() existingWorkDays: WorkDay[] = [];
  @Output() save = new EventEmitter<WorkDay[]>();
  @Output() cancel = new EventEmitter<void>();

  workdayForm: FormGroup;
  minDate: string = '';
  maxDate: string = '';
  isSubmitting = false;

  // Pour suivre les modifications non sauvegardées
  hasUnsavedChanges = false;

  constructor(private fb: FormBuilder) {
    this.workdayForm = this.fb.group({
      workdays: this.fb.array([]),
    });
  }

  ngOnInit(): void {
    this.initForm();

    // S'abonner aux changements du formulaire pour détecter les modifications
    this.workdayForm.valueChanges.subscribe(() => {
      this.hasUnsavedChanges = true;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Si timesheet change, mettre à jour les limites de dates
    if (changes['timesheet'] && this.timesheet) {
      this.updateDateLimits();

      // Si nous avons aussi de nouveaux workdays en même temps, réinitialiser
      if (changes['existingWorkDays']) {
        this.initForm();
      }
    } else if (changes['existingWorkDays'] && !changes['timesheet']) {
      // Si seulement existingWorkDays change, réinitialiser avec ces nouvelles valeurs
      this.initForm();
    }
  }

  private initForm(): void {
    // Mettre à jour les limites de dates avant d'initialiser
    this.updateDateLimits();

    // Réinitialiser les workdays
    while (this.workdays.length) {
      this.workdays.removeAt(0);
    }

    if (this.existingWorkDays && this.existingWorkDays.length) {
      // Pré-remplir avec les workdays existants
      this.existingWorkDays.forEach((workday) => this.addWorkdayGroup(workday));
      this.hasUnsavedChanges = false;
    } else {
      // Ajouter un workday vide si aucun n'existe
      this.addWorkday();
    }
  }

  private updateDateLimits(): void {
    if (this.timesheet) {
      // Format YYYY-MM-DD pour les inputs HTML de type date
      this.minDate = this.formatDateForInput(
        new Date(this.timesheet.periodStart)
      );
      this.maxDate = this.formatDateForInput(
        new Date(this.timesheet.periodEnd)
      );
    }
  }

  private formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  get workdays(): FormArray {
    return this.workdayForm.get('workdays') as FormArray;
  }

  addWorkday(): void {
    // Suggérer une date disponible si possible
    const suggestedDate = this.getSuggestedDate();
    this.addWorkdayGroup({
      date: suggestedDate,
      arrivalTime: '09:00',
      departureTime: '17:00',
    });
  }

  getSuggestedDate(): string {
    // Trouver une date qui n'est pas déjà utilisée
    if (!this.timesheet) return '';

    const start = new Date(this.timesheet.periodStart);
    const end = new Date(this.timesheet.periodEnd);
    const usedDates = this.workdays.controls
      .map((control) => {
        const date = control.get('date')?.value;
        return date ? new Date(date).toISOString().split('T')[0] : null;
      })
      .filter(Boolean);

    // Parcourir les dates dans la période et trouver une non utilisée
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dateStr = currentDate.toISOString().split('T')[0];
      if (!usedDates.includes(dateStr)) {
        return dateStr;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Si toutes les dates sont prises, retourner la date de début
    return this.formatDateForInput(start);
  }

  addWorkdayGroup(workday?: Partial<WorkDay>): void {
    // Extraction de date et heure si workday complet
    let date = '';
    let arrival = '';
    let departure = '';

    if (workday) {
      // Si date est complète (ISO string), on extrait juste la partie date
      if (workday.date) {
        if (typeof workday.date === 'string' && workday.date.includes('T')) {
          date = workday.date.split('T')[0];
        } else {
          date = workday.date.toString();
        }
      }

      // Extraire les heures des formats complets si nécessaire
      if (workday.arrivalTime) {
        if (
          typeof workday.arrivalTime === 'string' &&
          workday.arrivalTime.includes('T')
        ) {
          // Format ISO - extraire l'heure
          const arrivalDate = new Date(workday.arrivalTime);
          arrival = `${arrivalDate
            .getHours()
            .toString()
            .padStart(2, '0')}:${arrivalDate
            .getMinutes()
            .toString()
            .padStart(2, '0')}`;
        } else {
          arrival = workday.arrivalTime.toString();
        }
      }

      if (workday.departureTime) {
        if (
          typeof workday.departureTime === 'string' &&
          workday.departureTime.includes('T')
        ) {
          // Format ISO - extraire l'heure
          const departureDate = new Date(workday.departureTime);
          departure = `${departureDate
            .getHours()
            .toString()
            .padStart(2, '0')}:${departureDate
            .getMinutes()
            .toString()
            .padStart(2, '0')}`;
        } else {
          departure = workday.departureTime.toString();
        }
      }
    }

    // Créer un groupe avec des valeurs par défaut si nécessaire
    const group = this.fb.group({
      id: [workday?.id || null],
      date: [
        date || '',
        [Validators.required, this.dateInRangeValidator.bind(this)],
      ],
      arrivalTime: [
        arrival || '09:00',
        [Validators.required, this.timeValidator],
      ],
      departureTime: [
        departure || '17:00',
        [Validators.required, this.timeValidator, this.departureTimeValidator],
      ],
      // Calculer automatiquement
      regularHours: [{ value: 0, disabled: true }],
    });

    // Abonnement aux changements pour calculer les heures
    const calculateHours = () => {
      const arrivalCtrl = group.get('arrivalTime');
      const departureCtrl = group.get('departureTime');

      if (arrivalCtrl?.valid && departureCtrl?.valid) {
        const arrival = arrivalCtrl.value?.split(':').map(Number) || [0, 0];
        const departure = departureCtrl.value?.split(':').map(Number) || [0, 0];

        // Calculate time difference
        let hours = departure[0] - arrival[0];
        let minutes = departure[1] - arrival[1];

        if (minutes < 0) {
          hours--;
          minutes += 60;
        }

        // Round to nearest quarter hour
        const totalHours = hours + Math.round(minutes / 15) * 0.25;

        // If negative (departure before arrival), set to 0
        const regularHoursValue = Math.max(0, totalHours);
        group
          .get('regularHours')
          ?.setValue(Number(regularHoursValue.toFixed(2)));
      }
    };

    group.get('arrivalTime')?.valueChanges.subscribe(calculateHours);
    group.get('departureTime')?.valueChanges.subscribe(calculateHours);

    // Calculer les heures initiales
    calculateHours();

    this.workdays.push(group);
  }

  removeWorkday(index: number): void {
    this.workdays.removeAt(index);
    this.hasUnsavedChanges = true;
  }

  onSubmit(): void {
    if (this.workdayForm.invalid) {
      this.markFormGroupTouched(this.workdayForm);
      return;
    }

    this.isSubmitting = true;

    const workdaysData: WorkDay[] = this.workdays.value.map((wd: any) => {
      // Créer une nouvelle date pour chaque partie à manipuler
      const baseDate = new Date(wd.date);

      // Extraire les heures/minutes
      const arrivalParts = wd.arrivalTime.split(':').map(Number);
      const departureParts = wd.departureTime.split(':').map(Number);

      // Créer des copies de la date de base pour éviter les modifications mutuelles
      const arrivalDate = new Date(baseDate);
      arrivalDate.setHours(arrivalParts[0], arrivalParts[1], 0, 0);

      const departureDate = new Date(baseDate);
      departureDate.setHours(departureParts[0], departureParts[1], 0, 0);

      // Calculer les heures travaillées
      let hours = departureParts[0] - arrivalParts[0];
      let minutes = departureParts[1] - arrivalParts[1];
      if (minutes < 0) {
        hours--;
        minutes += 60;
      }
      const regularHours = hours + Math.round(minutes / 15) * 0.25;

      return {
        id: wd.id || null,
        timesheetId: this.timesheet.id,
        date: baseDate.toISOString(),
        arrivalTime: arrivalDate.toISOString(),
        departureTime: departureDate.toISOString(),
        regularHours: Math.max(0, regularHours),
        notes: wd.notes || '',
      };
    });

    this.save.emit(workdaysData);
    this.hasUnsavedChanges = false;
    this.isSubmitting = false;
  }

  onCancel(): void {
    // Afficher une confirmation si des modifications non sauvegardées
    if (this.hasUnsavedChanges) {
      if (
        confirm(
          'Vous avez des modifications non sauvegardées. Voulez-vous vraiment annuler?'
        )
      ) {
        this.cancel.emit();
      }
    } else {
      this.cancel.emit();
    }
  }

  // Marquer tous les contrôles d'un FormGroup comme touchés
  private markFormGroupTouched(formGroup: FormGroup | FormArray) {
    if (formGroup instanceof FormGroup) {
      // Handle FormGroup case
      Object.keys(formGroup.controls).forEach((key) => {
        const control = formGroup.get(key);
        if (control) {
          if (control instanceof FormGroup || control instanceof FormArray) {
            this.markFormGroupTouched(control);
          } else {
            control.markAsTouched();
          }
        }
      });
    } else if (formGroup instanceof FormArray) {
      // Handle FormArray case
      formGroup.controls.forEach((control) => {
        if (control instanceof FormGroup || control instanceof FormArray) {
          this.markFormGroupTouched(control);
        } else {
          control.markAsTouched();
        }
      });
    }
  }

  // Validation si la date est dans la période de la feuille de temps
  private dateInRangeValidator(
    control: AbstractControl
  ): { [key: string]: boolean } | null {
    if (!control.value || !this.timesheet) return null;

    const date = new Date(control.value);
    const periodStart = new Date(this.timesheet.periodStart);
    const periodEnd = new Date(this.timesheet.periodEnd);

    // Normaliser les dates en retirant l'heure
    date.setHours(0, 0, 0, 0);
    periodStart.setHours(0, 0, 0, 0);
    periodEnd.setHours(0, 0, 0, 0);

    return date >= periodStart && date <= periodEnd
      ? null
      : { outOfRange: true };
  }

  // Validation du format horaire (HH:mm)
  private timeValidator(
    control: AbstractControl
  ): { [key: string]: boolean } | null {
    if (!control.value) return null;

    const timePattern = /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/; // Format HH:mm
    return timePattern.test(control.value) ? null : { invalidTime: true };
  }

  // Validation que l'heure de départ est après l'heure d'arrivée
  private departureTimeValidator(
    control: AbstractControl
  ): { [key: string]: boolean } | null {
    if (!control.value) return null;

    // On ne peut accéder au formulaire parent que si le control est attaché
    const group = control.parent;
    if (!group) return null;

    const arrivalControl = group.get('arrivalTime');
    if (!arrivalControl || !arrivalControl.value) return null;

    const arrival = arrivalControl.value.split(':').map(Number);
    const departure = control.value.split(':').map(Number);

    // Comparer heures puis minutes
    if (departure[0] > arrival[0]) return null;
    if (departure[0] === arrival[0] && departure[1] >= arrival[1]) return null;

    return { departureBeforeArrival: true };
  }

  // Vérifier si une date est déjà utilisée (pour éviter les doublons)
  isDuplicateDate(index: number): boolean {
    const currentDate = this.workdays.at(index).get('date')?.value;
    if (!currentDate) return false;

    return this.workdays.controls.some(
      (control, i) => i !== index && control.get('date')?.value === currentDate
    );
  }

  // Calculer le total des heures de tous les jours
  getTotalHours(): number {
    return this.workdays.controls.reduce((total, group) => {
      const hours = parseFloat(group.get('regularHours')?.value || 0);
      return total + hours;
    }, 0);
  }
}
