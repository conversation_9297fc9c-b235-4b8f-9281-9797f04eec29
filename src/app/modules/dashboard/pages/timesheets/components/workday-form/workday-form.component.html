<div class="w-full">
  <h2 class="text-xl font-semibold mb-4"><PERSON><PERSON> de travail</h2>
  <p class="mb-4 text-sm text-gray-600">
    Période: {{ timesheet.periodStart | date : "dd/MM/yyyy" }} -
    {{ timesheet.periodEnd | date : "dd/MM/yyyy" }}
  </p>

  <form [formGroup]="workdayForm" (ngSubmit)="onSubmit()">
    <div formArrayName="workdays" class="space-y-6">
      <div
        *ngFor="let workdayGroup of workdays.controls; let i = index"
        class="bg-white border rounded-lg shadow-sm overflow-hidden"
      >
        <div
          class="flex justify-between items-center px-4 py-3"
          [ngClass]="{
            'bg-gray-50 border-b': true,
            'bg-red-50': isDuplicateDate(i)
          }"
        >
          <h5 class="font-medium">
            <PERSON><PERSON> de travail #{{ i + 1 }}
            <span *ngIf="isDuplicateDate(i)" class="text-red-600 text-sm ml-2">
              (Date en double)
            </span>
          </h5>
          <button
            type="button"
            class="p-1 text-red-600 hover:text-red-800 focus:outline-none"
            (click)="removeWorkday(i)"
            [disabled]="workdays.length === 1"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': workdays.length === 1
            }"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </div>

        <div class="p-4" [formGroupName]="i">
          <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <!-- Date -->
            <div class="md:col-span-2">
              <label
                [for]="'date-' + i"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Date <span class="text-red-600">*</span></label
              >
              <input
                type="date"
                [id]="'date-' + i"
                formControlName="date"
                [min]="minDate"
                [max]="maxDate"
                class="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                [ngClass]="{
                  'border-red-500':
                    (workdayGroup.get('date')?.invalid ||
                      workdayGroup.get('date')?.hasError('outOfRange') ||
                      isDuplicateDate(i)) &&
                    workdayGroup.get('date')?.touched
                }"
              />
              <div class="mt-1 text-sm text-red-600">
                <p
                  *ngIf="
                    workdayGroup.get('date')?.invalid &&
                    workdayGroup.get('date')?.touched &&
                    workdayGroup.get('date')?.hasError('required')
                  "
                >
                  La date est requise.
                </p>
                <p
                  *ngIf="
                    workdayGroup.get('date')?.hasError('outOfRange') &&
                    workdayGroup.get('date')?.touched
                  "
                >
                  La date doit être entre {{ minDate | date : "dd/MM/yyyy" }} et
                  {{ maxDate | date : "dd/MM/yyyy" }}.
                </p>
                <p
                  *ngIf="
                    isDuplicateDate(i) && workdayGroup.get('date')?.touched
                  "
                >
                  Cette date existe déjà dans la feuille de temps.
                </p>
              </div>
            </div>

            <!-- Heures d'arrivée -->
            <div>
              <label
                [for]="'arrivalTime-' + i"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Arrivée <span class="text-red-600">*</span></label
              >
              <input
                type="time"
                [id]="'arrivalTime-' + i"
                formControlName="arrivalTime"
                min="00:00"
                max="23:59"
                step="900"
                class="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                [ngClass]="{
                  'border-red-500':
                    workdayGroup.get('arrivalTime')?.invalid &&
                    workdayGroup.get('arrivalTime')?.touched
                }"
              />
              <p
                *ngIf="
                  workdayGroup.get('arrivalTime')?.invalid &&
                  workdayGroup.get('arrivalTime')?.touched
                "
                class="mt-1 text-sm text-red-600"
              >
                Heure invalide.
              </p>
            </div>

            <!-- Heures de départ -->
            <div>
              <label
                [for]="'departureTime-' + i"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Départ <span class="text-red-600">*</span></label
              >
              <input
                type="time"
                [id]="'departureTime-' + i"
                formControlName="departureTime"
                min="00:00"
                max="23:59"
                step="900"
                class="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                [ngClass]="{
                  'border-red-500':
                    workdayGroup.get('departureTime')?.invalid &&
                    workdayGroup.get('departureTime')?.touched
                }"
              />
              <div class="mt-1 text-sm text-red-600">
                <p
                  *ngIf="
                    workdayGroup.get('departureTime')?.invalid &&
                    workdayGroup.get('departureTime')?.touched &&
                    !workdayGroup
                      .get('departureTime')
                      ?.hasError('departureBeforeArrival')
                  "
                >
                  Heure invalide.
                </p>
                <p
                  *ngIf="
                    workdayGroup
                      .get('departureTime')
                      ?.hasError('departureBeforeArrival') &&
                    workdayGroup.get('departureTime')?.touched
                  "
                >
                  Le départ doit être après l'arrivée.
                </p>
              </div>
            </div>

            <!-- Heures calculées -->
            <div>
              <label
                [for]="'regularHours-' + i"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Heures</label
              >
              <input
                type="text"
                [id]="'regularHours-' + i"
                formControlName="regularHours"
                class="w-full px-3 py-2 border rounded-md bg-gray-50 shadow-sm"
                readonly
              />
            </div>

            <!-- Notes -->
          </div>
        </div>
      </div>
    </div>

    <!-- Résumé des heures -->
    <div *ngIf="workdays.length > 0" class="mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="flex justify-between items-center">
        <p class="font-medium">Total des heures:</p>
        <p class="font-bold text-lg">
          {{ getTotalHours() | number : "1.2-2" }} h
        </p>
      </div>
    </div>

    <div class="mt-6 mb-8">
      <button
        type="button"
        class="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
        (click)="addWorkday()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        Ajouter un jour de travail
      </button>
    </div>

    <div class="flex justify-end space-x-3">
      <button
        type="button"
        class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        (click)="onCancel()"
      >
        Annuler
      </button>
      <button
        type="submit"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        [disabled]="workdayForm.invalid || isSubmitting"
        [ngClass]="{
          'opacity-50 cursor-not-allowed': workdayForm.invalid || isSubmitting
        }"
      >
        <span *ngIf="isSubmitting" class="inline-block mr-2">
          <svg
            class="animate-spin h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
        Enregistrer les jours de travail
      </button>
    </div>
  </form>
</div>
