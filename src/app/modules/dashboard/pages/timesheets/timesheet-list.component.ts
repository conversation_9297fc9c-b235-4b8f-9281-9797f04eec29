// timesheet-list.component.ts
import { Component, Input, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';
import { CommonModule } from '@angular/common';
import {
  Timesheet,
  TimesheetService,
  WorkDay,
} from 'src/app/core/services/timesheet/timesheet.service';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { TimesheetFormComponent } from './components/timesheet-create-modal/timesheet-create-modal.component';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-timesheet-list',
  templateUrl: './timesheet-list.component.html',
  imports: [
    TimesheetFormComponent,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
  ],
})
export class TimesheetListComponent implements OnInit {
  @Input() employeeId!: string;

  timesheets: Timesheet[] = [];
  employees: EmployeeData[] = [];
  selectedTimesheet: Timesheet | null = null;
  selectedWorkDays: WorkDay[] = [];
  filterForm: FormGroup;
  showModal = false;
  lastUpdateMinutes = 0;
  isLoading = false;
  company!: Company;
  isEditing = false;
  currentCompanyId!: string;

  constructor(
    private timesheetService: TimesheetService,
    private employeeService: EmployeeService,
    private store: Store<AppState>,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      employeeId: [''],
      startDate: [''],
      endDate: [''],
    });

    // Simuler la mise à jour du timestamp
    this.lastUpdateMinutes = Math.floor(Math.random() * 60);

    setInterval(() => {
      this.lastUpdateMinutes++;
    }, 60000);
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
      this.currentCompanyId = company.id!;
    });

    this.loadAllTimesheets();
    this.loadEmployees();
  }

  loadAllTimesheets(): void {
    this.isLoading = true;

    // Nous devons charger les timesheets pour tous les employés
    // Cette méthode est une solution temporaire - idéalement, le backend fournirait un endpoint pour récupérer toutes les timesheets avec filtrage
    if (this.company.id!) {
      if (this.employeeId || this.filterForm.get('employeeId')?.value) {
        const employeeId =
          this.employeeId || this.filterForm.get('employeeId')?.value;
        this.timesheetService
          .getTimesheets(this.company.id!, employeeId)
          .subscribe({
            next: (data) => {
              this.timesheets = this.filterTimesheets(data);
              this.isLoading = false;
            },
            error: (error) => {
              console.error('Erreur lors du chargement des timesheets:', error);
              this.isLoading = false;
            },
          });
      } else {
        this.timesheetService.getTimesheets(this.company.id!).subscribe({
          next: (data) => {
            this.timesheets = this.filterTimesheets(data);
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Erreur lors du chargement des timesheets:', error);
            this.isLoading = false;
          },
        });
      }
    }
  }

  loadTimesheetsForEmployee(employeeId: string): void {
    this.timesheetService.getEmployeeTimesheets(employeeId).subscribe({
      next: (data) => {
        this.timesheets = this.filterTimesheets(data);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des timesheets:', error);
        this.isLoading = false;
      },
    });
  }

  filterTimesheets(timesheets: Timesheet[]): Timesheet[] {
    const startDate = this.filterForm.get('startDate')?.value;
    const endDate = this.filterForm.get('endDate')?.value;

    if (!startDate && !endDate) {
      return timesheets;
    }

    return timesheets.filter((timesheet) => {
      const periodStart = new Date(timesheet.periodStart);
      const periodEnd = new Date(timesheet.periodEnd);

      if (startDate && !endDate) {
        return periodEnd >= new Date(startDate);
      }

      if (!startDate && endDate) {
        return periodStart <= new Date(endDate);
      }

      // Les deux dates sont définies
      return (
        periodStart <= new Date(endDate) && periodEnd >= new Date(startDate)
      );
    });
  }

  selectTimesheet(timesheet: Timesheet): void {
    this.selectedTimesheet = timesheet;
    // Charger les workdays pour cette timesheet
    this.loadWorkDays(timesheet.id);
  }

  loadWorkDays(timesheetId: string): void {
    // Idéalement, vous auriez un endpoint pour récupérer les workdays d'une timesheet
    // Cette fonctionnalité n'est pas disponible dans le service fourni,
    // mais vous devrez l'implémenter côté serveur

    // Pour simuler le comportement :
    this.selectedWorkDays = [
      {
        id: '',
        date: new Date().toISOString(),
        regularHours: 8,
        overtimeHours: 1,
        undertimeHours: 0,
        arrivalTime: '12:00',
        departureTime: '16:00',
      },
      {
        id: '',
        date: new Date(Date.now() - 86400000).toISOString(),
        regularHours: 7.5,
        overtimeHours: 0,
        undertimeHours: 0.5,
        arrivalTime: '12:00',
        departureTime: '16:00',
      },
    ];
  }

  applyFilters(): void {
    this.loadAllTimesheets();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadAllTimesheets();
  }

  openModal(isEditing: boolean): void {
    this.isEditing = isEditing;
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
  }

  saveTimesheet(formData: any): void {
    console.log(formData);

    const timesheetData: Partial<Timesheet> = {
      employeeId: formData.employeeId,
      periodStart: formData.periodStart,
      periodEnd: formData.periodEnd,
      totalRegularHours: formData.totalRegularHours,
      totalOvertimeHours: formData.totalOvertimeHours,
      totalUndertimeHours: formData.totalUndertimeHours,
      workDays: formData.workdays,
    };

    if (this.selectedTimesheet) {
      // Mettre à jour la feuille de temps existante
      this.timesheetService
        .updateTimesheet(this.selectedTimesheet.id, timesheetData)
        .subscribe({
          next: (updated) => {
            const index = this.timesheets.findIndex((t) => t.id === updated.id);
            if (index !== -1) {
              this.timesheets[index] = updated;
            }
            this.closeModal();
            this.lastUpdateMinutes = 0;
          },
          error: (error) => {
            console.error(
              'Erreur lors de la mise à jour de la timesheet:',
              error
            );
          },
        });
    } else {
      // Créer une nouvelle feuille de temps
      this.timesheetService
        .createTimesheet(this.currentCompanyId, timesheetData as any)
        .subscribe({
          next: (createdTimesheet) => {
            this.timesheets.push(createdTimesheet);
            this.closeModal();
            this.lastUpdateMinutes = 0;
          },
          error: (error) => {
            console.error('Erreur lors de la création de la timesheet:', error);
          },
        });
    }
  }

  deleteTimesheet(): void {
    if (!this.selectedTimesheet) return;

    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer cette feuille de temps (ID: ${this.selectedTimesheet.id})?`
      )
    ) {
      this.timesheetService
        .deleteTimesheet(this.selectedTimesheet.id)
        .subscribe({
          next: () => {
            this.timesheets = this.timesheets.filter(
              (t) => t.id !== this.selectedTimesheet?.id
            );
            this.selectedTimesheet = null;
            this.lastUpdateMinutes = 0;
          },
          error: (error) => {
            console.error(
              'Erreur lors de la suppression de la timesheet:',
              error
            );
          },
        });
    }
  }

  recalculateTimesheetTotals(): void {
    if (!this.selectedTimesheet || !this.selectedWorkDays.length) return;

    // Calculer les totaux depuis les workdays
    let totalRegularHours = 0;
    let totalOvertimeHours = 0;
    let totalUndertimeHours = 0;

    this.selectedWorkDays.forEach((workday) => {
      totalRegularHours += workday.regularHours;
      totalOvertimeHours += workday.overtimeHours;
      totalUndertimeHours += workday.undertimeHours;
    });

    // Mettre à jour la timesheet
    const updates: Partial<Timesheet> = {
      totalRegularHours,
      totalOvertimeHours,
      totalUndertimeHours,
    };

    this.timesheetService
      .updateTimesheet(this.selectedTimesheet.id, updates)
      .subscribe({
        next: (updated) => {
          const index = this.timesheets.findIndex((t) => t.id === updated.id);
          if (index !== -1) {
            this.timesheets[index] = updated;
            this.selectedTimesheet = updated;
          }
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour des totaux:', error);
        },
      });
  }
  loadEmployees(): void {
    if (this.employeeId) {
      return;
    }
    this.employeeService
      .getEmployees(this.company.id!)
      .subscribe((data: any) => {
        this.employees = data.data || data;
      });
  }
  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employé #${employeeId}`;
  }
}
