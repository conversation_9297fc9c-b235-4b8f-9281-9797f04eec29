<!-- company-profile.component.html -->
<div
  class="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900"
  *ngIf="company"
>
  <!-- En-tête du profil -->
  <div class="bg-background rounded-xl p-6 mb-6 transition-all hover:shadow-lg">
    <div
      class="flex flex-col md:flex-row items-start md:items-center justify-between"
    >
      <div class="flex items-center mb-4 md:mb-0">
        <div class="relative group">
          <div
            class="w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center mr-5 shadow-md overflow-hidden"
          >
            <span class="text-white text-2xl font-bold">
              {{ company.companyName?.charAt(0)
              }}{{ company.companyName?.charAt(0) || "" }}
            </span>
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
            {{ company.companyName }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            {{ company.industry }}
          </p>
          <div class="flex items-center mt-1">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span>
              Active
            </span>
          </div>
        </div>
      </div>
      <div class="flex space-x-2">
        <button
          (click)="toggleEditMode()"
          [ngClass]="
            isEditMode
              ? 'px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 shadow-sm transition-all hover:shadow dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 focus:outline-none'
              : 'rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50'
          "
        >
          {{ isEditMode ? "Annuler" : "Modifier le profil" }}
        </button>
        <button
          *ngIf="isEditMode"
          (click)="saveCompany()"
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        >
          Enregistrer
        </button>
      </div>
    </div>
  </div>

  <!-- Onglets -->
  <div class="mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav
        class="-mb-px flex space-x-8 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600"
      >
        <a
          *ngFor="let tab of tabs"
          (click)="activeTab = tab.id"
          [ngClass]="{
            'border-blue-500 text-blue-600 dark:text-blue-400':
              activeTab === tab.id,
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600':
              activeTab !== tab.id
          }"
          class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer transition-colors flex items-center"
        >
          <span class="flex items-center">
            <span class="mr-2" [innerHTML]="getTabIcon(tab.id)"></span>
            {{ tab.name }}
          </span>
        </a>
      </nav>
    </div>
  </div>

  <!-- Contenu des onglets -->
  <div [ngSwitch]="activeTab" class="transition-opacity duration-300">
    <!-- Onglet Informations générales -->
    <div *ngSwitchCase="'general'" class="bg-background rounded-xl p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
          Informations générales
        </h2>
        <div class="flex space-x-2" *ngIf="!isEditMode">
          <button
            (click)="toggleEditMode()"
            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
          </button>
        </div>
      </div>

      <div *ngIf="!isEditMode" class="animate-fade-in">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Nom officiel
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ company.officialName }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Secteur d'activité
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ company.industry }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Numéro d'identification fiscale
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ company.taxIdentificationNumber }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Email
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ company.email }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Téléphone
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ company.phoneNumbers?.join(", ") }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Site web
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ company.website || "Non renseigné" }}
            </p>
          </div>
        </div>

        <!-- Adresse -->
        <div class="mt-6">
          <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-3">
            Adresse
          </h3>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <div *ngIf="company.address; else noAddress">
              <p class="text-gray-800 dark:text-white">
                {{ company.address.street }}
              </p>
              <p class="text-gray-800 dark:text-white">
                {{ company.address.postalCode }} {{ company.address.city }}
              </p>
              <p class="text-gray-800 dark:text-white">
                {{ company.address.country }}
              </p>
            </div>
            <ng-template #noAddress>
              <p class="text-gray-500 italic">Aucune adresse renseignée</p>
            </ng-template>
          </div>
        </div>

        <!-- Description -->
        <div class="mt-6">
          <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-3">
            Description
          </h3>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p class="text-gray-800 dark:text-white">
              {{ company.description || "Aucune description renseignée" }}
            </p>
          </div>
        </div>
      </div>

      <!-- Mode édition -->
      <div *ngIf="isEditMode" [formGroup]="editForm" class="animate-fade-in">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Nom de l'entreprise</label
            >
            <input
              type="text"
              formControlName="companyName"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Nom officiel</label
            >
            <input
              type="text"
              formControlName="officialName"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Secteur d'activité</label
            >
            <input
              type="text"
              formControlName="industry"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Numéro fiscal</label
            >
            <input
              type="text"
              formControlName="taxIdentificationNumber"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Email</label
            >
            <input
              type="email"
              formControlName="email"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Téléphone</label
            >
            <input
              type="tel"
              formControlName="phoneNumbers"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Site web</label
            >
            <input
              type="url"
              formControlName="website"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <!-- Adresse -->
        <div class="mt-6">
          <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-3">
            Adresse
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="mb-4">
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Rue</label
              >
              <input
                type="text"
                formControlName="street"
                class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div class="mb-4">
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Ville</label
              >
              <input
                type="text"
                formControlName="city"
                class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div class="mb-4">
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Code postal</label
              >
              <input
                type="text"
                formControlName="postalCode"
                class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div class="mb-4">
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Pays</label
              >
              <input
                type="text"
                formControlName="country"
                class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
        </div>

        <!-- Description -->
        <div class="mt-6">
          <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-3">
            Description
          </h3>
          <textarea
            formControlName="description"
            rows="4"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          ></textarea>
        </div>

        <div class="flex justify-end space-x-2 mt-6">
          <button
            (click)="toggleEditMode()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Annuler
          </button>
          <button
            (click)="saveCompany()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Enregistrer
          </button>
        </div>
      </div>
    </div>

    <!-- Onglet Paramètres fiscaux -->
    <div *ngSwitchCase="'tax'" class="bg-background rounded-xl p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
          Paramètres fiscaux
        </h2>
        <button
          (click)="toggleTaxEditMode()"
          [ngClass]="
            isTaxEditMode
              ? 'px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 shadow-sm transition-all hover:shadow dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 focus:outline-none'
              : 'rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50'
          "
        >
          {{ isTaxEditMode ? "Annuler" : "Modifier" }}
        </button>
      </div>

      <div *ngIf="!isTaxEditMode" class="animate-fade-in">
        <div *ngIf="company.taxSettings; else noTaxSettings">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Taux d'imposition sur le revenu
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.taxSettings.incomeTaxRate }}%
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Taux de sécurité sociale
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.taxSettings.socialSecurityRate }}%
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Taux d'assurance chômage
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.taxSettings.unEmploymentInsuranceRate }}%
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Taux d'assurance maladie
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.taxSettings.healthInsuranceRate }}%
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Taux de cotisation retraite
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.taxSettings.pensionContributionRate }}%
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Fréquence de paiement
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.taxSettings.taxPaymentFrequency }}
              </p>
            </div>
          </div>
        </div>
        <ng-template #noTaxSettings>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">Aucun paramètre fiscal défini</p>
            <button
              (click)="initializeTaxSettings()"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
            >
              Initialiser les paramètres fiscaux
            </button>
          </div>
        </ng-template>
      </div>

      <!-- Mode édition des paramètres fiscaux -->
      <div
        *ngIf="isTaxEditMode && company.taxSettings"
        [formGroup]="taxForm"
        class="animate-fade-in"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Taux d'imposition sur le revenu (%)</label
            >
            <input
              type="number"
              step="0.01"
              formControlName="incomeTaxRate"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Taux de sécurité sociale (%)</label
            >
            <input
              type="number"
              step="0.01"
              formControlName="socialSecurityRate"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Taux d'assurance chômage (%)</label
            >
            <input
              type="number"
              step="0.01"
              formControlName="unEmploymentInsuranceRate"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Taux d'assurance maladie (%)</label
            >
            <input
              type="number"
              step="0.01"
              formControlName="healthInsuranceRate"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Taux de cotisation retraite (%)</label
            >
            <input
              type="number"
              step="0.01"
              formControlName="pensionContributionRate"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Fréquence de paiement</label
            >
            <select
              formControlName="taxPaymentFrequency"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="MONTHLY">Mensuel</option>
              <option value="QUARTERLY">Trimestriel</option>
              <option value="ANNUALLY">Annuel</option>
            </select>
          </div>
        </div>

        <div class="flex justify-end space-x-2 mt-6">
          <button
            (click)="toggleTaxEditMode()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Annuler
          </button>
          <button
            (click)="saveTaxSettings()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Enregistrer
          </button>
        </div>
      </div>
    </div>

    <!-- Onglet Configuration de paie -->
    <div *ngSwitchCase="'payroll'" class="bg-background rounded-xl p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
          Configuration de paie
        </h2>
        <button
          (click)="togglePayrollEditMode()"
          [ngClass]="
            isPayrollEditMode
              ? 'px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 shadow-sm transition-all hover:shadow dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 focus:outline-none'
              : 'rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50'
          "
        >
          {{ isPayrollEditMode ? "Annuler" : "Modifier" }}
        </button>
      </div>

      <div *ngIf="!isPayrollEditMode" class="animate-fade-in">
        <div *ngIf="company.payrollConfiguration; else noPayrollConfig">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Cycle de paie
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.payrollConfiguration.payrollCycle }}
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Jour de paiement
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.payrollConfiguration.paymentDay }}
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Multiplicateur d'heures supplémentaires
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.payrollConfiguration.overtimeMultiplier }}x
              </p>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <p
                class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
              >
                Type de bonus
              </p>
              <p class="text-gray-800 dark:text-white font-medium">
                {{ company.payrollConfiguration.bonusType || "Non défini" }}
              </p>
            </div>
          </div>
        </div>
        <ng-template #noPayrollConfig>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">
              Aucune configuration de paie définie
            </p>
            <button
              (click)="initializePayrollConfig()"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
            >
              Initialiser la configuration
            </button>
          </div>
        </ng-template>
      </div>

      <!-- Mode édition de la configuration de paie -->
      <div
        *ngIf="isPayrollEditMode && company.payrollConfiguration"
        [formGroup]="payrollForm"
        class="animate-fade-in"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Cycle de paie</label
            >
            <select
              formControlName="payrollCycle"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="MONTHLY">Mensuel</option>
              <option value="SEMI_MONTHLY">Semi-mensuel</option>
              <option value="BI_WEEKLY">Bi-hebdomadaire</option>
              <option value="WEEKLY">Hebdomadaire</option>
            </select>
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Jour de paiement</label
            >
            <input
              type="number"
              min="1"
              max="31"
              formControlName="paymentDay"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Multiplicateur d'heures supplémentaires</label
            >
            <input
              type="number"
              step="0.1"
              min="1"
              formControlName="overtimeMultiplier"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Type de bonus</label
            >
            <select
              formControlName="bonusType"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option value="PERCENTAGE_OF_SALARY">
                Pourcentage du salaire
              </option>
              <option value="FIXED_AMOUNT">Montant fixe</option>
              <option value="PERFORMANCE_BASED">Basé sur la performance</option>
            </select>
          </div>
        </div>

        <div class="flex justify-end space-x-2 mt-6">
          <button
            (click)="togglePayrollEditMode()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Annuler
          </button>
          <button
            (click)="savePayrollConfig()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Enregistrer
          </button>
        </div>
      </div>
    </div>

    <!-- Onglet Employés -->
    <div *ngSwitchCase="'employees'" class="bg-background rounded-xl p-6">
      <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
        Employés ({{ company.employees?.length || 0 }})
      </h2>

      <div
        *ngIf="!company.employees || company.employees.length === 0"
        class="text-center py-8"
      >
        <p class="text-gray-500">Aucun employé enregistré</p>
      </div>

      <div
        *ngIf="company.employees && company.employees.length > 0"
        class="overflow-x-auto"
      >
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Nom
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Poste
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Département
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Date d'embauche
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-background divide-y divide-gray-200">
            <tr *ngFor="let employee of company.employees">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div
                    class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center"
                  >
                    <span class="text-gray-600">
                      {{ employee.user?.profile?.firstName?.charAt(0)
                      }}{{ employee.user?.profile?.lastName?.charAt(0) }}
                    </span>
                  </div>
                  <div class="ml-4">
                    <div
                      class="text-sm font-medium text-gray-900 dark:text-white"
                    >
                      {{ employee.user?.profile?.firstName }}
                      {{ employee.user?.profile?.lastName }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ employee.user?.email }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ employee.position?.title }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ employee.department?.name }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ employee.hireDate | date : "dd/MM/yyyy" }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a
                  href="#"
                  class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                  >Voir</a
                >
                <a
                  href="#"
                  class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                  >Modifier</a
                >
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Onglet Départements -->
    <div *ngSwitchCase="'departments'" class="bg-background rounded-xl p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
          Départements ({{ company.departments?.length || 0 }})
        </h2>
        <button
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
          (click)="openAddDepartmentModal()"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Ajouter
        </button>
      </div>

      <div
        *ngIf="!company.departments || company.departments.length === 0"
        class="text-center py-8"
      >
        <p class="text-gray-500">Aucun département créé</p>
      </div>

      <div
        *ngIf="company.departments && company.departments.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        <div
          *ngFor="let department of company.departments"
          class="border rounded-lg overflow-hidden hover:shadow-md transition-shadow"
        >
          <div
            class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b flex justify-between items-center"
          >
            <h3 class="font-medium text-gray-800 dark:text-white">
              {{ department.name }}
            </h3>
            <div class="flex space-x-2">
              <button
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                (click)="editDepartment(department)"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              </button>
              <button
                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                (click)="deleteDepartment(department.id)"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div class="p-4">
            <p class="text-gray-600 dark:text-gray-300 mb-3">
              {{ department.description || "Aucune description" }}
            </p>
            <div class="flex items-center text-sm text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              {{ getEmployeeCountForDepartment(department.id) }} employé(s)
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Documents -->
    <div *ngSwitchCase="'documents'" class="bg-background rounded-xl p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
          Documents ({{ company.documents?.length || 0 }})
        </h2>
        <button
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
          (click)="openUploadDocumentModal()"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
          Téléverser
        </button>
      </div>

      <div
        *ngIf="!company.documents || company.documents.length === 0"
        class="text-center py-8"
      >
        <p class="text-gray-500">Aucun document disponible</p>
      </div>

      <div
        *ngIf="company.documents && company.documents.length > 0"
        class="space-y-4"
      >
        <div
          *ngFor="let document of company.documents"
          class="border rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start">
            <div
              class="flex-shrink-0 h-10 w-10 rounded bg-gray-100 flex items-center justify-center mr-4"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex justify-between items-start">
                <div>
                  <h3
                    class="text-sm font-medium text-gray-900 dark:text-white truncate"
                  >
                    {{ document.name }}
                  </h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ document.type }}
                  </p>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{
                  document.uploadedAt | date : "dd/MM/yyyy"
                }}</span>
              </div>
              <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                {{ document.description || "Aucune description" }}
              </p>
              <div class="mt-2 flex space-x-3">
                <a
                  [href]="document.url"
                  target="_blank"
                  class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-3 w-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  Voir
                </a>
                <a
                  [href]="document.url"
                  download
                  class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-3 w-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    />
                  </svg>
                  Télécharger
                </a>
                <button
                  (click)="deleteDocument(document.id)"
                  class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-red-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-gray-700 dark:text-red-400 dark:border-gray-600 dark:hover:bg-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-3 w-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modals -->
<!-- <app-modal [isOpen]="showDepartmentModal" (close)="closeDepartmentModal()">
    <div class="p-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white mb-4">
        {{ editingDepartment ? 'Modifier département' : 'Ajouter un département' }}
      </h3>
      <form [formGroup]="departmentForm" (ngSubmit)="submitDepartmentForm()">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nom du département</label>
          <input
            type="text"
            formControlName="name"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
          <textarea
            formControlName="description"
            rows="3"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          ></textarea>
        </div>
        <div class="flex justify-end space-x-2 mt-6">
          <button
            type="button"
            (click)="closeDepartmentModal()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Annuler
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            {{ editingDepartment ? 'Modifier' : 'Ajouter' }}
          </button>
        </div>
      </form>
    </div>
  </app-modal> -->

<!-- <app-modal [isOpen]="showDocumentModal" (close)="closeDocumentModal()">
    <div class="p-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white mb-4">
        Téléverser un document
      </h3>
      <form [formGroup]="documentForm" (ngSubmit)="submitDocumentForm()">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nom du document</label>
          <input
            type="text"
            formControlName="name"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
          <select
            formControlName="type"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          >
            <option value="CONTRACT">Contrat</option>
            <option value="FINANCIAL">Document financier</option>
            <option value="LEGAL">Document légal</option>
            <option value="OTHER">Autre</option>
          </select>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
          <textarea
            formControlName="description"
            rows="3"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
          ></textarea>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fichier</label>
          <div class="mt-1 flex items-center">
            <input
              type="file"
              (change)="onFileSelected($event)"
              class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/30 dark:file:text-blue-400"
            />
          </div>
        </div>
        <div class="flex justify-end space-x-2 mt-6">
          <button
            type="button"
            (click)="closeDocumentModal()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Annuler
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Téléverser
          </button>
        </div>
      </form>
    </div>
  </app-modal> -->
