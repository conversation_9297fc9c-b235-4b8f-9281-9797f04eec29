<div
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4 transform transition-transform"
  >
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold text-gray-800 dark:text-white">
        {{ isEditingSalary ? "Modifier le salaire" : "Ajouter un salaire" }}
      </h3>
      <button
        (click)="closeSalaryModal()"
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        aria-label="Fermer la modal"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <form [formGroup]="salaryForm" (ngSubmit)="saveSalary()" novalidate>
      <div class="space-y-4">
        <div>
          <label
            for="employeeId"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Employé</label
          >
          <select
            id="employeeId"
            formControlName="employeeId"
            class="w-full px-3 py-2 border rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            [ngClass]="{
              'border-red-500': isFieldInvalid('employeeId')
            }"
          >
            <option value="">Sélectionner un employé</option>
            <option *ngFor="let employee of employees" [value]="employee.id">
              {{ employee.user?.profile?.firstName }}
              {{ employee.user?.profile?.lastName }}
            </option>
          </select>
          <p
            *ngIf="isFieldInvalid('employeeId')"
            class="mt-1 text-sm text-red-600"
          >
            L'employé est requis
          </p>
        </div>
        <!-- Champ Date d'effet -->
        <div>
          <label
            for="effectiveDate"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >Date d'effet</label
          >
          <input
            id="effectiveDate"
            type="date"
            formControlName="effectiveDate"
            class="w-full px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-700 dark:text-white"
            [ngClass]="{
              'border-red-500': isFieldInvalid('effectiveDate')
            }"
          />
          <p
            *ngIf="isFieldInvalid('effectiveDate')"
            class="mt-1 text-sm text-red-600"
          >
            La date d'effet est requise et doit être valide
          </p>
        </div>

        <!-- Champs de salaire -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div *ngFor="let field of salaryFields">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >{{ field.label }}</label
            >
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                *ngIf="field.currency"
              >
                <span class="text-gray-500 dark:text-gray-400">$</span>
              </div>
              <input
                [id]="field.name"
                [type]="field.type"
                [formControlName]="field.name"
                [step]="field.step"
                [min]="field.min"
                class="w-full px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-700 dark:text-white"
                [ngClass]="{
                  'pl-8': field.currency,
                  'border-red-500': isFieldInvalid(field.name)
                }"
              />
              <p
                *ngIf="isFieldInvalid(field.name)"
                class="mt-1 text-sm text-red-600"
              >
                {{ getErrorMessage(field.name) }}
              </p>
            </div>
          </div>
        </div>

        <!-- Résumé -->
        <div class="space-y-2 pt-2">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400"
              >Salaire de base:</span
            >
            <span class="font-medium">{{
              salaryForm.value.baseSalary | currency
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400"
              >Indemnités:</span
            >
            <span class="font-medium">{{ totalAllowances | currency }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Prime:</span>
            <span class="font-medium">{{
              salaryForm.value.bonus | currency
            }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400"
              >Heures supplémentaires:</span
            >
            <span class="font-medium">{{ overtimeAmount | currency }}</span>
          </div>
          <div class="border-t border-gray-200 dark:border-gray-700 my-2"></div>
          <div class="flex justify-between font-semibold">
            <span class="text-gray-800 dark:text-gray-200">Total estimé:</span>
            <span class="text-blue-600 dark:text-blue-400">{{
              estimatedTotal | currency
            }}</span>
          </div>
        </div>
      </div>

      <div class="flex justify-end space-x-2 mt-6">
        <button
          type="button"
          (click)="closeSalaryModal()"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          Annuler
        </button>
        <button
          type="submit"
          [disabled]="salaryForm.invalid"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ isEditingSalary ? "Modifier" : "Ajouter" }}
        </button>
      </div>
    </form>
  </div>
</div>
