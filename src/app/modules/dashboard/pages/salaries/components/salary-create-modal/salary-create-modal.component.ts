import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  AbstractControl,
} from '@angular/forms';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';
import {
  Salary,
  CreateSalaryDto,
  UpdateSalaryDto,
} from 'src/app/core/services/salary/salary.service';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-salary-create-modal',
  templateUrl: './salary-create-modal.component.html',
  imports: [CommonModule, ReactiveFormsModule],
  standalone: true,
})
export class SalaryModalComponent implements OnInit {
  @Input() employees: EmployeeData[] = [];
  @Input() isEditingSalary: boolean = false;
  @Input() existingSalary: Salary | null = null;
  @Input() employeeId: string | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<CreateSalaryDto | UpdateSalaryDto>();

  salaryFields = [
    {
      name: 'baseSalary',
      label: 'Salaire de base',
      type: 'number',
      step: '100',
      min: '0',
      currency: true,
      required: true,
    },
    {
      name: 'housingAllowance',
      label: 'Indemnité de logement',
      type: 'number',
      step: '10',
      min: '0',
      currency: true,
      required: false,
    },
    {
      name: 'transportAllowance',
      label: 'Indemnité de transport',
      type: 'number',
      step: '10',
      min: '0',
      currency: true,
      required: false,
    },
    {
      name: 'bonus',
      label: 'Prime',
      type: 'number',
      step: '10',
      min: '0',
      currency: true,
      required: false,
    },
    {
      name: 'overtimeHours',
      label: 'Heures supplémentaires',
      type: 'number',
      step: '1',
      min: '0',
      currency: false,
      required: false,
    },
    {
      name: 'overtimeRate',
      label: 'Taux des heures sup',
      type: 'number',
      step: '0.1',
      min: '0',
      currency: true,
      required: false,
    },
  ];

  salaryForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initForm();

    if (this.existingSalary && this.isEditingSalary) {
      this.patchFormWithExistingSalary();
    }
  }

  private initForm(): void {
    this.salaryForm = this.fb.group({
      employeeId: [this.employeeId, Validators.required],
      effectiveDate: [
        this.getTodayDate(),
        [Validators.required, this.dateValidator],
      ],
      baseSalary: [0, [Validators.required, Validators.min(0)]],
      housingAllowance: [0, [Validators.min(0)]],
      transportAllowance: [0, [Validators.min(0)]],
      bonus: [0, [Validators.min(0)]],
      overtimeHours: [0, [Validators.min(0)]],
      overtimeRate: [0, [Validators.min(0)]],
    });
  }

  private patchFormWithExistingSalary(): void {
    if (!this.existingSalary) return;

    this.salaryForm.patchValue({
      employeeId: this.existingSalary.employeeId,
      effectiveDate: formatDate(
        this.existingSalary.effectiveDate,
        'yyyy-MM-dd',
        'en'
      ),
      baseSalary: this.existingSalary.baseSalary,
      housingAllowance: this.existingSalary.housingAllowance || 0,
      transportAllowance: this.existingSalary.transportAllowance || 0,
      bonus: this.existingSalary.bonus || 0,
      overtimeHours: this.existingSalary.overtimeHours || 0,
      overtimeRate: this.existingSalary.overtimeRate || 0,
    });
  }

  private getTodayDate(): string {
    return formatDate(new Date(), 'yyyy-MM-dd', 'en');
  }

  private dateValidator(
    control: AbstractControl
  ): { [key: string]: boolean } | null {
    return control.value && isNaN(new Date(control.value).getTime())
      ? { invalidDate: true }
      : null;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.salaryForm.get(fieldName);
    return !!field && field.invalid && (field.dirty || field.touched);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.salaryForm.get(fieldName);
    if (!field || !field.errors) return '';

    if (field.errors['required']) return 'Ce champ est requis';
    if (field.errors['min'])
      return `La valeur doit être supérieure ou égale à ${field.errors['min'].min}`;
    if (field.errors['invalidDate']) return 'Date invalide';

    return 'Valeur invalide';
  }

  closeSalaryModal(): void {
    this.close.emit();
  }

  saveSalary(): void {
    if (this.salaryForm.invalid) {
      this.salaryForm.markAllAsTouched();
      return;
    }

    const formValue = this.salaryForm.value;
    const salaryData = {
      ...formValue,
      // Conversion explicite des valeurs en nombres
      baseSalary: Number(formValue.baseSalary),
      housingAllowance: Number(formValue.housingAllowance || 0),
      transportAllowance: Number(formValue.transportAllowance || 0),
      bonus: Number(formValue.bonus || 0),
      overtimeHours: Number(formValue.overtimeHours || 0),
      overtimeRate: Number(formValue.overtimeRate || 0),
      effectiveDate: new Date(formValue.effectiveDate).toISOString(),
    };

    this.save.emit(salaryData);
    this.closeSalaryModal();
  }

  // Méthodes de calcul avec conversion sécurisée
  get totalAllowances(): number {
    const housing = Number(this.salaryForm.value.housingAllowance || 0);
    const transport = Number(this.salaryForm.value.transportAllowance || 0);
    return housing + transport;
  }

  get overtimeAmount(): number {
    const hours = Number(this.salaryForm.value.overtimeHours || 0);
    const rate = Number(this.salaryForm.value.overtimeRate || 0);
    return hours * rate;
  }

  get estimatedTotal(): number {
    const base = Number(this.salaryForm.value.baseSalary || 0);
    const bonus = Number(this.salaryForm.value.bonus || 0);
    return base + this.totalAllowances + bonus + this.overtimeAmount;
  }
}
