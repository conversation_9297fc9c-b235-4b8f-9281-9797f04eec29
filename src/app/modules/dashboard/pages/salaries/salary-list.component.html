<div class="flex flex-col rounded-xl bg-background px-6 py-6">
  <!-- Header avec titre et bouton d'ajout -->
  <div class="mb-6 flex items-center justify-between">
    <div class="flex items-center gap-4">
      <div class="flex flex-col">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">
          {{ employeeId ? "Historique des salaires" : "Liste des salaires" }}
        </h3>
        <span class="text-xs text-gray-500 dark:text-gray-400">
          Mise à jour il y a {{ lastUpdateMinutes }} minutes
        </span>
      </div>
      <button
        (click)="loadAllSalaries()"
        class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
        title="Recharger les données"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      </button>
    </div>
    <div class="flex items-center space-x-2">
      <ng-container *ngIf="selectedSalary">
        <button
          class="rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50"
          (click)="openModal(true)"
        >
          Modifier
        </button>

        <button
          class="rounded-lg bg-red-50 px-4 py-2 text-sm font-medium text-red-600 transition hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"
          (click)="deleteSalary()"
        >
          Supprimer
        </button>
      </ng-container>
      <button
        *ngIf="!selectedSalary"
        class="rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        (click)="openModal(false)"
      >
        Ajouter un salaire
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div class="mb-6 bg-background p-4 rounded-lg">
    <form
      [formGroup]="filterForm"
      (ngSubmit)="applyFilters()"
      class="flex flex-wrap gap-4 items-end"
    >
      <div class="flex flex-col" *ngIf="!employeeId">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Employé
        </label>
        <select
          formControlName="employeeId"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        >
          <option value="">Tous les employés</option>
          <option *ngFor="let employee of employees" [value]="employee.id">
            {{ getEmployeeName(employee.id) }}
          </option>
        </select>
      </div>

      <div class="flex flex-col">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Date d'effet (début)
        </label>
        <input
          type="date"
          formControlName="startDate"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <div class="flex flex-col">
        <label
          class="mb-1 text-xs font-medium text-gray-700 dark:text-gray-300"
        >
          Date d'effet (fin)
        </label>
        <input
          type="date"
          formControlName="endDate"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
        />
      </div>

      <div class="flex gap-2">
        <button
          type="submit"
          class="rounded-lg bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
        >
          Filtrer
        </button>
        <button
          type="button"
          (click)="resetFilters()"
          class="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-600 transition hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600"
        >
          Réinitialiser
        </button>
      </div>
    </form>
  </div>

  <!-- Tableau des salaires -->
  <div
    *ngIf="!isLoading"
    class="relative overflow-x-auto rounded-lg border border-gray-100 dark:border-gray-700"
  >
    <table class="w-full table-auto">
      <thead
        class="bg-background text-xs uppercase text-foreground dark:text-gray-300"
      >
        <tr>
          <th class="px-4 py-3 text-left" *ngIf="!employeeId">N°</th>
          <th class="px-4 py-3 text-left" *ngIf="!employeeId">Employé</th>
          <th class="px-4 py-3 text-left">Date d'effet</th>
          <th class="px-4 py-3 text-right">Salaire de base</th>
          <th class="px-4 py-3 text-right">Ind. logement</th>
          <th class="px-4 py-3 text-right">Ind. transport</th>
          <th class="px-4 py-3 text-right">Bonus</th>
          <th class="px-4 py-3 text-right">Taux heures supp.</th>
          <th class="px-4 py-3 text-right">Total</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let salary of salaries; let odd = odd"
          (click)="selectSalary(salary)"
          [ngClass]="{
            'bg-blue-100 dark:bg-blue-700 text-blue-900 dark:text-blue-200 font-semibold':
              selectedSalary === salary,
            'bg-gray-50 dark:bg-gray-800/50': odd && selectedSalary !== salary,
            'bg-white dark:bg-gray-800': !odd && selectedSalary !== salary
          }"
          class="border-b border-gray-100 transition hover:bg-blue-50 dark:border-gray-700 dark:hover:bg-gray-700/50"
        >
          <td class="px-4 py-3" *ngIf="!employeeId">{{ salary.id }}</td>
          <td class="px-4 py-3" *ngIf="!employeeId">
            {{ getEmployeeName(salary.employeeId) }}
          </td>
          <td class="px-4 py-3">
            {{ salary.effectiveDate | date : "dd/MM/yyyy" }}
          </td>
          <td class="px-4 py-3 text-right">
            {{ salary.baseSalary | currency }}
          </td>
          <td class="px-4 py-3 text-right">
            {{ salary.housingAllowance | currency }}
          </td>
          <td class="px-4 py-3 text-right">
            {{ salary.transportAllowance | currency }}
          </td>
          <td class="px-4 py-3 text-right">
            {{ salary.bonus | currency }}
          </td>
          <td class="px-4 py-3 text-right">{{ salary.overtimeRate }}x</td>
          <td class="px-4 py-3 text-right font-medium">
            {{ calculateTotalSalary(salary) | currency }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Message si aucune donnée -->
  <div
    *ngIf="!isLoading && salaries.length === 0"
    class="flex justify-center items-center py-8 text-gray-500 dark:text-gray-400"
  >
    Aucun salaire trouvé.
  </div>
</div>

<app-salary-create-modal
  *ngIf="showModal"
  [isEditingSalary]="isEditing"
  [existingSalary]="selectedSalary"
  [employees]="employees"
  [employeeId]="employeeId!"
  (close)="closeModal()"
  (save)="saveSalary($event)"
></app-salary-create-modal>
