import { Routes } from '@angular/router';

export const TASKS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./task-list/task-list.component').then(
        (m) => m.TaskListComponent
      ),
    data: {
      title: 'Toutes les tâches',
      description: "Gestion de toutes les tâches de l'entreprise",
    },
  },
  {
    path: 'my-tasks',
    loadComponent: () =>
      import('./my-tasks/my-tasks.component').then((m) => m.MyTasksComponent),
    data: {
      title: 'Mes tâches',
      description: 'Mes tâches assignées',
    },
  },
  {
    path: 'projects',
    loadComponent: () =>
      import('./task-projects/task-projects.component').then(
        (m) => m.TaskProjectsComponent
      ),
    data: {
      title: 'Projets',
      description: 'Gestion des projets et tâches associées',
    },
  },
];
