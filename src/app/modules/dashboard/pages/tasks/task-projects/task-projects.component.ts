import { Compo<PERSON>, On<PERSON>ni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  TaskService,
  TaskProject,
  CreateTaskProjectDto,
  UpdateTaskProjectDto,
} from '../../../../../core/services/task/task.service';
import { EmployeeService } from '../../../../../core/services/employee/employee.service';
import { EmployeeData } from '../../../models/employee';
import { PaginatedResult } from '../../../../../core/models/employee.model';

@Component({
  selector: 'app-task-projects',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './task-projects.component.html',
})
export class TaskProjectsComponent implements OnInit, OnDestroy {
  projects: TaskProject[] = [];
  employees: EmployeeData[] = [];

  // Modal
  showCreateModal = false;
  showEditModal = false;
  selectedProject: TaskProject | null = null;
  projectForm: FormGroup;

  // États
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;

  private subscriptions = new Subscription();
  private currentCompanyId = 'current-company-id'; // TODO: Récupérer depuis le store

  constructor(
    private taskService: TaskService,
    private employeeService: EmployeeService,
    private fb: FormBuilder
  ) {
    this.projectForm = this.fb.group({
      name: ['', Validators.required],
      description: [''],
      startDate: ['', Validators.required],
      endDate: [''],
      managerId: ['', Validators.required],
      teamMembers: [[]],
    });
  }

  ngOnInit(): void {
    this.loadEmployees();
    this.loadProjects();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadEmployees(): void {
    this.subscriptions.add(
      this.employeeService.getEmployees(this.currentCompanyId).subscribe({
        next: (employees: PaginatedResult<any>) => {
          this.employees = employees.data;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des employés:', error);
        },
      })
    );
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.error = null;

    this.subscriptions.add(
      this.taskService.getProjects(this.currentCompanyId).subscribe({
        next: (projects) => {
          this.projects = projects;
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des projets';
          this.isLoading = false;
          console.error('Erreur:', error);
        },
      })
    );
  }

  // Actions CRUD
  createProject(): void {
    this.selectedProject = null;
    this.projectForm.reset();
    this.showCreateModal = true;
  }

  editProject(project: TaskProject): void {
    this.selectedProject = project;
    this.projectForm.patchValue({
      name: project.name,
      description: project.description,
      startDate: this.formatDateForInput(project.startDate),
      endDate: project.endDate ? this.formatDateForInput(project.endDate) : '',
      managerId: project.managerId,
      teamMembers: project.teamMembers || [],
    });
    this.showEditModal = true;
  }

  deleteProject(project: TaskProject): void {
    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer le projet "${project.name}" ?`
      )
    ) {
      this.subscriptions.add(
        this.taskService.deleteProject(project.id).subscribe({
          next: () => {
            this.loadProjects();
          },
          error: (error) => {
            this.error = 'Erreur lors de la suppression';
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  onSubmitProject(): void {
    if (this.projectForm.valid) {
      this.isSubmitting = true;
      const formData = this.projectForm.value;

      const projectData: CreateTaskProjectDto | UpdateTaskProjectDto = {
        name: formData.name,
        description: formData.description,
        startDate: formData.startDate,
        endDate: formData.endDate,
        managerId: formData.managerId,
        teamMembers: formData.teamMembers,
      };

      if (!this.selectedProject) {
        (projectData as CreateTaskProjectDto).companyId = this.currentCompanyId;
      }

      const operation = this.selectedProject
        ? this.taskService.updateProject(
            this.selectedProject.id,
            projectData as UpdateTaskProjectDto
          )
        : this.taskService.createProject(projectData as CreateTaskProjectDto);

      this.subscriptions.add(
        operation.subscribe({
          next: () => {
            this.closeModals();
            this.loadProjects();
            this.isSubmitting = false;
          },
          error: (error) => {
            this.error = 'Erreur lors de la sauvegarde';
            this.isSubmitting = false;
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  // Modals
  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.selectedProject = null;
    this.projectForm.reset();
  }

  // Méthodes utilitaires
  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employé #${employeeId}`;
  }

  getStatusLabel(status: TaskProject['status']): string {
    const labels = {
      ACTIVE: 'Actif',
      COMPLETED: 'Terminé',
      ON_HOLD: 'En attente',
      CANCELLED: 'Annulé',
    };
    return labels[status];
  }

  getStatusClasses(status: TaskProject['status']): string {
    const classes = {
      ACTIVE:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      COMPLETED:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      ON_HOLD:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      CANCELLED: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    };
    return classes[status];
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  formatDateForInput(date: string): string {
    return new Date(date).toISOString().split('T')[0];
  }

  getProjectProgress(project: TaskProject): number {
    if (!project.tasks || project.tasks.length === 0) return 0;
    const completedTasks = project.tasks.filter(
      (task) => task.status === 'COMPLETED'
    ).length;
    return Math.round((completedTasks / project.tasks.length) * 100);
  }

  getTeamMembersNames(memberIds: string[]): string {
    if (!memberIds || memberIds.length === 0) return 'Aucun membre';
    const names = memberIds.map((id) => this.getEmployeeName(id));
    return names.join(', ');
  }

  /**
   * Compte le nombre de tâches terminées dans un projet
   */
  getCompletedTasksCount(project: any): number {
    if (!project.tasks || !Array.isArray(project.tasks)) {
      return 0;
    }
    return project.tasks.filter((t: any) => t.status === 'COMPLETED').length;
  }

  /**
   * Vérifie si un projet a des tâches
   */
  hasProjectTasks(project: any): boolean {
    return (
      project.tasks && Array.isArray(project.tasks) && project.tasks.length > 0
    );
  }
}
