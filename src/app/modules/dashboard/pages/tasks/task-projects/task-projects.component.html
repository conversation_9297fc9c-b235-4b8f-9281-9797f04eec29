<div class="p-6">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Projets
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Gérez vos projets et les tâches associées
        </p>
      </div>
      <button
        (click)="createProject()"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4v16m8-8H4"
          ></path>
        </svg>
        Nouveau projet
      </button>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
  >
    {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
  </div>

  <!-- Liste des projets -->
  <div
    *ngIf="!isLoading"
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
  >
    <div
      *ngFor="let project of projects"
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <!-- En-tête du projet -->
      <div class="flex items-start justify-between mb-4">
        <div class="flex-1">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {{ project.name }}
          </h3>
          <span
            [class]="
              'inline-flex px-2 py-1 text-xs font-semibold rounded-full ' +
              getStatusClasses(project.status)
            "
          >
            {{ getStatusLabel(project.status) }}
          </span>
        </div>

        <!-- Menu actions -->
        <div class="flex gap-2">
          <button
            (click)="editProject(project)"
            class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
            title="Modifier"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              ></path>
            </svg>
          </button>

          <button
            (click)="deleteProject(project)"
            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Supprimer"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Description -->
      <p
        *ngIf="project.description"
        class="text-gray-600 dark:text-gray-400 text-sm mb-4"
      >
        {{ project.description }}
      </p>

      <!-- Informations du projet -->
      <div class="space-y-3 mb-4">
        <!-- Manager -->
        <div class="flex items-center text-sm">
          <svg
            class="w-4 h-4 text-gray-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            ></path>
          </svg>
          <span class="text-gray-500 dark:text-gray-400">Manager:</span>
          <span class="ml-1 text-gray-900 dark:text-white">{{
            getEmployeeName(project.managerId)
          }}</span>
        </div>

        <!-- Dates -->
        <div class="flex items-center text-sm">
          <svg
            class="w-4 h-4 text-gray-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            ></path>
          </svg>
          <span class="text-gray-500 dark:text-gray-400">Début:</span>
          <span class="ml-1 text-gray-900 dark:text-white">{{
            formatDate(project.startDate)
          }}</span>
        </div>

        <div *ngIf="project.endDate" class="flex items-center text-sm">
          <svg
            class="w-4 h-4 text-gray-400 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            ></path>
          </svg>
          <span class="text-gray-500 dark:text-gray-400">Fin:</span>
          <span class="ml-1 text-gray-900 dark:text-white">{{
            formatDate(project.endDate)
          }}</span>
        </div>

        <!-- Équipe -->
        <div class="flex items-start text-sm">
          <svg
            class="w-4 h-4 text-gray-400 mr-2 mt-0.5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            ></path>
          </svg>
          <div>
            <span class="text-gray-500 dark:text-gray-400">Équipe:</span>
            <div class="ml-1 text-gray-900 dark:text-white text-xs">
              {{ getTeamMembersNames(project.teamMembers) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Progression -->
      <div class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >Progression</span
          >
          <span class="text-sm text-gray-900 dark:text-white"
            >{{ getProjectProgress(project) }}%</span
          >
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="h-2 rounded-full bg-blue-500 transition-all duration-300"
            [style.width.%]="getProjectProgress(project)"
          ></div>
        </div>
      </div>

      <!-- Statistiques des tâches -->
      <div
        *ngIf="hasProjectTasks(project)"
        class="text-sm text-gray-600 dark:text-gray-400"
      >
        {{ project.tasks?.length || 0 }} tâche(s) •
        {{ getCompletedTasksCount(project) }} terminée(s)
      </div>
      <div *ngIf="!hasProjectTasks(project)" class="text-sm text-gray-400">
        Aucune tâche assignée
      </div>
    </div>
  </div>

  <!-- Message si aucun projet -->
  <div *ngIf="!isLoading && projects.length === 0" class="text-center py-8">
    <svg
      class="mx-auto h-12 w-12 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
      ></path>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
      Aucun projet
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Commencez par créer votre premier projet.
    </p>
  </div>

  <!-- Modal de création/édition -->
  <div
    *ngIf="showCreateModal || showEditModal"
    class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
  >
    <div
      class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"
    >
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {{ selectedProject ? "Modifier le projet" : "Nouveau projet" }}
        </h3>

        <form [formGroup]="projectForm" (ngSubmit)="onSubmitProject()">
          <!-- Nom -->
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Nom du projet *</label
            >
            <input
              type="text"
              formControlName="name"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Nom du projet"
            />
          </div>

          <!-- Description -->
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Description</label
            >
            <textarea
              formControlName="description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Description du projet"
            ></textarea>
          </div>

          <!-- Dates -->
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Date de début *</label
              >
              <input
                type="date"
                formControlName="startDate"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Date de fin</label
              >
              <input
                type="date"
                formControlName="endDate"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <!-- Manager -->
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Manager *</label
            >
            <select
              formControlName="managerId"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Sélectionner un manager</option>
              <option *ngFor="let employee of employees" [value]="employee.id">
                {{ getEmployeeName(employee.id) }}
              </option>
            </select>
          </div>

          <!-- Boutons -->
          <div class="flex justify-end gap-3">
            <button
              type="button"
              (click)="closeModals()"
              class="px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Annuler
            </button>
            <button
              type="submit"
              [disabled]="projectForm.invalid || isSubmitting"
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md"
            >
              {{
                isSubmitting
                  ? "Sauvegarde..."
                  : selectedProject
                  ? "Modifier"
                  : "Créer"
              }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
