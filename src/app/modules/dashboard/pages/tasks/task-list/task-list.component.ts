import { <PERSON>mpo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import {
  TaskService,
  Task,
  TaskStatus,
  TaskPriority,
  TaskType,
  CreateTaskDto,
  UpdateTaskDto,
  TaskQueryOptions,
} from '../../../../../core/services/task/task.service';
import { EmployeeService } from '../../../../../core/services/employee/employee.service';
import { PaginatedResult } from '../../../../../core/models/employee.model';
import { EmployeeData } from '../../../models/employee';

@Component({
  selector: 'app-task-list',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './task-list.component.html',
})
export class TaskListComponent implements OnInit, OnDestroy {
  tasks: Task[] = [];
  filteredTasks: Task[] = [];
  employees: EmployeeData[] = [];

  // Filtres
  filterForm: FormGroup;
  searchTerm = '';

  // Modal
  showCreateModal = false;
  showEditModal = false;
  showDetailsModal = false;
  selectedTask: Task | null = null;
  taskForm: FormGroup;

  // États
  isLoading = false;
  isSubmitting = false;
  error: string | null = null;

  // Énumérations
  taskStatuses = Object.values(TaskStatus);
  taskPriorities = Object.values(TaskPriority);
  taskTypes = Object.values(TaskType);

  private subscriptions = new Subscription();

  constructor(
    private taskService: TaskService,
    private employeeService: EmployeeService,
    private fb: FormBuilder,
    private router: Router
  ) {
    this.filterForm = this.fb.group({
      status: [''],
      priority: [''],
      type: [''],
      assigneeId: [''],
      assignedById: [''],
    });

    this.taskForm = this.fb.group({
      title: ['', Validators.required],
      description: [''],
      priority: [TaskPriority.MEDIUM, Validators.required],
      type: [TaskType.GENERAL, Validators.required],
      dueDate: [''],
      startDate: [''],
      estimatedHours: [''],
      assigneeId: ['', Validators.required],
      tags: [[]],
    });
  }

  ngOnInit(): void {
    this.loadEmployees();
    this.loadTasks();
    this.setupFilterSubscription();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadEmployees(): void {
    // TODO: Récupérer le companyId depuis le store
    const companyId = 'current-company-id';
    this.subscriptions.add(
      this.employeeService.getEmployees(companyId).subscribe({
        next: (employees: PaginatedResult<any>) => {
          this.employees = employees.data;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des employés:', error);
        },
      })
    );
  }

  private loadTasks(): void {
    this.isLoading = true;
    this.error = null;

    const options: TaskQueryOptions = this.buildQueryOptions();

    this.subscriptions.add(
      this.taskService.getTasks(options).subscribe({
        next: (tasks) => {
          this.tasks = tasks;
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des tâches';
          this.isLoading = false;
          console.error('Erreur:', error);
        },
      })
    );
  }

  private buildQueryOptions(): TaskQueryOptions {
    const formValue = this.filterForm.value;
    const options: TaskQueryOptions = {};

    if (formValue.status) options.status = formValue.status;
    if (formValue.priority) options.priority = formValue.priority;
    if (formValue.type) options.type = formValue.type;
    if (formValue.assigneeId) options.assigneeId = formValue.assigneeId;
    if (formValue.assignedById) options.assignedById = formValue.assignedById;

    return options;
  }

  private setupFilterSubscription(): void {
    this.subscriptions.add(
      this.filterForm.valueChanges.subscribe(() => {
        this.applyFilters();
      })
    );
  }

  applyFilters(): void {
    let filtered = [...this.tasks];

    // Filtre par terme de recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (task) =>
          task.title.toLowerCase().includes(term) ||
          (task.description && task.description.toLowerCase().includes(term)) ||
          this.getEmployeeName(task.assigneeId).toLowerCase().includes(term)
      );
    }

    this.filteredTasks = filtered;
  }

  // Actions CRUD
  createTask(): void {
    this.selectedTask = null;
    this.taskForm.reset({
      priority: TaskPriority.MEDIUM,
      type: TaskType.GENERAL,
    });
    this.showCreateModal = true;
  }

  editTask(task: Task): void {
    this.selectedTask = task;
    this.taskForm.patchValue({
      title: task.title,
      description: task.description,
      priority: task.priority,
      type: task.type,
      dueDate: task.dueDate ? this.formatDateForInput(task.dueDate) : '',
      startDate: task.startDate ? this.formatDateForInput(task.startDate) : '',
      estimatedHours: task.estimatedHours,
      assigneeId: task.assigneeId,
      tags: task.tags || [],
    });
    this.showEditModal = true;
  }

  viewTask(task: Task): void {
    this.selectedTask = task;
    this.showDetailsModal = true;
  }

  deleteTask(task: Task): void {
    if (
      confirm(`Êtes-vous sûr de vouloir supprimer la tâche "${task.title}" ?`)
    ) {
      this.subscriptions.add(
        this.taskService.deleteTask(task.id).subscribe({
          next: () => {
            this.loadTasks();
          },
          error: (error) => {
            this.error = 'Erreur lors de la suppression';
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  onSubmitTask(): void {
    if (this.taskForm.valid) {
      this.isSubmitting = true;
      const formData = this.taskForm.value;

      // TODO: Récupérer le companyId et assignedById depuis le store
      const taskData: CreateTaskDto | UpdateTaskDto = {
        title: formData.title,
        description: formData.description,
        priority: formData.priority,
        type: formData.type,
        dueDate: formData.dueDate,
        startDate: formData.startDate,
        estimatedHours: formData.estimatedHours,
        assigneeId: formData.assigneeId,
        tags: formData.tags,
      };

      if (!this.selectedTask) {
        (taskData as CreateTaskDto).companyId = 'current-company-id';
      }

      const operation = this.selectedTask
        ? this.taskService.updateTask(
            this.selectedTask.id,
            taskData as UpdateTaskDto
          )
        : this.taskService.createTask(taskData as CreateTaskDto);

      this.subscriptions.add(
        operation.subscribe({
          next: () => {
            this.closeModals();
            this.loadTasks();
            this.isSubmitting = false;
          },
          error: (error) => {
            this.error = 'Erreur lors de la sauvegarde';
            this.isSubmitting = false;
            console.error('Erreur:', error);
          },
        })
      );
    }
  }

  // Actions spécifiques
  updateTaskStatus(task: Task, status: TaskStatus): void {
    this.subscriptions.add(
      this.taskService.updateTask(task.id, { status }).subscribe({
        next: (updatedTask) => {
          const index = this.tasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.tasks[index] = updatedTask;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la mise à jour du statut';
          console.error('Erreur:', error);
        },
      })
    );
  }

  updateProgress(task: Task, progress: number): void {
    this.subscriptions.add(
      this.taskService.updateTaskProgress(task.id, progress).subscribe({
        next: (updatedTask) => {
          const index = this.tasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.tasks[index] = updatedTask;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la mise à jour du progrès';
          console.error('Erreur:', error);
        },
      })
    );
  }

  assignTask(task: Task, assigneeId: string): void {
    this.subscriptions.add(
      this.taskService.assignTask(task.id, assigneeId).subscribe({
        next: (updatedTask) => {
          const index = this.tasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.tasks[index] = updatedTask;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = "Erreur lors de l'assignation";
          console.error('Erreur:', error);
        },
      })
    );
  }

  completeTask(task: Task): void {
    const actualHours = prompt('Heures réelles passées (optionnel):');
    const hours = actualHours ? parseFloat(actualHours) : undefined;

    this.subscriptions.add(
      this.taskService.completeTask(task.id, hours).subscribe({
        next: (updatedTask) => {
          const index = this.tasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.tasks[index] = updatedTask;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la completion';
          console.error('Erreur:', error);
        },
      })
    );
  }

  // Modals
  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.showDetailsModal = false;
    this.selectedTask = null;
    this.taskForm.reset();
  }

  // Filtres
  resetFilters(): void {
    this.filterForm.reset();
    this.searchTerm = '';
    this.applyFilters();
  }

  // Méthodes utilitaires
  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employé #${employeeId}`;
  }

  getStatusLabel(status: TaskStatus): string {
    const labels: { [key in TaskStatus]: string } = {
      [TaskStatus.TODO]: 'À faire',
      [TaskStatus.IN_PROGRESS]: 'En cours',
      [TaskStatus.REVIEW]: 'En révision',
      [TaskStatus.COMPLETED]: 'Terminé',
      [TaskStatus.CANCELLED]: 'Annulé',
      [TaskStatus.ON_HOLD]: 'En attente',
    };
    return labels[status];
  }

  getStatusClasses(status: TaskStatus): string {
    const classes: { [key in TaskStatus]: string } = {
      [TaskStatus.TODO]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [TaskStatus.IN_PROGRESS]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [TaskStatus.REVIEW]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [TaskStatus.COMPLETED]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [TaskStatus.CANCELLED]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [TaskStatus.ON_HOLD]:
        'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
    };
    return classes[status];
  }

  getPriorityLabel(priority: TaskPriority): string {
    const labels: { [key in TaskPriority]: string } = {
      [TaskPriority.LOW]: 'Faible',
      [TaskPriority.MEDIUM]: 'Moyenne',
      [TaskPriority.HIGH]: 'Élevée',
      [TaskPriority.URGENT]: 'Urgente',
    };
    return labels[priority];
  }

  getPriorityClasses(priority: TaskPriority): string {
    const classes: { [key in TaskPriority]: string } = {
      [TaskPriority.LOW]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [TaskPriority.MEDIUM]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [TaskPriority.HIGH]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      [TaskPriority.URGENT]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    };
    return classes[priority];
  }

  getTypeLabel(type: TaskType): string {
    const labels: { [key in TaskType]: string } = {
      [TaskType.GENERAL]: 'Général',
      [TaskType.HR_TASK]: 'RH',
      [TaskType.ONBOARDING]: 'Intégration',
      [TaskType.PERFORMANCE_REVIEW]: 'Évaluation',
      [TaskType.DOCUMENT_REVIEW]: 'Révision doc.',
      [TaskType.COMPLIANCE]: 'Conformité',
      [TaskType.TRAINING]: 'Formation',
      [TaskType.PROJECT]: 'Projet',
    };
    return labels[type];
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  formatDateForInput(date: string): string {
    return new Date(date).toISOString().split('T')[0];
  }

  isOverdue(task: Task): boolean {
    return !!(
      task.dueDate &&
      new Date(task.dueDate) < new Date() &&
      task.status !== TaskStatus.COMPLETED &&
      task.status !== TaskStatus.CANCELLED
    );
  }

  getProgressColor(progress: number): string {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  }

  onProgressChange(task: Task, event: any): void {
    const progress = parseInt(event.target.value);
    if (progress !== task.progress) {
      this.updateProgress(task, progress);
    }
  }

  canEdit(task: Task): boolean {
    return (
      task.status !== TaskStatus.COMPLETED &&
      task.status !== TaskStatus.CANCELLED
    );
  }

  canComplete(task: Task): boolean {
    return (
      task.status === TaskStatus.IN_PROGRESS ||
      task.status === TaskStatus.REVIEW
    );
  }
}
