<div class="p-6">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Mes tâches
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Gérez vos tâches assignées et suivez votre progression
        </p>
      </div>
      <button
        (click)="refreshTasks()"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
        Actualiser
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Total
          </p>
          <p class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ stats.total }}
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-gray-100 dark:bg-gray-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-gray-600 dark:text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            À faire
          </p>
          <p class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ stats.todo }}
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-blue-600 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            En cours
          </p>
          <p class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ stats.inProgress }}
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-green-600 dark:text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Terminées
          </p>
          <p class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ stats.completed }}
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div
            class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-5 h-5 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
            En retard
          </p>
          <p class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ stats.overdue }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div
    class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6"
  >
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Recherche -->
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Recherche</label
        >
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (ngModelChange)="onSearchChange()"
          placeholder="Titre, description..."
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        />
      </div>

      <!-- Statut -->
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Statut</label
        >
        <select
          [(ngModel)]="selectedStatus"
          (ngModelChange)="onStatusFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        >
          <option value="">Tous les statuts</option>
          <option *ngFor="let status of taskStatuses" [value]="status">
            {{ getStatusLabel(status) }}
          </option>
        </select>
      </div>

      <!-- Priorité -->
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Priorité</label
        >
        <select
          [(ngModel)]="selectedPriority"
          (ngModelChange)="onPriorityFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        >
          <option value="">Toutes les priorités</option>
          <option *ngFor="let priority of taskPriorities" [value]="priority">
            {{ getPriorityLabel(priority) }}
          </option>
        </select>
      </div>

      <!-- Actions -->
      <div class="flex items-end">
        <button
          type="button"
          (click)="resetFilters()"
          class="w-full px-3 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          Réinitialiser
        </button>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
  >
    {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
  </div>

  <!-- Liste des tâches -->
  <div *ngIf="!isLoading" class="space-y-4">
    <div
      *ngFor="let task of filteredTasks"
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <!-- En-tête de la tâche -->
          <div class="flex items-center gap-3 mb-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              {{ task.title }}
            </h3>

            <!-- Badges -->
            <span
              [class]="
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full ' +
                getStatusClasses(task.status)
              "
            >
              {{ getStatusLabel(task.status) }}
            </span>

            <span
              [class]="
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full ' +
                getPriorityClasses(task.priority)
              "
            >
              {{ getPriorityLabel(task.priority) }}
            </span>

            <span
              class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
            >
              {{ getTypeLabel(task.type) }}
            </span>

            <!-- Indicateur de retard -->
            <span
              *ngIf="isOverdue(task)"
              class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
            >
              ⚠️ En retard
            </span>
          </div>

          <!-- Description -->
          <p
            *ngIf="task.description"
            class="text-gray-600 dark:text-gray-400 mb-4"
          >
            {{ task.description }}
          </p>

          <!-- Informations détaillées -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <!-- Échéance -->
            <div *ngIf="task.dueDate">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400"
                >Échéance:</span
              >
              <div
                [class]="
                  'text-sm ' +
                  (getDueDateStatus(task) === 'overdue'
                    ? 'text-red-600 dark:text-red-400'
                    : getDueDateStatus(task) === 'due-soon'
                    ? 'text-yellow-600 dark:text-yellow-400'
                    : 'text-gray-900 dark:text-white')
                "
              >
                {{ formatDate(task.dueDate) }}
                <span *ngIf="getDueDateStatus(task) === 'due-soon'" class="ml-1"
                  >({{ getDaysUntilDue(task) }} jours)</span
                >
              </div>
            </div>

            <!-- Temps estimé -->
            <div *ngIf="task.estimatedHours">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400"
                >Temps estimé:</span
              >
              <div class="text-sm text-gray-900 dark:text-white">
                {{ task.estimatedHours }}h
              </div>
            </div>

            <!-- Temps réel -->
            <div *ngIf="task.actualHours">
              <span class="text-sm font-medium text-gray-500 dark:text-gray-400"
                >Temps réel:</span
              >
              <div class="text-sm text-gray-900 dark:text-white">
                {{ task.actualHours }}h
              </div>
            </div>
          </div>

          <!-- Barre de progression -->
          <div class="mb-4">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Progression</span
              >
              <span class="text-sm text-gray-900 dark:text-white"
                >{{ task.progress }}%</span
              >
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                [class]="
                  'h-2 rounded-full transition-all duration-300 ' +
                  getProgressColor(task.progress)
                "
                [style.width.%]="task.progress"
              ></div>
            </div>

            <!-- Slider pour mettre à jour le progrès -->
            <div *ngIf="task.status === 'IN_PROGRESS'" class="mt-2">
              <input
                type="range"
                min="0"
                max="100"
                [value]="task.progress"
                (change)="onProgressChange(task, $event)"
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
          </div>

          <!-- Tags -->
          <div
            *ngIf="task.tags && task.tags.length > 0"
            class="flex flex-wrap gap-2 mb-4"
          >
            <span
              *ngFor="let tag of task.tags"
              class="inline-flex px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
            >
              #{{ tag }}
            </span>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex flex-col gap-2 ml-4">
          <button
            *ngIf="canStart(task)"
            (click)="startTask(task)"
            class="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Commencer
          </button>

          <button
            *ngIf="canComplete(task)"
            (click)="completeTask(task)"
            class="px-3 py-1 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md"
          >
            Terminer
          </button>

          <button
            *ngIf="task.status === 'IN_PROGRESS'"
            (click)="updateTaskStatus(task, TaskStatus.ON_HOLD)"
            class="px-3 py-1 text-sm bg-yellow-600 hover:bg-yellow-700 text-white rounded-md"
          >
            Pause
          </button>

          <button
            *ngIf="task.status === 'ON_HOLD'"
            (click)="updateTaskStatus(task, TaskStatus.IN_PROGRESS)"
            class="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Reprendre
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Message si aucune tâche -->
  <div
    *ngIf="!isLoading && filteredTasks.length === 0"
    class="text-center py-8"
  >
    <svg
      class="mx-auto h-12 w-12 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
      ></path>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
      Aucune tâche trouvée
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      <span *ngIf="searchTerm || selectedStatus || selectedPriority"
        >Essayez de modifier vos filtres.</span
      >
      <span *ngIf="!searchTerm && !selectedStatus && !selectedPriority"
        >Vous n'avez aucune tâche assignée pour le moment.</span
      >
    </p>
  </div>
</div>
