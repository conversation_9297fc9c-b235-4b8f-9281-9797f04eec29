import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  TaskService,
  Task,
  TaskStatus,
  TaskPriority,
  TaskType,
} from '../../../../../core/services/task/task.service';

@Component({
  selector: 'app-my-tasks',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './my-tasks.component.html',
})
export class MyTasksComponent implements OnInit, OnDestroy {
  myTasks: Task[] = [];
  filteredTasks: Task[] = [];

  // Filtres
  selectedStatus: TaskStatus | '' = '';
  selectedPriority: TaskPriority | '' = '';
  searchTerm = '';

  // États
  isLoading = false;
  error: string | null = null;

  // Énumérations
  TaskStatus = TaskStatus;
  taskStatuses = Object.values(TaskStatus);
  taskPriorities = Object.values(TaskPriority);

  // Statistiques
  stats = {
    total: 0,
    todo: 0,
    inProgress: 0,
    completed: 0,
    overdue: 0,
  };

  private subscriptions = new Subscription();
  private currentUserId = 'current-user-id'; // TODO: Récupérer depuis le store

  constructor(private taskService: TaskService) {}

  ngOnInit(): void {
    this.loadMyTasks();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadMyTasks(): void {
    this.isLoading = true;
    this.error = null;

    this.subscriptions.add(
      this.taskService.getUserTasks(this.currentUserId).subscribe({
        next: (tasks) => {
          this.myTasks = tasks;
          this.calculateStats();
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement de vos tâches';
          this.isLoading = false;
          console.error('Erreur:', error);
        },
      })
    );
  }

  private calculateStats(): void {
    this.stats.total = this.myTasks.length;
    this.stats.todo = this.myTasks.filter(
      (t) => t.status === TaskStatus.TODO
    ).length;
    this.stats.inProgress = this.myTasks.filter(
      (t) => t.status === TaskStatus.IN_PROGRESS
    ).length;
    this.stats.completed = this.myTasks.filter(
      (t) => t.status === TaskStatus.COMPLETED
    ).length;
    this.stats.overdue = this.myTasks.filter((t) => this.isOverdue(t)).length;
  }

  private applyFilters(): void {
    let filtered = [...this.myTasks];

    // Filtre par statut
    if (this.selectedStatus) {
      filtered = filtered.filter((task) => task.status === this.selectedStatus);
    }

    // Filtre par priorité
    if (this.selectedPriority) {
      filtered = filtered.filter(
        (task) => task.priority === this.selectedPriority
      );
    }

    // Filtre par terme de recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (task) =>
          task.title.toLowerCase().includes(term) ||
          (task.description && task.description.toLowerCase().includes(term))
      );
    }

    this.filteredTasks = filtered;
  }

  // Actions sur les tâches
  updateTaskStatus(task: Task, status: TaskStatus): void {
    this.subscriptions.add(
      this.taskService.updateTask(task.id, { status }).subscribe({
        next: (updatedTask) => {
          const index = this.myTasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.myTasks[index] = updatedTask;
            this.calculateStats();
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la mise à jour du statut';
          console.error('Erreur:', error);
        },
      })
    );
  }

  updateProgress(task: Task, progress: number): void {
    this.subscriptions.add(
      this.taskService.updateTaskProgress(task.id, progress).subscribe({
        next: (updatedTask) => {
          const index = this.myTasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.myTasks[index] = updatedTask;
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la mise à jour du progrès';
          console.error('Erreur:', error);
        },
      })
    );
  }

  startTask(task: Task): void {
    this.updateTaskStatus(task, TaskStatus.IN_PROGRESS);
  }

  completeTask(task: Task): void {
    const actualHours = prompt('Heures réelles passées (optionnel):');
    const hours = actualHours ? parseFloat(actualHours) : undefined;

    this.subscriptions.add(
      this.taskService.completeTask(task.id, hours).subscribe({
        next: (updatedTask) => {
          const index = this.myTasks.findIndex((t) => t.id === task.id);
          if (index !== -1) {
            this.myTasks[index] = updatedTask;
            this.calculateStats();
            this.applyFilters();
          }
        },
        error: (error) => {
          this.error = 'Erreur lors de la completion';
          console.error('Erreur:', error);
        },
      })
    );
  }

  // Filtres
  onStatusFilterChange(): void {
    this.applyFilters();
  }

  onPriorityFilterChange(): void {
    this.applyFilters();
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  resetFilters(): void {
    this.selectedStatus = '';
    this.selectedPriority = '';
    this.searchTerm = '';
    this.applyFilters();
  }

  // Méthodes utilitaires
  getStatusLabel(status: TaskStatus): string {
    const labels: { [key in TaskStatus]: string } = {
      [TaskStatus.TODO]: 'À faire',
      [TaskStatus.IN_PROGRESS]: 'En cours',
      [TaskStatus.REVIEW]: 'En révision',
      [TaskStatus.COMPLETED]: 'Terminé',
      [TaskStatus.CANCELLED]: 'Annulé',
      [TaskStatus.ON_HOLD]: 'En attente',
    };
    return labels[status];
  }

  getStatusClasses(status: TaskStatus): string {
    const classes: { [key in TaskStatus]: string } = {
      [TaskStatus.TODO]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [TaskStatus.IN_PROGRESS]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [TaskStatus.REVIEW]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [TaskStatus.COMPLETED]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [TaskStatus.CANCELLED]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [TaskStatus.ON_HOLD]:
        'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
    };
    return classes[status];
  }

  getPriorityLabel(priority: TaskPriority): string {
    const labels: { [key in TaskPriority]: string } = {
      [TaskPriority.LOW]: 'Faible',
      [TaskPriority.MEDIUM]: 'Moyenne',
      [TaskPriority.HIGH]: 'Élevée',
      [TaskPriority.URGENT]: 'Urgente',
    };
    return labels[priority];
  }

  getPriorityClasses(priority: TaskPriority): string {
    const classes: { [key in TaskPriority]: string } = {
      [TaskPriority.LOW]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [TaskPriority.MEDIUM]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [TaskPriority.HIGH]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      [TaskPriority.URGENT]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    };
    return classes[priority];
  }

  getTypeLabel(type: TaskType): string {
    const labels: { [key in TaskType]: string } = {
      [TaskType.GENERAL]: 'Général',
      [TaskType.HR_TASK]: 'RH',
      [TaskType.ONBOARDING]: 'Intégration',
      [TaskType.PERFORMANCE_REVIEW]: 'Évaluation',
      [TaskType.DOCUMENT_REVIEW]: 'Révision doc.',
      [TaskType.COMPLIANCE]: 'Conformité',
      [TaskType.TRAINING]: 'Formation',
      [TaskType.PROJECT]: 'Projet',
    };
    return labels[type];
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR');
  }

  isOverdue(task: Task): boolean {
    return !!(
      task.dueDate &&
      new Date(task.dueDate) < new Date() &&
      task.status !== TaskStatus.COMPLETED &&
      task.status !== TaskStatus.CANCELLED
    );
  }

  getProgressColor(progress: number): string {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  }

  onProgressChange(task: Task, event: any): void {
    const progress = parseInt(event.target.value);
    if (progress !== task.progress) {
      this.updateProgress(task, progress);
    }
  }

  canStart(task: Task): boolean {
    return task.status === TaskStatus.TODO;
  }

  canComplete(task: Task): boolean {
    return (
      task.status === TaskStatus.IN_PROGRESS ||
      task.status === TaskStatus.REVIEW
    );
  }

  getDaysUntilDue(task: Task): number {
    if (!task.dueDate) return 0;
    const today = new Date();
    const dueDate = new Date(task.dueDate);
    const diffTime = dueDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getDueDateStatus(task: Task): 'overdue' | 'due-soon' | 'normal' {
    if (!task.dueDate) return 'normal';
    const days = this.getDaysUntilDue(task);
    if (days < 0) return 'overdue';
    if (days <= 3) return 'due-soon';
    return 'normal';
  }

  refreshTasks(): void {
    this.loadMyTasks();
  }
}
