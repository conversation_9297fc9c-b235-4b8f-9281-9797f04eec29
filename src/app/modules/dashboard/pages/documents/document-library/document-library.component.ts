import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { DocumentService } from '../../../../../core/services/document/document.service';
import {
  DocumentModel,
  DocumentStats,
  DocumentFilter,
  DocumentCategory,
  DocumentType,
  DocumentStatus,
  DocumentUploadRequest,
  BulkDocumentOperation,
  BulkOperation,
} from '../../../../../core/models/document.model';

@Component({
  selector: 'app-document-library',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './document-library.component.html',
})
export class DocumentLibraryComponent implements OnInit, OnDestroy {
  documents: DocumentModel[] = [];
  filteredDocuments: DocumentModel[] = [];
  stats: DocumentStats | null = null;

  // Vue et filtres
  viewMode: 'grid' | 'list' = 'grid';
  searchTerm = '';
  selectedCategory = '';
  selectedType = '';

  // Sélection
  selectedDocuments: string[] = [];
  allSelected = false;
  someSelected = false;

  // Upload
  showUploadModal = false;
  selectedFiles: File[] = [];
  isUploading = false;

  // Données pour les filtres
  categories = [
    { value: DocumentCategory.CONTRACTS, label: 'Contrats' },
    { value: DocumentCategory.POLICIES, label: 'Politiques' },
    { value: DocumentCategory.PROCEDURES, label: 'Procédures' },
    { value: DocumentCategory.FORMS, label: 'Formulaires' },
    { value: DocumentCategory.CERTIFICATES, label: 'Certificats' },
    { value: DocumentCategory.TRAINING, label: 'Formation' },
    { value: DocumentCategory.COMPLIANCE, label: 'Conformité' },
    { value: DocumentCategory.PERSONAL, label: 'Personnel' },
    { value: DocumentCategory.ADMINISTRATIVE, label: 'Administratif' },
    { value: DocumentCategory.LEGAL, label: 'Juridique' },
    { value: DocumentCategory.FINANCIAL, label: 'Financier' },
    { value: DocumentCategory.REPORTS, label: 'Rapports' },
    { value: DocumentCategory.TEMPLATES, label: 'Templates' },
    { value: DocumentCategory.OTHER, label: 'Autre' },
  ];

  documentTypes = [
    { value: DocumentType.PDF, label: 'PDF' },
    { value: DocumentType.WORD, label: 'Word' },
    { value: DocumentType.EXCEL, label: 'Excel' },
    { value: DocumentType.POWERPOINT, label: 'PowerPoint' },
    { value: DocumentType.IMAGE, label: 'Image' },
    { value: DocumentType.VIDEO, label: 'Vidéo' },
    { value: DocumentType.AUDIO, label: 'Audio' },
    { value: DocumentType.TEXT, label: 'Texte' },
    { value: DocumentType.ARCHIVE, label: 'Archive' },
    { value: DocumentType.OTHER, label: 'Autre' },
  ];

  private subscriptions = new Subscription();

  constructor(
    private documentService: DocumentService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadDocuments();
    this.loadStats();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadDocuments(): void {
    this.subscriptions.add(
      this.documentService.getDocuments().subscribe((documents) => {
        this.documents = documents as any;
        this.applyFilters();
        this.updateSelectionState();
      })
    );
  }

  private loadStats(): void {
    this.subscriptions.add(
      this.documentService.getDocumentStats().subscribe((stats) => {
        this.stats = stats;
      })
    );
  }

  // Filtres et recherche
  onFilterChange(): void {
    this.applyFilters();
  }

  private applyFilters(): void {
    const filter: DocumentFilter = {
      search: this.searchTerm || undefined,
      category: (this.selectedCategory as DocumentCategory) || undefined,
      type: (this.selectedType as DocumentType) || undefined,
    };

    this.subscriptions.add(
      this.documentService.getDocuments(filter).subscribe((documents) => {
        this.filteredDocuments = documents as any;
        this.updateSelectionState();
      })
    );
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.selectedType = '';
    this.applyFilters();
  }

  // Vue
  toggleView(): void {
    this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid';
  }

  // Sélection
  isSelected(documentId: string): boolean {
    return this.selectedDocuments.includes(documentId);
  }

  toggleSelection(documentId: string, event: any): void {
    if (event.target.checked) {
      this.selectedDocuments.push(documentId);
    } else {
      this.selectedDocuments = this.selectedDocuments.filter(
        (id) => id !== documentId
      );
    }
    this.updateSelectionState();
  }

  toggleSelectAll(event: any): void {
    if (event.target.checked) {
      this.selectedDocuments = this.filteredDocuments.map((doc) => doc.id);
    } else {
      this.selectedDocuments = [];
    }
    this.updateSelectionState();
  }

  private updateSelectionState(): void {
    this.allSelected =
      this.filteredDocuments.length > 0 &&
      this.selectedDocuments.length === this.filteredDocuments.length;
    this.someSelected =
      this.selectedDocuments.length > 0 &&
      this.selectedDocuments.length < this.filteredDocuments.length;
  }

  clearSelection(): void {
    this.selectedDocuments = [];
    this.updateSelectionState();
  }

  // Actions sur les documents
  viewDocument(id: string): void {
    this.router.navigate(['/dashboard/documents/view', id]);
  }

  downloadDocument(id: string): void {
    this.subscriptions.add(
      this.documentService.downloadDocument(id).subscribe((blob) => {
        const documentModel = this.documents.find((d) => d.id === id);
        if (documentModel) {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = documentModel.originalFileName;
          link.click();
          window.URL.revokeObjectURL(url);
        }
      })
    );
  }

  shareDocument(id: string): void {
    // TODO: Implémenter le partage
    alert('Fonctionnalité de partage à implémenter');
  }

  deleteDocument(id: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      this.subscriptions.add(
        this.documentService.deleteDocument(id).subscribe(() => {
          this.loadDocuments();
          this.loadStats();
        })
      );
    }
  }

  // Actions en lot
  bulkDownload(): void {
    this.selectedDocuments.forEach((id) => {
      this.downloadDocument(id);
    });
  }

  bulkArchive(): void {
    const operation: BulkDocumentOperation = {
      documentIds: this.selectedDocuments,
      operation: BulkOperation.ARCHIVE,
    };

    this.subscriptions.add(
      this.documentService.bulkOperation(operation).subscribe(() => {
        this.loadDocuments();
        this.loadStats();
        this.clearSelection();
      })
    );
  }

  bulkDelete(): void {
    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer ${this.selectedDocuments.length} document(s) ?`
      )
    ) {
      const operation: BulkDocumentOperation = {
        documentIds: this.selectedDocuments,
        operation: BulkOperation.DELETE,
      };

      this.subscriptions.add(
        this.documentService.bulkOperation(operation).subscribe(() => {
          this.loadDocuments();
          this.loadStats();
          this.clearSelection();
        })
      );
    }
  }

  // Upload
  openUploadModal(): void {
    this.showUploadModal = true;
    this.selectedFiles = [];
  }

  closeUploadModal(): void {
    this.showUploadModal = false;
    this.selectedFiles = [];
    this.isUploading = false;
  }

  onFileSelect(event: any): void {
    const files = Array.from(event.target.files) as File[];
    this.selectedFiles = [...this.selectedFiles, ...files];
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    const files = Array.from(event.dataTransfer?.files || []) as File[];
    this.selectedFiles = [...this.selectedFiles, ...files];
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  uploadFiles(): void {
    if (this.selectedFiles.length === 0) return;

    this.isUploading = true;

    // Simuler l'upload de chaque fichier
    const uploadPromises = this.selectedFiles.map((file) => {
      const request: DocumentUploadRequest = {
        file,
        name: file.name.split('.')[0],
        category: DocumentCategory.OTHER,
        visibility: 'INTERNAL' as any,
        tags: [],
        isConfidential: false,
        requiresSignature: false,
        approvalRequired: false,
      };

      return this.documentService
        .uploadDocument(request as any, {})
        .toPromise();
    });

    Promise.all(uploadPromises)
      .then(() => {
        this.loadDocuments();
        this.loadStats();
        this.closeUploadModal();
      })
      .catch((error) => {
        console.error('Erreur lors du téléchargement:', error);
        this.isUploading = false;
      });
  }

  // Méthodes utilitaires
  getApprovedCount(): number {
    return this.stats?.byStatus[DocumentStatus.APPROVED] || 0;
  }

  getPendingCount(): number {
    return this.stats?.byStatus[DocumentStatus.PENDING_REVIEW] || 0;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getCategoryLabel(category: DocumentCategory): string {
    const categoryObj = this.categories.find((c) => c.value === category);
    return categoryObj?.label || category;
  }

  getStatusLabel(status: DocumentStatus): string {
    const labels: { [key in DocumentStatus]: string } = {
      [DocumentStatus.DRAFT]: 'Brouillon',
      [DocumentStatus.PENDING_REVIEW]: 'En attente',
      [DocumentStatus.APPROVED]: 'Approuvé',
      [DocumentStatus.REJECTED]: 'Rejeté',
      [DocumentStatus.PUBLISHED]: 'Publié',
      [DocumentStatus.ARCHIVED]: 'Archivé',
      [DocumentStatus.EXPIRED]: 'Expiré',
    };
    return labels[status];
  }

  getCategoryClasses(category: DocumentCategory): string {
    const classes: { [key in DocumentCategory]: string } = {
      [DocumentCategory.CONTRACTS]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [DocumentCategory.POLICIES]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [DocumentCategory.PROCEDURES]:
        'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
      [DocumentCategory.FORMS]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [DocumentCategory.CERTIFICATES]:
        'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400',
      [DocumentCategory.TRAINING]:
        'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400',
      [DocumentCategory.COMPLIANCE]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [DocumentCategory.PERSONAL]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      [DocumentCategory.ADMINISTRATIVE]:
        'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400',
      [DocumentCategory.LEGAL]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [DocumentCategory.FINANCIAL]:
        'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400',
      [DocumentCategory.REPORTS]:
        'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-400',
      [DocumentCategory.TEMPLATES]:
        'bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-400',
      [DocumentCategory.OTHER]:
        'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-400',
    };
    return classes[category];
  }

  getStatusClasses(status: DocumentStatus): string {
    const classes: { [key in DocumentStatus]: string } = {
      [DocumentStatus.DRAFT]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [DocumentStatus.PENDING_REVIEW]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [DocumentStatus.APPROVED]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [DocumentStatus.REJECTED]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [DocumentStatus.PUBLISHED]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [DocumentStatus.ARCHIVED]:
        'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
      [DocumentStatus.EXPIRED]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
    };
    return classes[status];
  }

  getFileTypeClasses(type: DocumentType): string {
    const classes: { [key in DocumentType]: string } = {
      [DocumentType.PDF]:
        'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400',
      [DocumentType.WORD]:
        'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
      [DocumentType.EXCEL]:
        'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
      [DocumentType.POWERPOINT]:
        'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400',
      [DocumentType.IMAGE]:
        'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400',
      [DocumentType.VIDEO]:
        'bg-pink-100 text-pink-600 dark:bg-pink-900/30 dark:text-pink-400',
      [DocumentType.AUDIO]:
        'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400',
      [DocumentType.TEXT]:
        'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400',
      [DocumentType.ARCHIVE]:
        'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400',
      [DocumentType.OTHER]:
        'bg-slate-100 text-slate-600 dark:bg-slate-900/30 dark:text-slate-400',
    };
    return classes[type];
  }

  getFileTypeIcon(type: DocumentType): string {
    const icons: { [key in DocumentType]: string } = {
      [DocumentType.PDF]:
        'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z',
      [DocumentType.WORD]:
        'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
      [DocumentType.EXCEL]:
        'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
      [DocumentType.POWERPOINT]:
        'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
      [DocumentType.IMAGE]:
        'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z',
      [DocumentType.VIDEO]:
        'M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z',
      [DocumentType.AUDIO]:
        'M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12Z',
      [DocumentType.TEXT]:
        'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
      [DocumentType.ARCHIVE]:
        'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
      [DocumentType.OTHER]:
        'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
    };
    return icons[type];
  }
}
