import { Routes } from '@angular/router';

export const DOCUMENTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./document-library/document-library.component').then(m => m.DocumentLibraryComponent),
    title: 'Bibliothèque de Documents - LuminaHR'
  },
  {
    path: 'templates',
    loadComponent: () => import('./document-templates/document-templates.component').then(m => m.DocumentTemplatesComponent),
    title: 'Templates de Documents - LuminaHR'
  },
  {
    path: 'approvals',
    loadComponent: () => import('./document-approvals/document-approvals.component').then(m => m.DocumentApprovalsComponent),
    title: 'Approbations de Documents - LuminaHR'
  },
  {
    path: 'upload',
    loadComponent: () => import('./document-upload/document-upload.component').then(m => m.DocumentUploadComponent),
    title: 'Télécharger un Document - LuminaHR'
  },
  {
    path: 'view/:id',
    loadComponent: () => import('./document-view/document-view.component').then(m => m.DocumentViewComponent),
    title: 'Détails du Document - LuminaHR'
  }
];
