<div class="p-6">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Paramètres de sécurité
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Configurez les politiques de sécurité de votre entreprise
        </p>
      </div>
      <button
        (click)="resetToDefaults()"
        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
        Valeurs par défaut
      </button>
    </div>
  </div>

  <!-- Messages -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6 flex items-center justify-between"
  >
    <span>{{ error }}</span>
    <button (click)="clearMessages()" class="text-red-500 hover:text-red-700">
      <svg
        class="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        ></path>
      </svg>
    </button>
  </div>

  <div
    *ngIf="successMessage"
    class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg mb-6 flex items-center justify-between"
  >
    <span>{{ successMessage }}</span>
    <button
      (click)="clearMessages()"
      class="text-green-500 hover:text-green-700"
    >
      <svg
        class="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        ></path>
      </svg>
    </button>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
  </div>

  <!-- Contenu principal -->
  <div *ngIf="!isLoading" class="space-y-8">
    <!-- Politique de mot de passe -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
          Politique de mot de passe
        </h2>
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">Force:</span>
          <span [class]="'text-sm font-medium ' + getPasswordStrengthColor()">{{
            getPasswordStrengthLabel()
          }}</span>
        </div>
      </div>

      <form [formGroup]="passwordPolicyForm" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Longueur minimale</label
            >
            <input
              type="number"
              formControlName="minLength"
              min="6"
              max="50"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Expiration (jours)</label
            >
            <input
              type="number"
              formControlName="expirationDays"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="flex items-center">
              <input
                type="checkbox"
                formControlName="requireUppercase"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Majuscules requises</span
              >
            </label>
          </div>

          <div>
            <label class="flex items-center">
              <input
                type="checkbox"
                formControlName="requireLowercase"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Minuscules requises</span
              >
            </label>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="flex items-center">
              <input
                type="checkbox"
                formControlName="requireNumbers"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Chiffres requis</span
              >
            </label>
          </div>

          <div>
            <label class="flex items-center">
              <input
                type="checkbox"
                formControlName="requireSpecialChars"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Caractères spéciaux requis</span
              >
            </label>
          </div>
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >Empêcher la réutilisation (derniers mots de passe)</label
          >
          <input
            type="number"
            formControlName="preventReuse"
            min="0"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div class="flex gap-3">
          <button
            type="button"
            (click)="savePasswordPolicy()"
            [disabled]="passwordPolicyForm.invalid || isSaving"
            class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md"
          >
            {{ isSaving ? "Sauvegarde..." : "Sauvegarder" }}
          </button>

          <button
            type="button"
            (click)="testPasswordPolicy()"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md"
          >
            Tester la politique
          </button>
        </div>
      </form>
    </div>

    <!-- Paramètres de session -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Paramètres de session
      </h2>

      <form [formGroup]="sessionForm" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Timeout de session (minutes)</label
            >
            <input
              type="number"
              formControlName="timeoutMinutes"
              min="5"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Sessions simultanées max</label
            >
            <input
              type="number"
              formControlName="maxConcurrentSessions"
              min="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div>
          <label class="flex items-center">
            <input
              type="checkbox"
              formControlName="requireReauthentication"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Réauthentification requise pour les actions sensibles</span
            >
          </label>
        </div>

        <button
          type="button"
          (click)="saveSessionSettings()"
          [disabled]="sessionForm.invalid || isSaving"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md"
        >
          {{ isSaving ? "Sauvegarde..." : "Sauvegarder" }}
        </button>
      </form>
    </div>

    <!-- Authentification à deux facteurs -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Authentification à deux facteurs (2FA)
      </h2>

      <form [formGroup]="twoFactorForm" class="space-y-4">
        <div>
          <label class="flex items-center">
            <input
              type="checkbox"
              formControlName="enabled"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Activer l'authentification à deux facteurs</span
            >
          </label>
        </div>

        <div *ngIf="twoFactorForm.value.enabled">
          <label class="flex items-center">
            <input
              type="checkbox"
              formControlName="required"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Rendre obligatoire pour tous les utilisateurs</span
            >
          </label>
        </div>

        <div *ngIf="twoFactorForm.value.enabled" class="space-y-2">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >Méthodes disponibles:</label
          >

          <div class="space-y-2">
            <label class="flex items-center">
              <input
                type="checkbox"
                [checked]="isTwoFactorMethodEnabled('SMS')"
                (change)="toggleTwoFactorMethod('SMS')"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >SMS</span
              >
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                [checked]="isTwoFactorMethodEnabled('EMAIL')"
                (change)="toggleTwoFactorMethod('EMAIL')"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Email</span
              >
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                [checked]="isTwoFactorMethodEnabled('TOTP')"
                (change)="toggleTwoFactorMethod('TOTP')"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                >Application d'authentification (TOTP)</span
              >
            </label>
          </div>
        </div>

        <button
          type="button"
          (click)="saveTwoFactorAuth()"
          [disabled]="twoFactorForm.invalid || isSaving"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md"
        >
          {{ isSaving ? "Sauvegarde..." : "Sauvegarder" }}
        </button>
      </form>
    </div>

    <!-- Whitelist IP -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Whitelist d'adresses IP
      </h2>

      <form [formGroup]="ipWhitelistForm" class="space-y-4">
        <div class="flex gap-2">
          <input
            type="text"
            [(ngModel)]="newIpAddress"
            placeholder="***********"
            class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
          <button
            type="button"
            (click)="addIpAddress()"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
          >
            Ajouter
          </button>
        </div>

        <div
          *ngIf="ipWhitelistForm.value.ipAddresses?.length > 0"
          class="space-y-2"
        >
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >Adresses autorisées:</label
          >
          <div class="space-y-1">
            <div
              *ngFor="let ip of ipWhitelistForm.value.ipAddresses"
              class="flex items-center justify-between bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded"
            >
              <span class="text-sm text-gray-900 dark:text-white">{{
                ip
              }}</span>
              <button
                type="button"
                (click)="removeIpAddress(ip)"
                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <button
          type="button"
          (click)="saveIpWhitelist()"
          [disabled]="isSaving"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md"
        >
          {{ isSaving ? "Sauvegarde..." : "Sauvegarder" }}
        </button>
      </form>
    </div>

    <!-- Journal d'audit -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Journal d'audit
      </h2>

      <form [formGroup]="auditForm" class="space-y-4">
        <div>
          <label class="flex items-center">
            <input
              type="checkbox"
              formControlName="enabled"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Activer le journal d'audit</span
            >
          </label>
        </div>

        <div *ngIf="auditForm.value.enabled">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >Rétention des logs (jours)</label
          >
          <input
            type="number"
            formControlName="retentionDays"
            min="30"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <button
          type="button"
          (click)="saveAuditSettings()"
          [disabled]="auditForm.invalid || isSaving"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md"
        >
          {{ isSaving ? "Sauvegarde..." : "Sauvegarder" }}
        </button>
      </form>
    </div>
  </div>
</div>
