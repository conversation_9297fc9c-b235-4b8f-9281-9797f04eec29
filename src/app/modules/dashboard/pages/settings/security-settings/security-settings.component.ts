import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { 
  SettingsService, 
  SecuritySetting,
  UpdateSecuritySettingDto
} from '../../../../../core/services/settings/settings.service';

@Component({
  selector: 'app-security-settings',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './security-settings.component.html'
})
export class SecuritySettingsComponent implements OnInit, OnDestroy {
  securitySettings: SecuritySetting | null = null;
  
  // Formulaires
  passwordPolicyForm: FormGroup;
  sessionForm: FormGroup;
  twoFactorForm: FormGroup;
  ipWhitelistForm: FormGroup;
  auditForm: FormGroup;
  
  // États
  isLoading = false;
  isSaving = false;
  error: string | null = null;
  successMessage: string | null = null;
  
  // Nouvelle IP pour la whitelist
  newIpAddress = '';
  
  private subscriptions = new Subscription();
  private currentCompanyId = 'current-company-id'; // TODO: Récupérer depuis le store

  constructor(
    private settingsService: SettingsService,
    private fb: FormBuilder
  ) {
    this.passwordPolicyForm = this.fb.group({
      minLength: [8, [Validators.required, Validators.min(6), Validators.max(50)]],
      requireUppercase: [true],
      requireLowercase: [true],
      requireNumbers: [true],
      requireSpecialChars: [true],
      expirationDays: [90, [Validators.required, Validators.min(0)]],
      preventReuse: [5, [Validators.required, Validators.min(0)]]
    });

    this.sessionForm = this.fb.group({
      timeoutMinutes: [30, [Validators.required, Validators.min(5)]],
      maxConcurrentSessions: [3, [Validators.required, Validators.min(1)]],
      requireReauthentication: [false]
    });

    this.twoFactorForm = this.fb.group({
      enabled: [false],
      required: [false],
      methods: [[]]
    });

    this.ipWhitelistForm = this.fb.group({
      ipAddresses: [[]]
    });

    this.auditForm = this.fb.group({
      enabled: [true],
      retentionDays: [365, [Validators.required, Validators.min(30)]]
    });
  }

  ngOnInit(): void {
    this.loadSecuritySettings();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadSecuritySettings(): void {
    this.isLoading = true;
    this.error = null;
    
    this.subscriptions.add(
      this.settingsService.getSecuritySettings(this.currentCompanyId).subscribe({
        next: (settings) => {
          this.securitySettings = settings;
          this.populateForms(settings);
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des paramètres de sécurité';
          this.isLoading = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  private populateForms(settings: SecuritySetting): void {
    this.passwordPolicyForm.patchValue(settings.passwordPolicy);
    this.sessionForm.patchValue(settings.sessionSettings);
    this.twoFactorForm.patchValue(settings.twoFactorAuth);
    this.ipWhitelistForm.patchValue({ ipAddresses: settings.ipWhitelist });
    this.auditForm.patchValue(settings.auditLog);
  }

  // Sauvegarde des paramètres
  savePasswordPolicy(): void {
    if (this.passwordPolicyForm.valid) {
      this.saveSettings({ passwordPolicy: this.passwordPolicyForm.value });
    }
  }

  saveSessionSettings(): void {
    if (this.sessionForm.valid) {
      this.saveSettings({ sessionSettings: this.sessionForm.value });
    }
  }

  saveTwoFactorAuth(): void {
    if (this.twoFactorForm.valid) {
      this.saveSettings({ twoFactorAuth: this.twoFactorForm.value });
    }
  }

  saveIpWhitelist(): void {
    if (this.ipWhitelistForm.valid) {
      this.saveSettings({ ipWhitelist: this.ipWhitelistForm.value.ipAddresses });
    }
  }

  saveAuditSettings(): void {
    if (this.auditForm.valid) {
      this.saveSettings({ auditLog: this.auditForm.value });
    }
  }

  private saveSettings(updateData: UpdateSecuritySettingDto): void {
    this.isSaving = true;
    this.error = null;
    this.successMessage = null;
    
    this.subscriptions.add(
      this.settingsService.updateSecuritySettings(this.currentCompanyId, updateData).subscribe({
        next: (updatedSettings) => {
          this.securitySettings = updatedSettings;
          this.successMessage = 'Paramètres sauvegardés avec succès';
          this.isSaving = false;
          setTimeout(() => this.successMessage = null, 3000);
        },
        error: (error) => {
          this.error = 'Erreur lors de la sauvegarde des paramètres';
          this.isSaving = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  // Gestion de la whitelist IP
  addIpAddress(): void {
    if (this.newIpAddress && this.isValidIpAddress(this.newIpAddress)) {
      const currentIps = this.ipWhitelistForm.value.ipAddresses || [];
      if (!currentIps.includes(this.newIpAddress)) {
        const updatedIps = [...currentIps, this.newIpAddress];
        this.ipWhitelistForm.patchValue({ ipAddresses: updatedIps });
        this.newIpAddress = '';
      }
    }
  }

  removeIpAddress(ip: string): void {
    const currentIps = this.ipWhitelistForm.value.ipAddresses || [];
    const updatedIps = currentIps.filter((address: string) => address !== ip);
    this.ipWhitelistForm.patchValue({ ipAddresses: updatedIps });
  }

  private isValidIpAddress(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }

  // Gestion des méthodes 2FA
  toggleTwoFactorMethod(method: string): void {
    const currentMethods = this.twoFactorForm.value.methods || [];
    let updatedMethods;
    
    if (currentMethods.includes(method)) {
      updatedMethods = currentMethods.filter((m: string) => m !== method);
    } else {
      updatedMethods = [...currentMethods, method];
    }
    
    this.twoFactorForm.patchValue({ methods: updatedMethods });
  }

  isTwoFactorMethodEnabled(method: string): boolean {
    const methods = this.twoFactorForm.value.methods || [];
    return methods.includes(method);
  }

  // Test de configuration
  testPasswordPolicy(): void {
    const policy = this.passwordPolicyForm.value;
    const testPassword = 'TestPassword123!';
    
    let isValid = true;
    let errors: string[] = [];
    
    if (testPassword.length < policy.minLength) {
      isValid = false;
      errors.push(`Minimum ${policy.minLength} caractères requis`);
    }
    
    if (policy.requireUppercase && !/[A-Z]/.test(testPassword)) {
      isValid = false;
      errors.push('Au moins une majuscule requise');
    }
    
    if (policy.requireLowercase && !/[a-z]/.test(testPassword)) {
      isValid = false;
      errors.push('Au moins une minuscule requise');
    }
    
    if (policy.requireNumbers && !/\d/.test(testPassword)) {
      isValid = false;
      errors.push('Au moins un chiffre requis');
    }
    
    if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(testPassword)) {
      isValid = false;
      errors.push('Au moins un caractère spécial requis');
    }
    
    if (isValid) {
      this.successMessage = `Test réussi avec le mot de passe: ${testPassword}`;
    } else {
      this.error = `Test échoué: ${errors.join(', ')}`;
    }
    
    setTimeout(() => {
      this.successMessage = null;
      this.error = null;
    }, 5000);
  }

  // Méthodes utilitaires
  getPasswordStrengthLabel(): string {
    const policy = this.passwordPolicyForm.value;
    let score = 0;
    
    if (policy.minLength >= 12) score++;
    if (policy.requireUppercase) score++;
    if (policy.requireLowercase) score++;
    if (policy.requireNumbers) score++;
    if (policy.requireSpecialChars) score++;
    if (policy.expirationDays <= 60) score++;
    if (policy.preventReuse >= 10) score++;
    
    if (score >= 6) return 'Très forte';
    if (score >= 4) return 'Forte';
    if (score >= 2) return 'Moyenne';
    return 'Faible';
  }

  getPasswordStrengthColor(): string {
    const label = this.getPasswordStrengthLabel();
    switch (label) {
      case 'Très forte': return 'text-green-600 dark:text-green-400';
      case 'Forte': return 'text-blue-600 dark:text-blue-400';
      case 'Moyenne': return 'text-yellow-600 dark:text-yellow-400';
      default: return 'text-red-600 dark:text-red-400';
    }
  }

  clearMessages(): void {
    this.error = null;
    this.successMessage = null;
  }

  resetToDefaults(): void {
    if (confirm('Êtes-vous sûr de vouloir restaurer les paramètres par défaut ?')) {
      this.passwordPolicyForm.reset({
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        expirationDays: 90,
        preventReuse: 5
      });
      
      this.sessionForm.reset({
        timeoutMinutes: 30,
        maxConcurrentSessions: 3,
        requireReauthentication: false
      });
      
      this.twoFactorForm.reset({
        enabled: false,
        required: false,
        methods: []
      });
      
      this.ipWhitelistForm.reset({
        ipAddresses: []
      });
      
      this.auditForm.reset({
        enabled: true,
        retentionDays: 365
      });
    }
  }
}
