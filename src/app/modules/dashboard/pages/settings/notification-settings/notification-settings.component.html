<div class="p-6">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Paramètres de notifications
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Configurez vos préférences de notifications
        </p>
      </div>
      <div class="flex gap-2">
        <button
          (click)="resetToDefaults()"
          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
        >
          Défaut
        </button>
      </div>
    </div>
  </div>

  <!-- Résumé -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <div
      class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
    >
      <div class="flex items-center">
        <svg
          class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"
          ></path>
        </svg>
        <div>
          <p class="text-sm font-medium text-blue-600 dark:text-blue-400">
            Canaux actifs
          </p>
          <p class="text-lg font-semibold text-blue-900 dark:text-blue-300">
            {{ getActiveNotificationCount() }}/3
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-4"
    >
      <div class="flex items-center">
        <svg
          class="w-8 h-8 text-green-600 dark:text-green-400 mr-3"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
        <div>
          <p class="text-sm font-medium text-green-600 dark:text-green-400">
            Types activés
          </p>
          <p class="text-lg font-semibold text-green-900 dark:text-green-300">
            {{ getActiveTypeCount() }}/6
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 rounded-lg p-4"
    >
      <div class="flex items-center">
        <svg
          class="w-8 h-8 text-purple-600 dark:text-purple-400 mr-3"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          ></path>
        </svg>
        <div>
          <p class="text-sm font-medium text-purple-600 dark:text-purple-400">
            Mode silencieux
          </p>
          <p class="text-lg font-semibold text-purple-900 dark:text-purple-300">
            {{ isQuietHoursActive() ? "Actif" : "Inactif" }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Messages -->
  <div
    *ngIf="error"
    class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6 flex items-center justify-between"
  >
    <span>{{ error }}</span>
    <button (click)="clearMessages()" class="text-red-500 hover:text-red-700">
      <svg
        class="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        ></path>
      </svg>
    </button>
  </div>

  <div
    *ngIf="successMessage"
    class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg mb-6 flex items-center justify-between"
  >
    <span>{{ successMessage }}</span>
    <button
      (click)="clearMessages()"
      class="text-green-500 hover:text-green-700"
    >
      <svg
        class="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        ></path>
      </svg>
    </button>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
    ></div>
  </div>

  <!-- Contenu principal -->
  <div *ngIf="!isLoading" class="space-y-6">
    <!-- Actions rapides -->
    <div
      class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Actions rapides
      </h2>
      <div class="flex flex-wrap gap-3">
        <button
          (click)="enableAllNotifications()"
          class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm"
        >
          Tout activer
        </button>
        <button
          (click)="disableAllNotifications()"
          class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm"
        >
          Tout désactiver
        </button>
        <button
          (click)="enableEssentialOnly()"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
        >
          Essentiel uniquement
        </button>
      </div>
    </div>

    <form [formGroup]="notificationForm" class="space-y-6">
      <!-- Canaux de notification -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Canaux de notification
        </h2>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg
                class="w-5 h-5 text-gray-400 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                ></path>
              </svg>
              <div>
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Notifications par email</label
                >
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  Recevoir les notifications par email
                </p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                formControlName="emailNotifications"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <button
                type="button"
                (click)="testNotification('email')"
                class="text-blue-600 hover:text-blue-800 text-xs"
              >
                Tester
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg
                class="w-5 h-5 text-gray-400 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"
                ></path>
              </svg>
              <div>
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Notifications push</label
                >
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  Notifications dans le navigateur
                </p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                formControlName="pushNotifications"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <button
                type="button"
                (click)="testNotification('push')"
                class="text-blue-600 hover:text-blue-800 text-xs"
              >
                Tester
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg
                class="w-5 h-5 text-gray-400 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                ></path>
              </svg>
              <div>
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Notifications SMS</label
                >
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  Notifications par message texte
                </p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <input
                type="checkbox"
                formControlName="smsNotifications"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <button
                type="button"
                (click)="testNotification('SMS')"
                class="text-blue-600 hover:text-blue-800 text-xs"
              >
                Tester
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Types de notifications -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        formGroupName="notificationTypes"
      >
        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Types de notifications
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            *ngFor="
              let type of [
                'leaveRequests',
                'performanceReviews',
                'payrollUpdates',
                'systemAlerts',
                'documentApprovals',
                'recruitmentUpdates'
              ]
            "
            class="flex items-center justify-between"
          >
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ getNotificationTypeLabel(type) }}
            </label>
            <input
              type="checkbox"
              [formControlName]="type"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <!-- Fréquence -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Fréquence des notifications
        </h2>

        <div class="space-y-3">
          <label class="flex items-center">
            <input
              type="radio"
              formControlName="frequency"
              value="IMMEDIATE"
              class="text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Immédiate - Recevoir les notifications instantanément</span
            >
          </label>
          <label class="flex items-center">
            <input
              type="radio"
              formControlName="frequency"
              value="DAILY"
              class="text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Quotidienne - Résumé quotidien des notifications</span
            >
          </label>
          <label class="flex items-center">
            <input
              type="radio"
              formControlName="frequency"
              value="WEEKLY"
              class="text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Hebdomadaire - Résumé hebdomadaire des notifications</span
            >
          </label>
        </div>
      </div>

      <!-- Heures de silence -->
      <div
        class="bg-background rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        formGroupName="quietHours"
      >
        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Heures de silence
        </h2>

        <div class="space-y-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              formControlName="enabled"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300"
              >Activer les heures de silence</span
            >
          </label>

          <div
            *ngIf="notificationForm.value.quietHours?.enabled"
            class="grid grid-cols-2 gap-4"
          >
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Début</label
              >
              <input
                type="time"
                formControlName="startTime"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Fin</label
              >
              <input
                type="time"
                formControlName="endTime"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div
            *ngIf="
              notificationForm.value.quietHours?.enabled && isQuietHoursActive()
            "
            class="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 rounded-lg p-3"
          >
            <div class="flex items-center">
              <svg
                class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                ></path>
              </svg>
              <span class="text-sm text-purple-700 dark:text-purple-300"
                >Mode silencieux actuellement actif</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Bouton de sauvegarde -->
      <div class="flex justify-end">
        <button
          type="button"
          (click)="saveSettings()"
          [disabled]="notificationForm.invalid || isSaving"
          class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md"
        >
          {{ isSaving ? "Sauvegarde..." : "Sauvegarder les paramètres" }}
        </button>
      </div>
    </form>
  </div>
</div>
