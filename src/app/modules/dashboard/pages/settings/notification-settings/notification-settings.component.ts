import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { 
  SettingsService, 
  NotificationSetting,
  UpdateNotificationSettingDto
} from '../../../../../core/services/settings/settings.service';

@Component({
  selector: 'app-notification-settings',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './notification-settings.component.html'
})
export class NotificationSettingsComponent implements OnInit, OnDestroy {
  notificationSettings: NotificationSetting | null = null;
  
  // Formulaire
  notificationForm: FormGroup;
  
  // États
  isLoading = false;
  isSaving = false;
  error: string | null = null;
  successMessage: string | null = null;
  
  private subscriptions = new Subscription();
  private currentUserId = 'current-user-id'; // TODO: Récupérer depuis le store

  constructor(
    private settingsService: SettingsService,
    private fb: FormBuilder
  ) {
    this.notificationForm = this.fb.group({
      emailNotifications: [true],
      pushNotifications: [true],
      smsNotifications: [false],
      notificationTypes: this.fb.group({
        leaveRequests: [true],
        performanceReviews: [true],
        payrollUpdates: [true],
        systemAlerts: [true],
        documentApprovals: [true],
        recruitmentUpdates: [false]
      }),
      frequency: ['IMMEDIATE'],
      quietHours: this.fb.group({
        enabled: [false],
        startTime: ['22:00'],
        endTime: ['08:00']
      })
    });
  }

  ngOnInit(): void {
    this.loadNotificationSettings();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadNotificationSettings(): void {
    this.isLoading = true;
    this.error = null;
    
    this.subscriptions.add(
      this.settingsService.getNotificationSettings(this.currentUserId).subscribe({
        next: (settings) => {
          this.notificationSettings = settings;
          this.populateForm(settings);
          this.isLoading = false;
        },
        error: (error) => {
          this.error = 'Erreur lors du chargement des paramètres de notification';
          this.isLoading = false;
          console.error('Erreur:', error);
        }
      })
    );
  }

  private populateForm(settings: NotificationSetting): void {
    this.notificationForm.patchValue({
      emailNotifications: settings.emailNotifications,
      pushNotifications: settings.pushNotifications,
      smsNotifications: settings.smsNotifications,
      notificationTypes: settings.notificationTypes,
      frequency: settings.frequency,
      quietHours: settings.quietHours
    });
  }

  saveSettings(): void {
    if (this.notificationForm.valid) {
      this.isSaving = true;
      this.error = null;
      this.successMessage = null;
      
      const updateData: UpdateNotificationSettingDto = this.notificationForm.value;
      
      this.subscriptions.add(
        this.settingsService.updateNotificationSettings(this.currentUserId, updateData).subscribe({
          next: (updatedSettings) => {
            this.notificationSettings = updatedSettings;
            this.successMessage = 'Paramètres sauvegardés avec succès';
            this.isSaving = false;
            setTimeout(() => this.successMessage = null, 3000);
          },
          error: (error) => {
            this.error = 'Erreur lors de la sauvegarde des paramètres';
            this.isSaving = false;
            console.error('Erreur:', error);
          }
        })
      );
    }
  }

  // Actions rapides
  enableAllNotifications(): void {
    this.notificationForm.patchValue({
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: true,
      notificationTypes: {
        leaveRequests: true,
        performanceReviews: true,
        payrollUpdates: true,
        systemAlerts: true,
        documentApprovals: true,
        recruitmentUpdates: true
      }
    });
  }

  disableAllNotifications(): void {
    this.notificationForm.patchValue({
      emailNotifications: false,
      pushNotifications: false,
      smsNotifications: false,
      notificationTypes: {
        leaveRequests: false,
        performanceReviews: false,
        payrollUpdates: false,
        systemAlerts: false,
        documentApprovals: false,
        recruitmentUpdates: false
      }
    });
  }

  enableEssentialOnly(): void {
    this.notificationForm.patchValue({
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      notificationTypes: {
        leaveRequests: true,
        performanceReviews: true,
        payrollUpdates: true,
        systemAlerts: true,
        documentApprovals: true,
        recruitmentUpdates: false
      }
    });
  }

  testNotification(type: string): void {
    // Simulation d'envoi de notification de test
    this.successMessage = `Notification de test envoyée via ${type}`;
    setTimeout(() => this.successMessage = null, 3000);
  }

  // Méthodes utilitaires
  getNotificationTypeLabel(type: string): string {
    const labels: { [key: string]: string } = {
      leaveRequests: 'Demandes de congés',
      performanceReviews: 'Évaluations de performance',
      payrollUpdates: 'Mises à jour de paie',
      systemAlerts: 'Alertes système',
      documentApprovals: 'Approbations de documents',
      recruitmentUpdates: 'Mises à jour de recrutement'
    };
    return labels[type] || type;
  }

  getFrequencyLabel(frequency: string): string {
    const labels: { [key: string]: string } = {
      IMMEDIATE: 'Immédiate',
      DAILY: 'Quotidienne',
      WEEKLY: 'Hebdomadaire'
    };
    return labels[frequency] || frequency;
  }

  isQuietHoursActive(): boolean {
    if (!this.notificationForm.value.quietHours?.enabled) return false;
    
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const startTime = this.timeStringToMinutes(this.notificationForm.value.quietHours.startTime);
    const endTime = this.timeStringToMinutes(this.notificationForm.value.quietHours.endTime);
    
    if (startTime > endTime) {
      // Heures de silence traversent minuit
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      return currentTime >= startTime && currentTime <= endTime;
    }
  }

  private timeStringToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  getActiveNotificationCount(): number {
    const form = this.notificationForm.value;
    let count = 0;
    
    if (form.emailNotifications) count++;
    if (form.pushNotifications) count++;
    if (form.smsNotifications) count++;
    
    return count;
  }

  getActiveTypeCount(): number {
    const types = this.notificationForm.value.notificationTypes;
    return Object.values(types).filter(Boolean).length;
  }

  clearMessages(): void {
    this.error = null;
    this.successMessage = null;
  }

  resetToDefaults(): void {
    if (confirm('Êtes-vous sûr de vouloir restaurer les paramètres par défaut ?')) {
      this.notificationForm.reset({
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: false,
        notificationTypes: {
          leaveRequests: true,
          performanceReviews: true,
          payrollUpdates: true,
          systemAlerts: true,
          documentApprovals: true,
          recruitmentUpdates: false
        },
        frequency: 'IMMEDIATE',
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00'
        }
      });
    }
  }
}
