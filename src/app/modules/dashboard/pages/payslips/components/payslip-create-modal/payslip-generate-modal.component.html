<div
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4 transform transition-transform"
  >
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold text-gray-800 dark:text-white">
        {{ generatedPayslip ? "Fiche de paie" : "Générer une fiche de paie" }}
      </h3>
      <button
        (click)="closePayslipModal()"
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        aria-label="Fermer la modal"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Formulaire de génération - affiché uniquement si aucune fiche n'est généree -->
    <form
      *ngIf="!generatedPayslip"
      [formGroup]="payslipForm"
      (ngSubmit)="generatePayslip()"
      novalidate
    >
      <div class="space-y-4">
        <!-- Sélection de l'employé -->
        <div>
          <label
            for="employeeId"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >Employé</label
          >
          <select
            id="employeeId"
            formControlName="employeeId"
            class="w-full px-3 py-2 border rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            [ngClass]="{
              'border-red-500': isFieldInvalid('employeeId')
            }"
          >
            <option value="">Sélectionner un employé</option>
            <option *ngFor="let employee of employees" [value]="employee.id">
              {{ employee.user?.profile?.firstName }}
              {{ employee.user?.profile?.lastName }}
            </option>
          </select>
          <p
            *ngIf="isFieldInvalid('employeeId')"
            class="mt-1 text-sm text-red-600"
          >
            L'employé est requis
          </p>
        </div>

        <!-- Période -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Mois</label
            >
            <select
              formControlName="month"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              [ngClass]="{
                'border-red-500': isFieldInvalid('month')
              }"
            >
              <option
                *ngFor="let month of months; let i = index"
                [value]="i + 1"
              >
                {{ month }}
              </option>
            </select>
            <p
              *ngIf="isFieldInvalid('month')"
              class="mt-1 text-sm text-red-600"
            >
              Le mois est requis
            </p>
          </div>
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Année</label
            >
            <input
              type="number"
              formControlName="year"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              [ngClass]="{
                'border-red-500': isFieldInvalid('year')
              }"
            />
            <p *ngIf="isFieldInvalid('year')" class="mt-1 text-sm text-red-600">
              L'année doit être entre {{ currentYear - 5 }} et
              {{ currentYear + 1 }}
            </p>
          </div>
        </div>

        <!-- Chargement -->
        <div *ngIf="isLoading" class="text-center py-4">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"
          ></div>
          <p class="mt-2 text-gray-600 dark:text-gray-400">
            Génération de la fiche de paie en cours...
          </p>
        </div>
      </div>

      <div class="flex justify-end space-x-2 mt-6">
        <button
          type="button"
          (click)="closePayslipModal()"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          Annuler
        </button>
        <button
          type="submit"
          [disabled]="payslipForm.invalid || isLoading"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Générer
        </button>
      </div>
    </form>

    <!-- Affichage de la fiche de paie générée -->
    <div *ngIf="generatedPayslip" class="space-y-4">
      <!-- En-tête avec infos de l'employé -->
      <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <div class="text-center mb-2">
          <h4 class="font-semibold text-lg text-gray-800 dark:text-gray-200">
            Fiche de paie
          </h4>
          <p class="text-gray-500 dark:text-gray-400">
            {{ getEmployeeName(generatedPayslip.employeeId) }}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ months[payslipForm.value.month - 1] }}
            {{ payslipForm.value.year }}
          </p>
        </div>
      </div>

      <!-- Détails de la fiche de paie -->
      <div class="space-y-3 border rounded-lg p-4">
        <div class="grid grid-cols-2 gap-2">
          <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
            Salaire brut:
          </div>
          <div class="text-right font-medium">
            {{
              generatedPayslip.grossSalary
                | currency : "EUR" : "symbol" : "1.2-2"
            }}
          </div>
        </div>

        <div class="pt-2 border-t border-gray-100 dark:border-gray-700">
          <div
            class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2"
          >
            Déductions:
          </div>

          <div class="grid grid-cols-2 gap-1 pl-2">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              Cotisations sociales:
            </div>
            <div class="text-right text-red-500">
              -{{
                generatedPayslip.socialSecurity
                  | currency : "EUR" : "symbol" : "1.2-2"
              }}
            </div>

            <div class="text-sm text-gray-600 dark:text-gray-400">
              Impôts sur le revenu:
            </div>
            <div class="text-right text-red-500">
              -{{
                generatedPayslip.deductions
                  | currency : "EUR" : "symbol" : "1.2-2"
              }}
            </div>

            <div class="text-sm text-gray-600 dark:text-gray-400">
              Autres déductions:
            </div>
            <div class="text-right text-red-500">
              -{{ 0 | currency : "EUR" : "symbol" : "1.2-2" }}
            </div>
          </div>
        </div>

        <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
          <div class="grid grid-cols-2 gap-2">
            <div class="font-semibold text-gray-800 dark:text-gray-200">
              Salaire net:
            </div>
            <div class="text-right font-bold text-blue-600 dark:text-blue-400">
              {{
                generatedPayslip.netSalary
                  | currency : "EUR" : "symbol" : "1.2-2"
              }}
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex flex-col sm:flex-row justify-between gap-2 mt-4">
        <button
          type="button"
          (click)="downloadPayslip()"
          class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 transition-colors flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Télécharger
        </button>
        <button
          type="button"
          (click)="closePayslipModal()"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
        >
          Terminer
        </button>
      </div>
    </div>
  </div>
</div>
