import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Payslip } from 'src/app/core/services/salary/salary.service';
import { EmployeeResponse } from 'src/app/core/models/employee.model';

@Component({
  selector: 'app-payslip-generate-modal',
  templateUrl: './payslip-generate-modal.component.html',
  imports: [CommonModule, ReactiveFormsModule],
  standalone: true,
})
export class PayslipGenerateModalComponent implements OnInit {
  @Input() employees: EmployeeResponse[] = [];
  @Input() employeeId: string | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() generate = new EventEmitter<{
    employeeId: string;
    year: number;
    month: number;
  }>();
  @Output() download = new EventEmitter<Payslip>();

  months = [
    'Janvier',
    'Février',
    'Mars',
    'Avril',
    '<PERSON>',
    'Juin',
    '<PERSON><PERSON><PERSON>',
    'Août',
    'Septembre',
    'Octobre',
    'Novembre',
    'Décembre',
  ];
  currentYear = new Date().getFullYear();
  isLoading = false;
  generatedPayslip: Payslip | null = null;

  payslipForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    this.payslipForm = this.fb.group({
      employeeId: [this.employeeId, Validators.required],
      month: [
        new Date().getMonth() + 1,
        [Validators.required, Validators.min(1), Validators.max(12)],
      ],
      year: [
        this.currentYear,
        [
          Validators.required,
          Validators.min(this.currentYear - 5),
          Validators.max(this.currentYear + 1),
        ],
      ],
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.payslipForm.get(fieldName);
    return !!field && field.invalid && (field.dirty || field.touched);
  }

  closePayslipModal(): void {
    this.close.emit();
    this.generatedPayslip = null;
  }

  generatePayslip(): void {
    if (this.payslipForm.invalid) {
      this.payslipForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    this.generatedPayslip = null;

    const formValue = this.payslipForm.value;
    const payload = {
      employeeId: formValue.employeeId,
      year: formValue.year,
      month: formValue.month,
    };

    this.generate.emit(payload);
  }

  downloadPayslip(): void {
    if (this.generatedPayslip) {
      this.download.emit(this.generatedPayslip);
    }
  }

  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    if (employee?.user?.profile) {
      return `${employee.user.profile.firstName} ${employee.user.profile.lastName}`;
    }
    return 'Employé';
  }

  @Input() set generationResult(result: {
    payslip: Payslip | null;
    error: string | null;
  }) {
    this.isLoading = false;

    if (result.error) {
      // Gérer l'erreur (peut-être afficher un message)
      console.error('Erreur lors de la génération:', result.error);
      return;
    }

    if (result.payslip) {
      this.generatedPayslip = result.payslip;
    }
  }
}
