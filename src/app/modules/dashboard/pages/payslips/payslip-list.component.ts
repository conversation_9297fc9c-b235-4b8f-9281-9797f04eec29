import { Component, Input, OnInit } from '@angular/core';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { EmployeeNew } from 'src/app/core/models/employee.model';
import { CurrencyPipe, NgClass, NgFor, NgIf } from '@angular/common';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

import { SalaryService } from 'src/app/core/services/salary/salary.service';
import { PayslipGenerateModalComponent } from './components/payslip-create-modal/payslip-generate-modal.component';
import { Payslip } from 'src/app/core/services/payslip/payslip.service';

@Component({
  selector: 'app-payslip-list',
  templateUrl: './payslip-list.component.html',
  imports: [
    NgClass,

    CurrencyPipe,
    NgFor,
    PayslipGenerateModalComponent,
    NgIf,
    FormsModule,
    ReactiveFormsModule,
  ],
  standalone: true,
})
export class PayslipListComponent implements OnInit {
  @Input() employeeId!: string;

  payslips: Payslip[] = [];
  employees: EmployeeNew[] = [];
  selectedPayslip: Payslip | null = null;
  filterForm: FormGroup;
  showModal = false;
  showPreviewModal = false;
  lastUpdateMinutes = 0;
  isLoading = false;
  company!: Company;
  generationResult: { payslip: any | null; error: string | null } = {
    payslip: null,
    error: null,
  };
  today = new Date();

  availableMonths = [
    { value: 1, label: 'Janvier' },
    { value: 2, label: 'Février' },
    { value: 3, label: 'Mars' },
    { value: 4, label: 'Avril' },
    { value: 5, label: 'Mai' },
    { value: 6, label: 'Juin' },
    { value: 7, label: 'Juillet' },
    { value: 8, label: 'Août' },
    { value: 9, label: 'Septembre' },
    { value: 10, label: 'Octobre' },
    { value: 11, label: 'Novembre' },
    { value: 12, label: 'Décembre' },
  ];
  availableYears: number[] = Array.from(
    { length: 6 },
    (_, i) => this.today.getFullYear() - 5 + i
  );

  constructor(
    private salaryService: SalaryService,
    private employeeService: EmployeeService,
    private store: Store<AppState>,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      employeeId: [''],
      month: [''],
      year: [''],
    });

    this.lastUpdateMinutes = Math.floor(Math.random() * 60);
    setInterval(() => this.lastUpdateMinutes++, 60000);
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
      this.loadEmployees();
      this.loadAllPayslips();
    });
  }

  loadEmployees(): void {
    this.employeeService.getEmployees(this.company.id!).subscribe((data) => {
      this.employees = (data.data || data) as any;
    });
  }

  loadAllPayslips(): void {
    this.isLoading = true;

    const employeeId =
      this.employeeId || this.filterForm.get('employeeId')?.value;
    const companyId = this.company.id;

    if (employeeId) {
      this.salaryService.getEmployeePayslips(employeeId).subscribe({
        next: (data) => {
          this.payslips = this.filterPayslips(data as any);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading payslips:', error);
          this.isLoading = false;
        },
      });
    } else if (companyId) {
      this.salaryService.getCompanyPayslips(companyId).subscribe({
        next: (data) => {
          this.payslips = this.filterPayslips(data as any);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading payslips:', error);
          this.isLoading = false;
        },
      });
    }
  }

  filterPayslips(payslips: Payslip[]): Payslip[] {
    const month = this.filterForm.get('month')?.value;
    const year = this.filterForm.get('year')?.value;

    if (!month && !year) return payslips;

    return payslips.filter((payslip) => {
      const monthMatch = !month || payslip.month === +month;
      const yearMatch = !year || payslip.year === +year;
      return monthMatch && yearMatch;
    });
  }

  selectPayslip(payslip: any): void {
    this.selectedPayslip = payslip;
  }

  applyFilters(): void {
    this.loadAllPayslips();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadAllPayslips();
  }

  openModal(): void {
    this.showModal = true;
    this.selectedPayslip = null;
  }

  closeModal(): void {
    this.showModal = false;
    this.generationResult = { payslip: null, error: null };
  }

  generatePayslip(payload: {
    employeeId: string;
    year: number;
    month: number;
  }): void {
    this.isLoading = true;
    this.generationResult = { payslip: null, error: null };

    this.salaryService.generatePayslip(payload).subscribe({
      next: (payslip) => {
        this.generationResult = { payslip: payslip as any, error: null };
        this.payslips.unshift(payslip as any);
        this.lastUpdateMinutes = 0;
        this.isLoading = false;
      },
      error: (error) => {
        this.generationResult = { payslip: null, error: error.message };
        this.isLoading = false;
      },
    });
  }

  deletePayslip(): void {
    if (!this.selectedPayslip) return;

    if (
      confirm(`Supprimer la fiche de paie (ID: ${this.selectedPayslip.id})?`)
    ) {
      this.salaryService.deletePayslip(this.selectedPayslip.id!).subscribe({
        next: () => {
          this.payslips = this.payslips.filter(
            (p) => p.id !== this.selectedPayslip?.id
          );
          this.selectedPayslip = null;
          this.lastUpdateMinutes = 0;
        },
        error: (error) => console.error('Error deleting payslip:', error),
      });
    }
  }

  printPayslip(): void {
    if (!this.selectedPayslip) return;
    this.showPreviewModal = true;
  }

  closePreviewModal(): void {
    this.showPreviewModal = false;
  }

  months = [
    'Janvier',
    'Février',
    'Mars',
    'Avril',
    'Mai',
    'Juin',
    'Juillet',
    'Août',
    'Septembre',
    'Octobre',
    'Novembre',
    'Décembre',
  ];

  handlePayslipDownload(payslip: any): void {
    // Récupération des informations de l'employé
    let employeeName = 'Employé';

    if (payslip.employee?.user?.profile) {
      employeeName = `${payslip.employee.user.profile.firstName} ${payslip.employee.user.profile.lastName}`;
    } else {
      const employee = this.employees.find(
        (emp) => emp.id === payslip.employeeId
      );
      if (employee?._personalInfo) {
        employeeName = `${employee._personalInfo.firstName} ${employee._personalInfo.lastName}`;
      }
    }

    const doc = new jsPDF();

    // En-tête
    doc.setFontSize(20);
    doc.text('Bulletin de Salaire', 105, 20, { align: 'center' });

    // Informations générales
    doc.setFontSize(12);
    doc.text(`Employé: ${employeeName}`, 20, 40);
    doc.text(
      `Période: ${this.months[payslip.month - 1]} ${payslip.year}`,
      20,
      50
    );

    // Date d'émission
    const today = new Date();
    doc.text(`Date d'émission: ${today.toLocaleDateString()}`, 20, 60);

    // Détails du salaire
    let y = 80;
    doc.setFontSize(11);

    // En-têtes
    doc.setFont('helvetica', 'bold');
    doc.text('Description', 20, y);
    doc.text('Montant ($)', 150, y);
    doc.line(20, y + 5, 190, y + 5);
    y += 15;

    // Éléments
    doc.setFont('helvetica', 'normal');
    doc.text('Salaire brut', 20, y);
    doc.text(payslip.grossSalary.toFixed(2), 150, y);
    y += 10;

    doc.text('Cotisations sociales', 20, y);
    doc.text(`- ${payslip.socialSecurity.toFixed(2)}`, 150, y);
    y += 10;

    doc.text('Impôts sur le revenu', 20, y);
    doc.text(`- ${payslip.taxDeductions.toFixed(2)}`, 150, y);
    y += 10;

    doc.text('Autres déductions', 20, y);
    doc.text(`- ${payslip.otherDeductions.toFixed(2)}`, 150, y);
    y += 15;

    // Ligne de séparation
    doc.line(20, y, 190, y);
    y += 15;

    // Total
    doc.setFont('helvetica', 'bold');
    doc.text('Salaire net:', 110, y);
    doc.text(`${payslip.netSalary.toFixed(2)} €`, 150, y);

    // Pied de page
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text('Ce document est un bulletin de salaire officiel.', 105, 280, {
      align: 'center',
    });

    // Référence
    if (payslip.id) {
      doc.text(`Référence: ${payslip.id}`, 20, 270);
    }

    // Télécharger le PDF
    doc.save(
      `fiche-paie-${employeeName
        .replace(/\s+/g, '-')
        .toLowerCase()}-${this.months[payslip.month - 1].toLowerCase()}-${
        payslip.year
      }.pdf`
    );
  }
  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee._personalInfo?.firstName} ${employee._personalInfo?.lastName}`
      : `Employé #${employeeId}`;
  }

  getMonthName(monthNumber: number): string {
    return (
      this.availableMonths[monthNumber - 1]?.label || `Mois ${monthNumber}`
    );
  }
}
