import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ButtonComponent } from 'src/app/shared/components/button/button.component';
import { DepartmentService } from 'src/app/core/services/department/department.service';
import { PositionService } from 'src/app/core/services/department/position.service';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-employee-create-modal',
  templateUrl: './employee-create-modal.component.html',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    NgClass,
    NgFor,
    ButtonComponent,
  ],
  standalone: true,
})
export class EmployeeCreateModalComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() employeeCreated = new EventEmitter<any>();

  form: FormGroup;
  departments: any[] = [];
  positions: any[] = [];
  isLoadingDepartments = false;
  isLoadingPositions = false;
  currentCompanyId!: string;

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private positionService: PositionService,
    private store: Store<AppState>
  ) {
    this.form = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      birthDate: ['', Validators.required],
      hireDate: ['', Validators.required],
      departmentId: ['', Validators.required],
      positionId: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.loadDepartments();

    // Surveille les changements de département pour charger les postes
    this.form.get('departmentId')?.valueChanges.subscribe((departmentId) => {
      if (departmentId) {
        this.loadPositions(departmentId);
        this.form.get('positionId')?.reset(); // Réinitialise le poste quand le département change
      }
    });
  }

  loadDepartments(): void {
    this.isLoadingDepartments = true;
    this.departmentService.getAllDepartments(this.currentCompanyId).subscribe({
      next: (departments) => {
        this.departments = departments;
        this.isLoadingDepartments = false;
      },
      error: () => {
        this.isLoadingDepartments = false;
      },
    });
  }

  loadPositions(departmentId: string): void {
    this.isLoadingPositions = true;
    this.positionService
      .getAllPositions(this.currentCompanyId, departmentId)
      .subscribe({
        next: (positions) => {
          this.positions = positions;
          this.isLoadingPositions = false;
        },
        error: () => {
          this.isLoadingPositions = false;
        },
      });
  }

  onSubmit(): void {
    if (this.form.valid) {
      this.employeeCreated.emit(this.form.value);
    }
  }

  closeModal(): void {
    this.close.emit();
  }
}
