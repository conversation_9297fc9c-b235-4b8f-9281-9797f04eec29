<form class="space-y-4 mt-4" [formGroup]="form" (ngSubmit)="onSubmit()">
  <!-- Informations personnelles -->
  <div class="form__group">
    <div class="relative">
      <input
        type="text"
        id="firstName"
        formControlName="firstName"
        class="peer block w-full"
        [ngClass]="{
          'border-red-500':
            form.get('firstName')?.touched && form.get('firstName')?.errors
        }"
        placeholder=" "
      />
      <label
        for="firstName"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >Prénom</label
      >
    </div>
    <div
      *ngIf="form.get('firstName')?.touched && form.get('firstName')?.errors"
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('firstName')?.errors?.['required']">
        Le prénom est requis.
      </div>
    </div>
  </div>

  <div class="form__group">
    <div class="relative">
      <input
        type="text"
        id="lastName"
        formControlName="lastName"
        class="peer block w-full"
        [ngClass]="{
          'border-red-500':
            form.get('lastName')?.touched && form.get('lastName')?.errors
        }"
        placeholder=" "
      />
      <label
        for="lastName"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >Nom</label
      >
    </div>
    <div
      *ngIf="form.get('lastName')?.touched && form.get('lastName')?.errors"
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('lastName')?.errors?.['required']">
        Le nom est requis.
      </div>
    </div>
  </div>

  <!-- Contact -->
  <div class="form__group">
    <div class="relative">
      <input
        type="email"
        id="email"
        formControlName="email"
        class="peer block w-full"
        [ngClass]="{
          'border-red-500':
            form.get('email')?.touched && form.get('email')?.errors
        }"
        placeholder=" "
      />
      <label
        for="email"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-95 peer-focus:px-2 peer-focus:text-primary"
        >Email</label
      >
    </div>
    <div
      *ngIf="form.get('email')?.touched && form.get('email')?.errors"
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('email')?.errors?.['required']">
        L'email est requis.
      </div>
      <div *ngIf="form.get('email')?.errors?.['email']">
        L'email doit être valide.
      </div>
    </div>
  </div>

  <!-- Sélection du département -->
  <div class="form__group">
    <div class="relative">
      <select
        id="departmentId"
        formControlName="departmentId"
        class="peer block w-full"
        [ngClass]="{
          'border-red-500':
            form.get('departmentId')?.touched &&
            form.get('departmentId')?.errors
        }"
      >
        <option value="">Sélectionnez un département</option>
        <option *ngFor="let department of departments" [value]="department.id">
          {{ department.name }}
        </option>
      </select>
      <label
        for="departmentId"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground"
        >Département</label
      >
    </div>
    <div
      *ngIf="
        form.get('departmentId')?.touched && form.get('departmentId')?.errors
      "
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('departmentId')?.errors?.['required']">
        Le département est requis.
      </div>
    </div>
    <div *ngIf="isLoadingDepartments" class="text-sm text-gray-500 mt-1">
      Chargement des départements...
    </div>
  </div>

  <!-- Sélection du poste -->
  <div class="form__group">
    <div class="relative">
      <select
        id="positionId"
        formControlName="positionId"
        class="peer block w-full"
        [disabled]="!form.get('departmentId')?.value"
        [ngClass]="{
          'border-red-500':
            form.get('positionId')?.touched && form.get('positionId')?.errors,
          'bg-gray-100': !form.get('departmentId')?.value
        }"
      >
        <option value="">Sélectionnez un poste</option>
        <option *ngFor="let position of positions" [value]="position.id">
          {{ position.title }}
        </option>
      </select>
      <label
        for="positionId"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground"
        >Poste</label
      >
    </div>
    <div
      *ngIf="form.get('positionId')?.touched && form.get('positionId')?.errors"
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('positionId')?.errors?.['required']">
        Le poste est requis.
      </div>
    </div>
    <div *ngIf="isLoadingPositions" class="text-sm text-gray-500 mt-1">
      Chargement des postes...
    </div>
    <div
      *ngIf="
        !isLoadingPositions &&
        form.get('departmentId')?.value &&
        positions.length === 0
      "
      class="text-sm text-gray-500 mt-1"
    >
      Aucun poste disponible pour ce département
    </div>
  </div>

  <!-- Dates -->
  <div class="form__group">
    <div class="relative">
      <input
        type="date"
        id="birthDate"
        formControlName="birthDate"
        class="peer block w-full"
        [ngClass]="{
          'border-red-500':
            form.get('birthDate')?.touched && form.get('birthDate')?.errors
        }"
      />
      <label
        for="birthDate"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground"
        >Date de naissance</label
      >
    </div>
    <div
      *ngIf="form.get('birthDate')?.touched && form.get('birthDate')?.errors"
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('birthDate')?.errors?.['required']">
        La date de naissance est requise.
      </div>
    </div>
  </div>

  <div class="form__group">
    <div class="relative">
      <input
        type="date"
        id="hireDate"
        formControlName="hireDate"
        class="peer block w-full"
        [ngClass]="{
          'border-red-500':
            form.get('hireDate')?.touched && form.get('hireDate')?.errors
        }"
      />
      <label
        for="hireDate"
        class="absolute top-2 left-1 z-10 origin-[0] -translate-y-4 scale-95 transform bg-background px-2 text-sm text-muted-foreground"
        >Date d'embauche</label
      >
    </div>
    <div
      *ngIf="form.get('hireDate')?.touched && form.get('hireDate')?.errors"
      class="text-red-500 text-sm mt-1"
    >
      <div *ngIf="form.get('hireDate')?.errors?.['required']">
        La date d'embauche est requise.
      </div>
    </div>
  </div>

  <!-- Boutons d'action -->
  <div class="flex justify-end space-x-3 mt-6">
    <app-button
      type="button"
      impact="none"
      tone="light"
      shape="rounded"
      size="medium"
      (click)="closeModal()"
    >
      Annuler
    </app-button>
    <app-button
      type="submit"
      impact="bold"
      tone="primary"
      shape="rounded"
      size="medium"
    >
      Ajouter l'employé
    </app-button>
  </div>
</form>
