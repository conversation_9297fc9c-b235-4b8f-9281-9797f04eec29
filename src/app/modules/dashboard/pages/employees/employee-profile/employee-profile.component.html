<div
  class="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900"
  *ngIf="employee"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-xl p-6 mb-6 transition-all hover:shadow-lg"
  >
    <div
      class="flex flex-col md:flex-row items-start md:items-center justify-between"
    >
      <div class="flex items-center mb-4 md:mb-0">
        <div class="relative group">
          <div
            class="w-12 h-12 rounded-lg bg-gray-100 text-gray-600 flex items-center justify-center mr-5 overflow-hidden"
          >
            <span class="text-sm font-medium"
              >{{ employee._personalInfo.firstName?.charAt(0)
              }}{{ employee._personalInfo.lastName?.charAt(0) }}</span
            >

            <!-- Bouton pour changer de photo (apparaît au survol) -->
            <div
              class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
            >
              <span class="text-white text-xs">Changer</span>
            </div>
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
            {{ employee._personalInfo.firstName }}
            {{ employee._personalInfo.lastName }}
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            {{ employee._employmentDetails.position?.title }}
          </p>
          <div class="flex items-center mt-1">
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span>
              {{ employee._employmentDetails.status }}
            </span>
          </div>
        </div>
      </div>
      <div class="flex space-x-2">
        <button
          (click)="toggleEditMode()"
          [ngClass]="
            isEditMode
              ? 'px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 shadow-sm transition-all hover:shadow dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 focus:outline-none'
              : 'rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50'
          "
        >
          {{ isEditMode ? "Annuler" : "Modifier le profil" }}
        </button>
        <button
          *ngIf="!isEditMode"
          (click)="generatePassword()"
          class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"
            />
          </svg>
          Générer mot de passe
        </button>
        <button
          *ngIf="isEditMode"
          (click)="saveProfile()"
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        >
          Enregistrer
        </button>

        <button
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 shadow-sm transition-all hover:shadow dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 focus:outline-none"
        >
          <span class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
              />
            </svg>
            Exporter
          </span>
        </button>
      </div>
    </div>
  </div>

  <div class="mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav
        class="-mb-px flex space-x-8 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600"
      >
        <a
          *ngFor="let tab of tabs"
          (click)="activeTab = tab.id"
          [ngClass]="{
            'border-blue-500 text-blue-600 dark:text-blue-400':
              activeTab === tab.id,
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600':
              activeTab !== tab.id
          }"
          class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer transition-colors flex items-center"
        >
          <span class="flex items-center">
            <span class="mr-2" [innerHTML]="getActivityIcon(tab.id)"></span>
            {{ tab.name }}
          </span>
        </a>
      </nav>
    </div>
  </div>

  <!-- Contenu des onglets avec effets de transition -->
  <div
    [ngSwitch]="activeTab"
    class="transition-opacity duration-300"
    [@fadeAnimation]="activeTab"
  >
    <!-- Onglet Informations personnelles -->
    <div
      *ngSwitchCase="'personal'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
          Informations personnelles
        </h2>
        <div class="flex space-x-2" *ngIf="!isEditMode">
          <button
            (click)="toggleEditMode()"
            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
          </button>
        </div>
      </div>

      <div *ngIf="!isEditMode" class="animate-fade-in">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              ID Employé
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ employee.id }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Date d'embauche
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ employee._employmentDetails.hireDate | date : "dd/MM/yyyy" }}
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Poste
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ employee._employmentDetails.position?.title }}
            </p>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Email
            </p>
            <p
              class="text-gray-800 dark:text-white font-medium flex items-center"
            >
              {{ employee._personalInfo.email }}
              <button
                class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </button>
            </p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <p
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
            >
              Téléphone
            </p>
            <p class="text-gray-800 dark:text-white font-medium">
              {{ employee._personalInfo.phoneNumber }}
            </p>
          </div>
        </div>
      </div>

      <div *ngIf="isEditMode" [formGroup]="editForm" class="animate-fade-in">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >ID Employé</label
            >
            <input
              type="text"
              [value]="employee.id"
              disabled
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg bg-gray-100 text-gray-500 focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-gray-400"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Date d'embauche</label
            >
            <input
              type="date"
              formControlName="hireDate"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Departement</label
            >
            <select
              formControlName="departmentId"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option
                *ngFor="let department of departments"
                [value]="department.id"
              >
                {{ department.name }}
              </option>
            </select>
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Position</label
            >
            <select
              formControlName="positionId"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            >
              <option *ngFor="let position of positions" [value]="position.id">
                {{ position.title }}
              </option>
            </select>
          </div>

          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Email</label
            >
            <input
              type="email"
              formControlName="email"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
          <div class="mb-4">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >Téléphone</label
            >
            <input
              type="tel"
              formControlName="phone"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:outline-none dark:bg-gray-800 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <div class="flex justify-end space-x-2 mt-4">
          <button
            (click)="toggleEditMode()"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          >
            Annuler
          </button>
          <button
            (click)="saveProfile()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Enregistrer
          </button>
        </div>
      </div>
    </div>

    <!-- Onglet Salaires avec visuel amélioré -->
    <div
      *ngSwitchCase="'salary'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <app-salary-list [employeeId]="employee.id"></app-salary-list>
    </div>

    <!-- Onglet Fiches de paie -->
    <div
      *ngSwitchCase="'payslips'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <app-payslip-list [employeeId]="employee.id"></app-payslip-list>
    </div>

    <!-- Autres onglets avec le même style amélioré... -->
    <div
      *ngSwitchCase="'leave'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold mb-4">Demandes de congés</h2>
        <button
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Ajouter
        </button>
      </div>
      <div class="mb-6">
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="flex items-center">
            <div class="mr-4 bg-blue-100 rounded-full p-2">
              <svg
                class="w-6 h-6 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <div>
              <p class="font-medium">Jours de congés restants</p>
              <p class="text-2xl font-bold">
                {{ calculateRemainingLeaves() }} jours
              </p>
            </div>
          </div>
        </div>
      </div>

      <div
        *ngIf="
          !employee._employmentDetails.leaves ||
          employee._employmentDetails.leaves.length === 0
        "
        class="text-gray-500 italic"
      >
        Aucune demande de congé disponible
      </div>

      <table
        *ngIf="
          employee._employmentDetails.leaves &&
          employee._employmentDetails.leaves.length > 0
        "
        class="min-w-full divide-y divide-gray-200"
      >
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Type
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Date de début
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Date de fin
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Durée
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Statut
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let leave of getSortedLeaves()">
            <td class="px-6 py-4 whitespace-nowrap">{{ leave.leaveType }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ leave.startDate | date : "dd/MM/yyyy" }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ leave.endDate | date : "dd/MM/yyyy" }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ calculateLeaveDuration(leave) }} jours
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                [ngClass]="getLeaveStatusClass(leave.status)"
                class="px-2 py-1 rounded-full text-xs"
              >
                {{ leave.status }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Onglet Évaluations -->
    <div
      *ngSwitchCase="'evaluations'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold mb-4">Évaluations de performance</h2>
        <button
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Ajouter
        </button>
      </div>
      <div
        *ngIf="
          !employee._employmentDetails.performanceEvaluations ||
          employee._employmentDetails.performanceEvaluations.length === 0
        "
        class="text-gray-500 italic"
      >
        Aucune évaluation disponible
      </div>

      <div
        *ngFor="let eval of getSortedEvaluations()"
        class="mb-6 border-b pb-6 last:border-0"
      >
        <div class="flex justify-between items-start">
          <div>
            <h3 class="font-medium text-lg">
              Évaluation du {{ eval.evaluationDate | date : "dd/MM/yyyy" }}
            </h3>
            <p class="text-gray-500 text-sm mb-2">
              Par: {{ eval.evaluatorName }}
            </p>
          </div>
          <div>
            <span class="text-lg font-bold">{{ eval.overallRating }}/5</span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <h4 class="font-medium mb-2">Points forts</h4>
            <p class="text-gray-700">{{ eval.strengths }}</p>
          </div>
          <div>
            <h4 class="font-medium mb-2">Axes d'amélioration</h4>
            <p class="text-gray-700">{{ eval.areasForImprovement }}</p>
          </div>
        </div>

        <div class="mt-4">
          <h4 class="font-medium mb-2">Commentaires</h4>
          <p class="text-gray-700">{{ eval.comments }}</p>
        </div>
      </div>
    </div>

    <!-- Onglet Formations -->
    <div
      *ngSwitchCase="'trainings'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold mb-4">Formations suivies</h2>
        <button
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Ajouter
        </button>
      </div>
      <div
        *ngIf="
          !employee._employmentDetails.trainings ||
          employee._employmentDetails.trainings.length === 0
        "
        class="text-gray-500 italic"
      >
        Aucune formation disponible
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div
          *ngFor="let training of getSortedTrainings()"
          class="border rounded-lg overflow-hidden"
        >
          <div class="bg-gray-50 px-4 py-2 border-b">
            <h3 class="font-medium">{{ training.trainingName }}</h3>
          </div>
          <div class="p-4">
            <p class="text-sm text-gray-500 mb-2">
              {{ training.startDate | date : "dd/MM/yyyy" }} -
              {{ training.endDate | date : "dd/MM/yyyy" }}
            </p>
            <p class="mb-2">{{ training.description }}</p>
            <div *ngIf="training.certificationObtained" class="mt-2">
              <span
                class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs"
              >
                Certification obtenue
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Onglet Feuilles de temps -->
    <div
      *ngSwitchCase="'timesheets'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <div
        *ngIf="
          !employee._employmentDetails.timesheets ||
          employee._employmentDetails.timesheets.length === 0
        "
        class="text-gray-500 italic"
      >
        Aucune feuille de temps disponible
      </div>

      <app-timesheet-list [employeeId]="employee.id"></app-timesheet-list>
    </div>

    <!-- Onglet Activités récentes -->
    <div
      *ngSwitchCase="'activity'"
      class="bg-white dark:bg-gray-800 rounded-xl p-6"
    >
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold mb-4">Activités récentes</h2>
        <button
          class="flex justify-center items-center rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Ajouter
        </button>
      </div>
      <div *ngIf="recentActivities.length === 0" class="text-gray-500 italic">
        Aucune activité récente
      </div>

      <div class="space-y-4">
        <div
          *ngFor="let activity of recentActivities"
          class="flex items-start bg-gray-50 p-4 rounded-lg"
        >
          <div
            class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4"
          >
            <span class="text-blue-600">{{
              getActivityIcon(activity.type)
            }}</span>
          </div>
          <div class="flex-1">
            <div class="flex justify-between items-start">
              <h3 class="font-medium">{{ activity.title }}</h3>
              <span class="text-sm text-gray-500">{{
                activity.date | date : "dd/MM/yyyy HH:mm"
              }}</span>
            </div>
            <p class="text-gray-600">{{ activity.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
