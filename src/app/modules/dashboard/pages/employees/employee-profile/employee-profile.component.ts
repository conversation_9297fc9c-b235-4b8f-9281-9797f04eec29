import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import {
  DatePipe,
  NgClass,
  NgFor,
  NgIf,
  NgSwitch,
  NgSwitchCase,
} from '@angular/common';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { TimesheetListComponent } from '../../timesheets/timesheet-list.component';
import { EmployeeResponse } from 'src/app/core/models/employee.model';
import { SalaryListComponent } from '../../salaries/salary-list.component';
import { PayslipListComponent } from '../../payslips/payslip-list.component';
import { AppState } from 'src/app/core/store/app.state';
import { select, Store } from '@ngrx/store';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { Department, Position } from 'src/app/core/models/company.model';
import { DepartmentService } from 'src/app/core/services/department/department.service';
import { PositionService } from 'src/app/core/services/department/position.service';

@Component({
  selector: 'app-employee-profile',
  templateUrl: './employee-profile.component.html',
  imports: [
    NgClass,
    NgSwitch,
    NgSwitchCase,
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    NgFor,
    DatePipe,
    TimesheetListComponent,
    SalaryListComponent,
    PayslipListComponent,
  ],
  animations: [
    trigger('fadeAnimation', [
      state('void', style({ opacity: 0 })),
      transition(':enter, :leave', [animate('300ms ease-in-out')]),
    ]),
  ],
})
export class EmployeeProfileComponent implements OnInit {
  hasAdminRights: boolean = false; // Changé de 'any' à boolean pour plus de clarté

  employee!: EmployeeResponse;
  departments: Department[] = [];
  positions: Position[] = [];
  isEditMode = false;
  editForm!: FormGroup;
  recentActivities: any[] = [];
  currentCompanyId!: string;

  // Configuration des onglets
  tabs = [
    { id: 'personal', name: 'Informations' },
    { id: 'salary', name: 'Salaires' },

    { id: 'payslips', name: 'Fiches de paie' },
    { id: 'leave', name: 'Congés' },
    { id: 'evaluations', name: 'Évaluations' },
    { id: 'trainings', name: 'Formations' },
    { id: 'timesheets', name: 'Feuilles de temps' },
    { id: 'activity', name: 'Activités récentes' },
  ];
  activeTab = 'personal';

  constructor(
    private employeeService: EmployeeService,
    private store: Store<AppState>,
    private route: ActivatedRoute,
    private departmentService: DepartmentService,
    private positionService: PositionService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.currentCompanyId = company.id!;
    });
    this.route.params.subscribe((params) => {
      const employeeId = params['id'];
      this.loadEmployeeData(employeeId);
    });
  }

  loadEmployeeData(employeeId: string): void {
    this.employeeService
      .getEmployeeById(this.currentCompanyId, employeeId)
      .subscribe({
        next: (data) => {
          this.employee = data as any;
          this.loadDepartments();
        },
        error: () => {},
      });
  }

  setupDepartmentChangeListener(): void {
    this.editForm
      .get('departmentId')
      ?.valueChanges.subscribe((departmentId) => {
        if (departmentId) {
          console.log('Department changed to:', departmentId); // Debug
          this.loadPositions(departmentId);
          this.editForm.get('positionId')?.reset();
        }
      });
  }

  loadRecentActivities(_employeeId: number): void {}

  initializeEditForm(): void {
    this.editForm = this.fb.group({
      firstName: [this.employee._personalInfo.firstName, Validators.required],
      lastName: [this.employee._personalInfo.lastName, Validators.required],
      email: [
        this.employee._personalInfo.email,
        [Validators.required, Validators.email],
      ],
      birthDate: [this.employee._personalInfo.birthDate, Validators.required],
      hireDate: [
        this.formatDateForInput(this.employee._employmentDetails.hireDate),
        Validators.required,
      ],
      departmentId: [
        this.employee._employmentDetails.department?.id,
        Validators.required,
      ],
      positionId: [
        this.employee._employmentDetails.position?.id,
        Validators.required,
      ],
      phone: [this.employee._personalInfo.phoneNumber, Validators.required],
    });
    this.setupDepartmentChangeListener();
  }

  formatDateForInput(dateString: string): string {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }

  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    if (!this.isEditMode) {
      this.initializeEditForm();
    }
  }

  saveProfile(): void {
    // TODO: Adapter cette méthode au nouveau format de données
    // Le format de mise à jour doit correspondre au nouveau format backend
    const updatedEmployee = {
      firstName: this.editForm.value.firstName,
      lastName: this.editForm.value.lastName,
      email: this.editForm.value.email,
      phoneNumber: this.editForm.value.phone,
      birthDate: this.editForm.value.birthDate,
      hireDate: this.editForm.value.hireDate,
      positionId: this.editForm.value.positionId,
      departmentId: this.editForm.value.departmentId,
    };

    console.log(
      'Save profile functionality needs to be adapted to new data format',
      updatedEmployee
    );

    // Ancienne implémentation commentée :
    // this.employeeService
    //   .updateEmployee(this.currentCompanyId, this.employee.id, updatedEmployee)
    //   .subscribe({
    //     next: (employee) => {
    //       this.loadEmployeeData(employee.id);
    //     },
    //     error: (error) => {
    //       console.log(error);
    //     },
    //   });
  }

  loadDepartments(): void {
    this.departmentService.getAllDepartments(this.currentCompanyId).subscribe({
      next: (departments) => {
        this.departments = departments;
        this.initializeEditForm();
        if (this.employee._employmentDetails.department?.id) {
          this.loadPositions(this.employee._employmentDetails.department.id);
        }
      },
      error: () => {},
    });
  }

  loadPositions(departmentId: string): void {
    this.positionService
      .getAllPositions(this.currentCompanyId, departmentId)
      .subscribe({
        next: (positions) => {
          this.positions = positions;
        },
        error: () => {},
      });
  }

  getSortedLeaves(): any[] {
    if (!this.employee._employmentDetails.leaves) {
      return [];
    }
    return [...this.employee._employmentDetails.leaves].sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    );
  }

  getSortedEvaluations(): any[] {
    if (!this.employee._employmentDetails.performanceEvaluations) {
      return [];
    }
    return [...this.employee._employmentDetails.performanceEvaluations].sort(
      (a, b) =>
        new Date(b.evaluationDate).getTime() -
        new Date(a.evaluationDate).getTime()
    );
  }

  getSortedTrainings(): any[] {
    if (!this.employee._employmentDetails.trainings) {
      return [];
    }
    return [...this.employee._employmentDetails.trainings].sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    );
  }

  getSortedTimesheets(): any[] {
    if (!this.employee._employmentDetails.timesheets) {
      return [];
    }
    return [...this.employee._employmentDetails.timesheets].sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    );
  }

  calculateRemainingLeaves(): number {
    if (!this.employee._employmentDetails.leaves) {
      return 0;
    }

    const currentYear = new Date().getFullYear();

    // Calculer le nombre de jours de congés alloués selon l'ancienneté
    const hireDate = new Date(this.employee._employmentDetails.hireDate);
    const yearsOfService = currentYear - hireDate.getFullYear();
    let allocatedLeaves = 25; // Base de 25 jours

    // Ajuster selon l'ancienneté
    if (yearsOfService >= 10) {
      allocatedLeaves = 35;
    } else if (yearsOfService >= 5) {
      allocatedLeaves = 30;
    }

    // Calculer les congés utilisés pour l'année en cours
    const usedLeaves = this.employee._employmentDetails.leaves
      .filter(
        (leave: {
          startDate: string | number | Date;
          endDate: string | number | Date;
          status: string;
        }) => {
          const startDate = new Date(leave.startDate);
          const endDate = new Date(leave.endDate);

          // Vérifier si le congé est approuvé et dans l'année en cours
          return (
            leave.status === 'APPROVED' &&
            startDate.getFullYear() === currentYear &&
            endDate.getFullYear() === currentYear
          );
        }
      )
      .reduce(
        (
          total: number,
          leave: {
            startDate: string | number | Date;
            endDate: string | number | Date;
          }
        ) => {
          // Calculer la durée exacte du congé en jours
          const duration = this.calculateLeaveDuration(leave);

          // Vérifier si le congé est valide
          if (isNaN(duration) || duration < 0) {
            return total;
          }

          return total + duration;
        },
        0
      );

    // Calculer les congés restants
    const remainingLeaves = allocatedLeaves - usedLeaves;

    // S'assurer que le résultat n'est pas négatif
    return Math.max(0, remainingLeaves);
  }

  getSortedPayslips() {
    if (!this.employee._employmentDetails.payslips) return [];
    return [...this.employee._employmentDetails.payslips].sort((a, b) => {
      // Tri par année décroissante puis par mois décroissant
      if (a.year !== b.year) return b.year - a.year;
      return b.month - a.month;
    });
  }

  calculateLeaveDuration(leave: any): number {
    const start = new Date(leave.startDate);
    const end = new Date(leave.endDate);
    return (
      Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1
    );
  }

  getTimesheetStatusClass(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getLeaveStatusClass(status: string): string {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getActivityIcon(type: string): string {
    switch (type.toUpperCase()) {
      case 'TIMESHEETS':
        return '⏱️';
      case 'LEAVE':
        return '🏖️';
      case 'EVALUATIONS':
        return '📊';
      case 'TRAININGS':
        return '🎓';
      case 'SALARY':
        return '💰';
      case 'BENEFIT':
        return '🎁';
      default:
        return '📝';
    }
  }

  generatePassword(): void {
    // TODO: Adapter cette méthode au nouveau format de données
    // Dans le nouveau format, nous n'avons pas d'ID utilisateur directement accessible
    // Il faudra soit ajouter l'ID utilisateur au format de données, soit utiliser l'ID employé
    console.log(
      'Generate password functionality needs to be adapted to new data format'
    );

    // Ancienne implémentation commentée :
    // if (!this.employee?.user?.id) return;
    // this.employeeService
    //   .generateAndSendPassword(this.employee.user.id)
    //   .subscribe({
    //     next: (response) => {
    //       console.log('Password generated and sent:', response.message);
    //     },
    //     error: (error) => {
    //       console.error('Failed to generate password:', error);
    //     },
    //   });
  }
}
