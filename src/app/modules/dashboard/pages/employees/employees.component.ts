import { Component, OnInit } from '@angular/core';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { OverviewEmployeesTableComponent } from '../../components/overview/overview-employees-table/overview-employees-table.component';
import { ButtonComponent } from 'src/app/shared/components/button/button.component';
import { NgIf } from '@angular/common';
import { EmployeeCreateModalComponent } from './components/employee-create-modal/employee-create-modal.component';

@Component({
  selector: 'app-employees',
  templateUrl: './employees.component.html',
  standalone: true,
  imports: [OverviewEmployeesTableComponent],
})
export class EmployeesComponent implements OnInit {
  showCreateModal = false;

  constructor() {}

  ngOnInit(): void {}
}
