import { Component, OnInit } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';
import { DatePipe, Ng<PERSON>lass, <PERSON><PERSON>or, NgIf, TitleCasePipe } from '@angular/common';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { Employee, PaginatedResult } from 'src/app/core/models/employee.model';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { LeaveService } from 'src/app/core/services/leave/leave.service';
import { Leave } from 'src/app/core/models/leave.model';

@Component({
  selector: 'app-leave-list',
  templateUrl: './leave-list.component.html',
  standalone: true,
  imports: [
    Ng<PERSON><PERSON>,
    NgFor,
    NgIf,
    DatePipe,
    FormsModule,
    ReactiveFormsModule,
    TitleCasePipe,
  ],
})
export class LeaveListComponent implements OnInit {
  leaves: Leave[] = [];
  employees: any[] = [];
  selectedLeave: Leave | null = null;
  filterForm: FormGroup;
  showModal = false;
  lastUpdateMinutes = 0;
  isLoading = false;
  isEditing = false;
  company?: Company;
  currentCompanyId!: string;

  constructor(
    private leaveService: LeaveService,
    private employeeService: EmployeeService,
    private store: Store<AppState>,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      employeeId: [''],
      startDate: [''],
      endDate: [''],
      status: [''],
    });

    this.lastUpdateMinutes = Math.floor(Math.random() * 60);
    setInterval(() => this.lastUpdateMinutes++, 60000);
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
      this.currentCompanyId = company.id!;
      this.loadEmployees();
      this.loadAllLeaves();
    });
  }

  loadEmployees(): void {
    if (this.company?.id) {
      this.employeeService.getEmployees(this.company.id).subscribe({
        next: (data: PaginatedResult<Employee>) =>
          (this.employees = data.data as any),
        error: (error) => console.error('Error loading employees:', error),
      });
    }
  }

  loadAllLeaves(): void {
    this.isLoading = true;

    const employeeId = this.filterForm.get('employeeId')?.value;
    const companyId = this.company?.id;

    if (!companyId) {
      this.isLoading = false;
      return;
    }

    this.leaveService.getLeaves(companyId).subscribe({
      next: (data) => {
        this.leaves = this.filterLeaves(data);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading leaves:', error);
        this.isLoading = false;
      },
    });
  }

  filterLeaves(leaves: Leave[]): Leave[] {
    const startDate = this.filterForm.get('startDate')?.value;
    const endDate = this.filterForm.get('endDate')?.value;
    const status = this.filterForm.get('status')?.value;
    const employeeId = this.filterForm.get('employeeId')?.value;

    return leaves.filter((leave) => {
      const leaveStartDate = new Date(leave.startDate);
      const leaveEndDate = new Date(leave.endDate);
      const start = startDate ? new Date(startDate) : null;
      const end = endDate ? new Date(endDate) : null;

      const dateMatch =
        (!start || leaveStartDate >= start) && (!end || leaveEndDate <= end);
      const statusMatch = !status || leave.status === status;
      const employeeMatch = !employeeId || leave.employeeId === employeeId;

      return dateMatch && statusMatch && employeeMatch;
    });
  }

  selectLeave(leave: Leave): void {
    this.selectedLeave = leave;
  }

  applyFilters(): void {
    this.loadAllLeaves();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadAllLeaves();
  }

  openModal(isEditing: boolean): void {
    this.isEditing = isEditing;
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedLeave = null;
  }

  saveLeave(leaveData: any): void {
    const operation = this.selectedLeave
      ? this.leaveService.updateLeave(this.selectedLeave.id!, leaveData)
      : this.leaveService.createLeave(this.currentCompanyId, leaveData);

    operation.subscribe({
      next: (leave) => {
        if (this.selectedLeave) {
          this.leaves = this.leaves.map((l) => (l.id === leave.id ? leave : l));
        } else {
          this.leaves = [leave, ...this.leaves];
        }
        this.closeModal();
        this.lastUpdateMinutes = 0;
      },
      error: (error) => console.error('Error saving leave:', error),
    });
  }

  deleteLeave(): void {
    if (!this.selectedLeave) return;

    if (confirm(`Delete leave request (ID: ${this.selectedLeave.id})?`)) {
      this.leaveService.deleteLeave(this.selectedLeave.id!).subscribe({
        next: () => {
          this.leaves = this.leaves.filter(
            (l) => l.id !== this.selectedLeave?.id
          );
          this.selectedLeave = null;
          this.lastUpdateMinutes = 0;
        },
        error: (error) => console.error('Error deleting leave:', error),
      });
    }
  }

  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);

    return employee
      ? `${employee._personalInfo.firstName} ${employee._personalInfo.lastName}`
      : `Employee #${employeeId}`;
  }

  calculateDuration(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }
}
