import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';
import { CommonModule } from '@angular/common';
import { Leave } from 'src/app/core/models/leave.model';

@Component({
  selector: 'app-leave-create-modal',
  templateUrl: './leave-create-modal.component.html',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
})
export class LeaveCreateModalComponent implements OnInit {
  @Input() isEditingLeave = false;
  @Input() existingLeave?: Leave;
  @Input() employees: EmployeeData[] = [];
  @Input() employeeId?: string;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<any>();

  leaveForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.leaveForm = this.fb.group({
      employeeId: ['', Validators.required],
      type: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      reason: ['', Validators.required],
      status: ['PENDING'],
    });
  }

  ngOnInit(): void {
    if (this.employeeId) {
      this.leaveForm.patchValue({ employeeId: this.employeeId });
    }

    if (this.isEditingLeave && this.existingLeave) {
      this.leaveForm.patchValue(this.existingLeave);
    }
  }

  onSubmit(): void {
    if (this.leaveForm.valid) {
      this.save.emit(this.leaveForm.value);
    }
  }

  onClose(): void {
    this.close.emit();
  }

  getEmployeeName(employeeId: string): string {
    const employee = this.employees.find((emp) => emp.id === employeeId);
    return employee
      ? `${employee.user?.profile?.firstName} ${employee.user?.profile?.lastName}`
      : `Employee #${employeeId}`;
  }
}
