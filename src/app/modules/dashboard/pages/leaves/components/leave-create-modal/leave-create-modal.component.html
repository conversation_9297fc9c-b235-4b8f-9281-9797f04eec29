<div
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
>
  <div class="w-full max-w-2xl rounded-xl bg-white p-6 dark:bg-gray-800">
    <div class="mb-6 flex items-center justify-between">
      <h3 class="text-lg font-bold text-gray-800 dark:text-white">
        {{
          isEditingLeave ? "Modifier la demande" : "Nouvelle demande de congé"
        }}
      </h3>
      <button
        (click)="onClose()"
        class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()" class="space-y-4">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div class="flex flex-col" *ngIf="!employeeId">
          <label
            class="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Employé
          </label>
          <select
            formControlName="employeeId"
            class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="">Sélectionner un employé</option>
            <option *ngFor="let employee of employees" [value]="employee.id">
              {{ getEmployeeName(employee.id) }}
            </option>
          </select>
        </div>

        <div class="flex flex-col">
          <label
            class="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Type de congé
          </label>
          <select
            formControlName="type"
            class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="">Sélectionner un type</option>
            <option value="ANNUAL">Congé annuel</option>
            <option value="SICK">Congé maladie</option>
            <option value="MATERNITY">Congé maternité</option>
            <option value="PATERNITY">Congé paternité</option>
            <option value="UNPAID">Congé sans solde</option>
            <option value="OTHER">Autre</option>
          </select>
        </div>

        <div class="flex flex-col">
          <label
            class="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Date de début
          </label>
          <input
            type="date"
            formControlName="startDate"
            class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>

        <div class="flex flex-col">
          <label
            class="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Date de fin
          </label>
          <input
            type="date"
            formControlName="endDate"
            class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>
      </div>

      <div class="flex flex-col">
        <label
          class="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Motif
        </label>
        <textarea
          formControlName="reason"
          rows="3"
          class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          placeholder="Entrez le motif de votre demande..."
        ></textarea>
      </div>

      <div class="flex justify-end space-x-2">
        <button
          type="button"
          (click)="onClose()"
          class="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-600 transition hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600"
        >
          Annuler
        </button>
        <button
          type="submit"
          [disabled]="!leaveForm.valid"
          class="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 disabled:opacity-50"
        >
          {{ isEditingLeave ? "Modifier" : "Créer" }}
        </button>
      </div>
    </form>
  </div>
</div>
