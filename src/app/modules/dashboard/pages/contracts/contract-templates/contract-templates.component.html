<div class="p-6 space-y-6">
  <!-- Header -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
    <div>
      <div class="flex items-center">
        <button
          (click)="goBack()"
          class="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            📋 Templates de Contrats
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            G<PERSON><PERSON> vos modèles de contrats réutilisables
          </p>
        </div>
      </div>
    </div>
    
    <div class="flex flex-wrap gap-3">
      <button
        (click)="openAIGenerator()"
        class="inline-flex items-center px-4 py-2 border border-purple-300 dark:border-purple-600 text-sm font-medium rounded-md text-purple-700 dark:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
        disabled
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        Générer avec IA
        <span class="ml-2 text-xs bg-purple-200 dark:bg-purple-800 px-2 py-1 rounded">Soon</span>
      </button>
      
      <button
        (click)="createTemplate()"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Nouveau Template
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Recherche
        </label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="applyFilters()"
          placeholder="Rechercher un template..."
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Type de contrat
        </label>
        <select
          [(ngModel)]="selectedType"
          (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
        >
          <option value="">Tous les types</option>
          <option *ngFor="let type of contractTypes" [value]="type">{{ getTypeLabel(type) }}</option>
        </select>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Catégorie
        </label>
        <select
          [(ngModel)]="selectedCategory"
          (change)="applyFilters()"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
        >
          <option value="">Toutes les catégories</option>
          <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Grille des templates -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div
      *ngFor="let template of filteredTemplates"
      class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
    >
      <div class="p-6">
        <!-- Header du template -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {{ template.name }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {{ template.description }}
            </p>
          </div>
          <div class="flex items-center space-x-2 ml-4">
            <span
              *ngIf="template.isDefault"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
            >
              Défaut
            </span>
            <span
              *ngIf="template.aiGenerated"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
            >
              IA
            </span>
          </div>
        </div>

        <!-- Métadonnées -->
        <div class="space-y-3 mb-4">
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600 dark:text-gray-400">Type:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="getTypeClasses(template.contractType)">
              {{ getTypeLabel(template.contractType) }}
            </span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600 dark:text-gray-400">Catégorie:</span>
            <span class="text-gray-900 dark:text-white">{{ template.category }}</span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600 dark:text-gray-400">Variables:</span>
            <span class="text-gray-900 dark:text-white">{{ template.variables.length }}</span>
          </div>
          
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600 dark:text-gray-400">Utilisations:</span>
            <span class="text-gray-900 dark:text-white">{{ template.usageCount }}</span>
          </div>
        </div>

        <!-- Tags -->
        <div class="flex flex-wrap gap-1 mb-4" *ngIf="template.tags.length > 0">
          <span
            *ngFor="let tag of template.tags"
            class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
          >
            {{ tag }}
          </span>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-2">
            <button
              (click)="previewTemplate(template.id)"
              class="text-primary hover:text-primary/80 transition-colors text-sm font-medium"
            >
              Aperçu
            </button>
            <button
              (click)="useTemplate(template.id)"
              class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors text-sm font-medium"
            >
              Utiliser
            </button>
          </div>
          
          <div class="flex items-center space-x-1">
            <button
              (click)="editTemplate(template.id)"
              class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="Modifier"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </button>
            <button
              (click)="duplicateTemplate(template.id)"
              class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="Dupliquer"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </button>
            <button
              (click)="deleteTemplate(template.id)"
              class="p-2 text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors"
              title="Supprimer"
              [disabled]="template.isDefault"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- État vide -->
  <div *ngIf="filteredTemplates.length === 0" class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun template</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Commencez par créer votre premier template de contrat.
    </p>
    <div class="mt-6">
      <button
        (click)="createTemplate()"
        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Nouveau Template
      </button>
    </div>
  </div>
</div>

<!-- Modal de prévisualisation (à implémenter) -->
<div *ngIf="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
    <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Aperçu du Template
      </h3>
      <button
        (click)="closePreview()"
        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
      <div [innerHTML]="previewContent" class="prose dark:prose-invert max-w-none"></div>
    </div>
  </div>
</div>
