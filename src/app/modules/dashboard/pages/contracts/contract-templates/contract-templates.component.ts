import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ContractService } from '../../../../../core/services/contract/contract.service';
import {
  ContractTemplate,
  ContractType,
} from '../../../../../core/models/contract.model';

@Component({
  selector: 'app-contract-templates',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './contract-templates.component.html',
})
export class ContractTemplatesComponent implements OnInit, OnDestroy {
  templates: ContractTemplate[] = [];
  filteredTemplates: ContractTemplate[] = [];

  // Filtres
  searchTerm = '';
  selectedType = '';
  selectedCategory = '';

  // Données pour les filtres
  contractTypes = Object.values(ContractType);
  categories: string[] = [];

  // Modal de prévisualisation
  showPreviewModal = false;
  previewContent = '';

  private subscriptions = new Subscription();

  constructor(
    private contractService: ContractService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadTemplates();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadTemplates(): void {
    this.subscriptions.add(
      this.contractService.getTemplates().subscribe((templates: any) => {
        this.templates = templates as any;
        this.extractCategories();
        this.applyFilters();
      })
    );
  }

  private extractCategories(): void {
    const categorySet = new Set(this.templates.map((t) => t.category));
    this.categories = Array.from(categorySet).sort();
  }

  applyFilters(): void {
    this.filteredTemplates = this.templates.filter((template) => {
      const matchesSearch =
        !this.searchTerm ||
        template.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        template.description
          .toLowerCase()
          .includes(this.searchTerm.toLowerCase()) ||
        template.tags.some((tag) =>
          tag.toLowerCase().includes(this.searchTerm.toLowerCase())
        );

      const matchesType =
        !this.selectedType || template.contractType === this.selectedType;
      const matchesCategory =
        !this.selectedCategory || template.category === this.selectedCategory;

      return matchesSearch && matchesType && matchesCategory;
    });
  }

  // Navigation
  goBack(): void {
    this.router.navigate(['/dashboard/contracts']);
  }

  createTemplate(): void {
    // TODO: Implémenter la création de template
    console.log('Créer un nouveau template');
  }

  editTemplate(id: string): void {
    // TODO: Implémenter l'édition de template
    console.log('Éditer template:', id);
  }

  duplicateTemplate(id: string): void {
    const template = this.templates.find((t) => t.id === id);
    if (template) {
      const duplicatedTemplate = {
        ...template,
        name: `${template.name} (Copie)`,
        isDefault: false,
        usageCount: 0,
      };

      this.subscriptions.add(
        this.contractService
          .createTemplate(duplicatedTemplate)
          .subscribe(() => {
            this.loadTemplates();
          })
      );
    }
  }

  deleteTemplate(id: string): void {
    const template = this.templates.find((t) => t.id === id);
    if (template?.isDefault) {
      alert('Impossible de supprimer un template par défaut');
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer ce template ?')) {
      this.subscriptions.add(
        this.contractService.deleteTemplate(id).subscribe(() => {
          this.loadTemplates();
        })
      );
    }
  }

  previewTemplate(id: string): void {
    const template = this.templates.find((t) => t.id === id);
    if (template) {
      this.previewContent = template.content;
      this.showPreviewModal = true;
    }
  }

  closePreview(): void {
    this.showPreviewModal = false;
    this.previewContent = '';
  }

  useTemplate(id: string): void {
    this.router.navigate(['/dashboard/contracts/create'], {
      queryParams: { templateId: id },
    });
  }

  openAIGenerator(): void {
    // TODO: Implémenter le générateur IA
    alert('Fonctionnalité IA bientôt disponible !');
  }

  // Méthodes utilitaires
  getTypeLabel(type: ContractType): string {
    const labels: { [key in ContractType]: string } = {
      [ContractType.CDI]: 'CDI',
      [ContractType.CDD]: 'CDD',
      [ContractType.STAGE]: 'Stage',
      [ContractType.FREELANCE]: 'Freelance',
      [ContractType.APPRENTISSAGE]: 'Apprentissage',
      [ContractType.INTERIM]: 'Intérim',
      [ContractType.CONSULTANT]: 'Consultant',
    };
    return labels[type];
  }

  getTypeClasses(type: ContractType): string {
    const classes: { [key in ContractType]: string } = {
      [ContractType.CDI]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [ContractType.CDD]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [ContractType.STAGE]:
        'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
      [ContractType.FREELANCE]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      [ContractType.APPRENTISSAGE]:
        'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400',
      [ContractType.INTERIM]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [ContractType.CONSULTANT]:
        'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400',
    };
    return classes[type];
  }
}
