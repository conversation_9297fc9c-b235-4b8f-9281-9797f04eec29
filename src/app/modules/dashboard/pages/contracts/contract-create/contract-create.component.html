<div class="p-6 space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <button
        (click)="goBack()"
        class="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
      </button>
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          📄 {{ isEditing ? 'Modifier le Contrat' : 'Nouveau Contrat' }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          {{ isEditing ? 'Modifiez les informations du contrat' : 'Créez un nouveau contrat d\'employé' }}
        </p>
      </div>
    </div>
  </div>

  <!-- Formulaire -->
  <form [formGroup]="contractForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Section 1: Informations de base -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Informations de base
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Employé -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Employé *
          </label>
          <select
            formControlName="employeeId"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          >
            <option value="">Sélectionner un employé</option>
            <option *ngFor="let employee of employees" [value]="employee.id">
              {{ employee.user?.profile?.firstName }} {{ employee.user?.profile?.lastName }}
            </option>
          </select>
          <div *ngIf="contractForm.get('employeeId')?.invalid && contractForm.get('employeeId')?.touched" 
               class="mt-1 text-sm text-red-600 dark:text-red-400">
            L'employé est requis
          </div>
        </div>

        <!-- Template -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Template (optionnel)
          </label>
          <select
            formControlName="templateId"
            (change)="onTemplateChange()"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          >
            <option value="">Aucun template</option>
            <option *ngFor="let template of templates" [value]="template.id">
              {{ template.name }}
            </option>
          </select>
        </div>

        <!-- Type de contrat -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Type de contrat *
          </label>
          <select
            formControlName="contractType"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          >
            <option value="">Sélectionner un type</option>
            <option *ngFor="let type of contractTypes" [value]="type">
              {{ getTypeLabel(type) }}
            </option>
          </select>
          <div *ngIf="contractForm.get('contractType')?.invalid && contractForm.get('contractType')?.touched" 
               class="mt-1 text-sm text-red-600 dark:text-red-400">
            Le type de contrat est requis
          </div>
        </div>

        <!-- Titre du contrat -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Titre du contrat *
          </label>
          <input
            type="text"
            formControlName="title"
            placeholder="Ex: Contrat Développeur Senior"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
          <div *ngIf="contractForm.get('title')?.invalid && contractForm.get('title')?.touched" 
               class="mt-1 text-sm text-red-600 dark:text-red-400">
            Le titre est requis
          </div>
        </div>
      </div>
    </div>

    <!-- Section 2: Dates et conditions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Dates et conditions
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Date de début -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date de début *
          </label>
          <input
            type="date"
            formControlName="startDate"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
          <div *ngIf="contractForm.get('startDate')?.invalid && contractForm.get('startDate')?.touched" 
               class="mt-1 text-sm text-red-600 dark:text-red-400">
            La date de début est requise
          </div>
        </div>

        <!-- Date de fin -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date de fin (optionnelle)
          </label>
          <input
            type="date"
            formControlName="endDate"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
        </div>

        <!-- Salaire -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Salaire annuel brut *
          </label>
          <div class="relative">
            <input
              type="number"
              formControlName="salary"
              placeholder="45000"
              class="w-full px-3 py-2 pr-12 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
            />
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span class="text-gray-500 dark:text-gray-400 sm:text-sm">€</span>
            </div>
          </div>
          <div *ngIf="contractForm.get('salary')?.invalid && contractForm.get('salary')?.touched" 
               class="mt-1 text-sm text-red-600 dark:text-red-400">
            Le salaire est requis
          </div>
        </div>

        <!-- Heures de travail -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Heures de travail par semaine *
          </label>
          <input
            type="number"
            formControlName="workingHours"
            placeholder="35"
            min="1"
            max="60"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
          <div *ngIf="contractForm.get('workingHours')?.invalid && contractForm.get('workingHours')?.touched" 
               class="mt-1 text-sm text-red-600 dark:text-red-400">
            Les heures de travail sont requises
          </div>
        </div>
      </div>
    </div>

    <!-- Section 3: Variables du template -->
    <div *ngIf="templateVariables.length > 0" 
         class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Variables du template
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div *ngFor="let variable of templateVariables; let i = index">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ variable.label }}
            <span *ngIf="variable.required" class="text-red-500">*</span>
          </label>
          
          <!-- Input selon le type -->
          <input
            *ngIf="variable.type === 'TEXT' || variable.type === 'EMAIL' || variable.type === 'PHONE'"
            [type]="getInputType(variable.type)"
            [(ngModel)]="variableValues[variable.key]"
            [ngModelOptions]="{standalone: true}"
            [placeholder]="variable.defaultValue || ''"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
          
          <input
            *ngIf="variable.type === 'NUMBER' || variable.type === 'CURRENCY'"
            type="number"
            [(ngModel)]="variableValues[variable.key]"
            [ngModelOptions]="{standalone: true}"
            [placeholder]="variable.defaultValue || ''"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
          
          <input
            *ngIf="variable.type === 'DATE'"
            type="date"
            [(ngModel)]="variableValues[variable.key]"
            [ngModelOptions]="{standalone: true}"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          />
          
          <select
            *ngIf="variable.type === 'SELECT'"
            [(ngModel)]="variableValues[variable.key]"
            [ngModelOptions]="{standalone: true}"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          >
            <option value="">Sélectionner...</option>
            <option *ngFor="let option of variable.options" [value]="option">{{ option }}</option>
          </select>
          
          <div class="flex items-center" *ngIf="variable.type === 'BOOLEAN'">
            <input
              type="checkbox"
              [(ngModel)]="variableValues[variable.key]"
              [ngModelOptions]="{standalone: true}"
              class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              {{ variable.description || variable.label }}
            </label>
          </div>
          
          <p *ngIf="variable.description" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {{ variable.description }}
          </p>
        </div>
      </div>
    </div>

    <!-- Section 4: Notes -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Notes additionnelles
      </h3>
      
      <textarea
        formControlName="notes"
        rows="4"
        placeholder="Notes ou commentaires sur ce contrat..."
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
      ></textarea>
    </div>

    <!-- Actions -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
      <button
        type="button"
        (click)="goBack()"
        class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        Annuler
      </button>
      
      <div class="flex space-x-3">
        <button
          type="button"
          (click)="saveDraft()"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          Sauvegarder en brouillon
        </button>
        
        <button
          type="submit"
          [disabled]="contractForm.invalid || isSubmitting"
          class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span *ngIf="isSubmitting" class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Création...
          </span>
          <span *ngIf="!isSubmitting">
            {{ isEditing ? 'Mettre à jour' : 'Créer le contrat' }}
          </span>
        </button>
      </div>
    </div>
  </form>
</div>
