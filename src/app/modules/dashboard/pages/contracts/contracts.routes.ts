import { Routes } from '@angular/router';

export const CONTRACTS_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./contract-list/contract-list.component').then(m => m.ContractListComponent),
    title: 'Gestion des Contrats - LuminaHR'
  },
  {
    path: 'templates',
    loadComponent: () => import('./contract-templates/contract-templates.component').then(m => m.ContractTemplatesComponent),
    title: 'Templates de Contrats - LuminaHR'
  },
  {
    path: 'create',
    loadComponent: () => import('./contract-create/contract-create.component').then(m => m.ContractCreateComponent),
    title: 'Nouveau Contrat - LuminaHR'
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./contract-edit/contract-edit.component').then(m => m.ContractEditComponent),
    title: 'Modifier Contrat - LuminaHR'
  },
  {
    path: 'view/:id',
    loadComponent: () => import('./contract-view/contract-view.component').then(m => m.ContractViewComponent),
    title: 'Détails Contrat - LuminaHR'
  }
];
