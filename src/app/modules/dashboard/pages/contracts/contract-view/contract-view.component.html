<div class="p-6 space-y-6" *ngIf="contract">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <button
        (click)="goBack()"
        class="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
      </button>
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          📄 {{ contract.title }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Contrat {{ getTypeLabel(contract.contractType) }} - {{ getEmployeeName() }}
        </p>
      </div>
    </div>
    
    <div class="flex items-center space-x-3">
      <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
            [ngClass]="getStatusClasses(contract.status)">
        {{ getStatusLabel(contract.status) }}
      </span>
      
      <div class="flex space-x-2">
        <button
          (click)="editContract()"
          class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Modifier
        </button>
        
        <button
          (click)="downloadPDF()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Télécharger PDF
        </button>
      </div>
    </div>
  </div>

  <!-- Informations principales -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Détails du contrat -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Informations de base -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Informations du contrat
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Type de contrat</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ getTypeLabel(contract.contractType) }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Statut</label>
            <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="getStatusClasses(contract.status)">
              {{ getStatusLabel(contract.status) }}
            </span>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Date de début</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ contract.startDate | date:'dd/MM/yyyy' }}</p>
          </div>
          
          <div *ngIf="contract.endDate">
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Date de fin</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ contract.endDate | date:'dd/MM/yyyy' }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Salaire annuel brut</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ contract.salary | currency:'EUR':'symbol':'1.0-0' }}</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Heures de travail</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ contract.workingHours }}h/semaine</p>
          </div>
        </div>
      </div>

      <!-- Variables du contrat -->
      <div *ngIf="contract.variables.length > 0" 
           class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Variables du contrat
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div *ngFor="let variable of contract.variables">
            <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">{{ variable.label }}</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
              <span *ngIf="variable.type !== 'BOOLEAN'">{{ variable.value || 'Non défini' }}</span>
              <span *ngIf="variable.type === 'BOOLEAN'">{{ variable.value ? 'Oui' : 'Non' }}</span>
            </p>
          </div>
        </div>
      </div>

      <!-- Contenu du contrat -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Contenu du contrat
        </h3>
        
        <div class="prose dark:prose-invert max-w-none" [innerHTML]="getProcessedContent()">
        </div>
      </div>

      <!-- Notes -->
      <div *ngIf="contract.notes" 
           class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Notes
        </h3>
        <p class="text-gray-700 dark:text-gray-300">{{ contract.notes }}</p>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Informations employé -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Employé
        </h3>
        
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <span class="text-lg font-medium text-primary">
              {{ getEmployeeInitials() }}
            </span>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ getEmployeeName() }}
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ contract.employee?.user?.email }}
            </p>
          </div>
        </div>
        
        <button
          (click)="viewEmployeeProfile()"
          class="w-full text-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          Voir le profil
        </button>
      </div>

      <!-- Signatures -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Signatures
        </h3>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Employé</span>
            <span class="inline-flex items-center">
              <svg *ngIf="contract.signedByEmployee" class="w-5 h-5 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <svg *ngIf="!contract.signedByEmployee" class="w-5 h-5 text-gray-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm">{{ contract.signedByEmployee ? 'Signé' : 'En attente' }}</span>
            </span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Employeur</span>
            <span class="inline-flex items-center">
              <svg *ngIf="contract.signedByEmployer" class="w-5 h-5 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <svg *ngIf="!contract.signedByEmployer" class="w-5 h-5 text-gray-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm">{{ contract.signedByEmployer ? 'Signé' : 'En attente' }}</span>
            </span>
          </div>
          
          <div *ngIf="contract.signedDate" class="pt-2 border-t border-gray-200 dark:border-gray-700">
            <span class="text-xs text-gray-500 dark:text-gray-400">
              Signé le {{ contract.signedDate | date:'dd/MM/yyyy à HH:mm' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Métadonnées -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Métadonnées
        </h3>
        
        <div class="space-y-3 text-sm">
          <div>
            <span class="text-gray-600 dark:text-gray-400">Version:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ contract.version }}</span>
          </div>
          
          <div>
            <span class="text-gray-600 dark:text-gray-400">Créé le:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ contract.createdAt | date:'dd/MM/yyyy' }}</span>
          </div>
          
          <div>
            <span class="text-gray-600 dark:text-gray-400">Modifié le:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ contract.updatedAt | date:'dd/MM/yyyy' }}</span>
          </div>
          
          <div *ngIf="contract.template">
            <span class="text-gray-600 dark:text-gray-400">Template:</span>
            <span class="ml-2 text-gray-900 dark:text-white">{{ contract.template.name }}</span>
          </div>
        </div>
      </div>

      <!-- Actions rapides -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Actions
        </h3>
        
        <div class="space-y-3">
          <button
            (click)="duplicateContract()"
            class="w-full text-left px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            Dupliquer
          </button>
          
          <button
            (click)="sendForSignature()"
            [disabled]="contract.status === 'SIGNED'"
            class="w-full text-left px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Envoyer pour signature
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- État de chargement -->
<div *ngIf="!contract" class="flex items-center justify-center h-64">
  <div class="text-center">
    <svg class="animate-spin h-8 w-8 text-primary mx-auto mb-4" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    <p class="text-gray-600 dark:text-gray-400">Chargement du contrat...</p>
  </div>
</div>
