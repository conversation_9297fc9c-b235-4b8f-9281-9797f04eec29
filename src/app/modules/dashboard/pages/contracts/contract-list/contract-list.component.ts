import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ContractService } from '../../../../../core/services/contract/contract.service';
import {
  Contract,
  ContractStats,
  ContractType,
  ContractStatus,
  ContractFilter,
} from '../../../../../core/models/contract.model';

@Component({
  selector: 'app-contract-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './contract-list.component.html',
})
export class ContractListComponent implements OnInit, OnDestroy {
  contracts: Contract[] = [];
  filteredContracts: Contract[] = [];
  stats: ContractStats | null = null;

  // Filtres
  searchTerm = '';
  selectedType = '';
  selectedStatus = '';

  // Énumérations pour les templates
  contractTypes = Object.values(ContractType);
  contractStatuses = Object.values(ContractStatus);

  private subscriptions = new Subscription();

  constructor(
    private contractService: ContractService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadContracts();
    this.loadStats();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private loadContracts(): void {
    this.subscriptions.add(
      this.contractService.getContracts().subscribe((contracts) => {
        this.contracts = contracts as any;
        this.applyFilters();
      })
    );
  }

  private loadStats(): void {
    this.subscriptions.add(
      this.contractService.getContractStats().subscribe((stats) => {
        this.stats = stats;
      })
    );
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  private applyFilters(): void {
    const filter: ContractFilter = {
      search: this.searchTerm || undefined,
      contractType: (this.selectedType as ContractType) || undefined,
      status: (this.selectedStatus as ContractStatus) || undefined,
    };

    this.subscriptions.add(
      this.contractService.getContracts(filter).subscribe((contracts) => {
        this.filteredContracts = contracts as any;
      })
    );
  }

  resetFilters(): void {
    this.searchTerm = '';
    this.selectedType = '';
    this.selectedStatus = '';
    this.applyFilters();
  }

  // Navigation
  navigateToTemplates(): void {
    this.router.navigate(['/dashboard/contracts/templates']);
  }

  createContract(): void {
    this.router.navigate(['/dashboard/contracts/create']);
  }

  viewContract(id: string): void {
    this.router.navigate(['/dashboard/contracts/view', id]);
  }

  editContract(id: string): void {
    this.router.navigate(['/dashboard/contracts/edit', id]);
  }

  duplicateContract(id: string): void {
    this.subscriptions.add(
      this.contractService.duplicateContract(id).subscribe(() => {
        this.loadContracts();
        this.loadStats();
      })
    );
  }

  deleteContract(id: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce contrat ?')) {
      this.subscriptions.add(
        this.contractService.deleteContract(id).subscribe(() => {
          this.loadContracts();
          this.loadStats();
        })
      );
    }
  }

  // Méthodes utilitaires pour l'affichage
  getEmployeeName(contract: Contract): string {
    if (contract.employee?.user?.profile) {
      const profile = contract.employee.user.profile;
      return `${profile.firstName} ${profile.lastName}`;
    }
    return `Employé ${contract.employeeId}`;
  }

  getEmployeeInitials(contract: Contract): string {
    if (contract.employee?.user?.profile) {
      const profile = contract.employee.user.profile;
      return `${profile.firstName?.[0] || ''}${
        profile.lastName?.[0] || ''
      }`.toUpperCase();
    }
    return 'EMP';
  }

  getTypeLabel(type: ContractType): string {
    const labels: { [key in ContractType]: string } = {
      [ContractType.CDI]: 'CDI',
      [ContractType.CDD]: 'CDD',
      [ContractType.STAGE]: 'Stage',
      [ContractType.FREELANCE]: 'Freelance',
      [ContractType.APPRENTISSAGE]: 'Apprentissage',
      [ContractType.INTERIM]: 'Intérim',
      [ContractType.CONSULTANT]: 'Consultant',
    };
    return labels[type];
  }

  getStatusLabel(status: ContractStatus): string {
    const labels: { [key in ContractStatus]: string } = {
      [ContractStatus.DRAFT]: 'Brouillon',
      [ContractStatus.PENDING_SIGNATURE]: 'En attente de signature',
      [ContractStatus.SIGNED]: 'Signé',
      [ContractStatus.ACTIVE]: 'Actif',
      [ContractStatus.EXPIRED]: 'Expiré',
      [ContractStatus.TERMINATED]: 'Résilié',
      [ContractStatus.CANCELLED]: 'Annulé',
    };
    return labels[status];
  }

  getTypeClasses(type: ContractType): string {
    const classes: { [key in ContractType]: string } = {
      [ContractType.CDI]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [ContractType.CDD]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [ContractType.STAGE]:
        'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
      [ContractType.FREELANCE]:
        'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
      [ContractType.APPRENTISSAGE]:
        'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400',
      [ContractType.INTERIM]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [ContractType.CONSULTANT]:
        'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400',
    };
    return classes[type];
  }

  getStatusClasses(status: ContractStatus): string {
    const classes: { [key in ContractStatus]: string } = {
      [ContractStatus.DRAFT]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
      [ContractStatus.PENDING_SIGNATURE]:
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
      [ContractStatus.SIGNED]:
        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
      [ContractStatus.ACTIVE]:
        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
      [ContractStatus.EXPIRED]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [ContractStatus.TERMINATED]:
        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
      [ContractStatus.CANCELLED]:
        'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
    };
    return classes[status];
  }
}
