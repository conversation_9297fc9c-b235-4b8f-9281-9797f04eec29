<div class="flex flex-col p-6">
  <!-- Titre -->
  <h3 class="mb-4 text-lg font-bold text-gray-800 dark:text-white">
    {{ data.title }}
  </h3>

  <!-- Message -->
  <p class="mb-6 text-gray-600 dark:text-gray-400">
    {{ data.message }}
  </p>

  <!-- Actions -->
  <div class="flex justify-end gap-3">
    <button
      mat-button
      (click)="onCancel()"
      class="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
    >
      {{ data.cancelText || "Annuler" }}
    </button>
    <button
      mat-button
      (click)="onConfirm()"
      class="rounded-lg bg-red-50 px-4 py-2 text-sm font-medium text-red-600 transition hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"
    >
      {{ data.confirmText || "Confirmer" }}
    </button>
  </div>
</div>
