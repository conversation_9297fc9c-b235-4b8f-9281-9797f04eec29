<td class="py-3 px-3 text-left flex items-center space-x-2">
  <div *ngIf="leave.employee.user.profile?.avatar; else defaultAvatar">
    <img
      [src]="leave.employee.user.profile?.avatar"
      alt="Avatar"
      class="w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
    />
  </div>
  <ng-template #defaultAvatar>
    <div
      class="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 text-gray-600"
    >
      <span className="text-sm font-medium">
        {{ (leave.employee.user?.profile?.firstName)![0] | uppercase }}
      </span>
    </div>
  </ng-template>
  <span class="px-4 py-3 font-medium text-gray-800 dark:text-white"
    >{{ leave.employee.user.profile?.firstName }}
    {{ leave.employee.user.profile?.lastName }}</span
  >
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ leave.leaveType }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ getLeaveDays(leave.startDate, leave.endDate) }} jours
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">{{ leave.status }}</td>
