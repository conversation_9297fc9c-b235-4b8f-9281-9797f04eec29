import { NgIf, UpperCasePipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { Leave } from 'src/app/core/models/leave.model';

@Component({
    selector: '[overview-leave-request-table-item]',
    templateUrl: './overview-leave-request-table-item.component.html',
    imports: [AngularSvgIconModule,NgIf,UpperCasePipe]
})
export class OverviewLeaveRequestTableItemComponent implements OnInit {
  @Input() leave = <Leave>{};

  constructor() {}

  ngOnInit(): void {}

  getLeaveDays(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)); 
  }
  
}
