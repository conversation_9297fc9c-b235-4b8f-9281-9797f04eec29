import { Component, Input, OnInit } from '@angular/core';
import { EmployeeData, EmployeeStat } from '../../../models/employee';
import { NgStyle, CurrencyPipe, CommonModule } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
    selector: 'app-employee-card',
    templateUrl: './employee-data-card.component.html',
    styleUrls: ['./employee-data-card.component.scss'],
    imports: [NgStyle,  AngularSvgIconModule, CommonModule]
})
export class EmployeeDataCardComponent implements OnInit {
  @Input() employeeData!: EmployeeStat;

  constructor() {}

  ngOnInit(): void {}

  getGradientColor(color: string): string {
    const colors: { [key: string]: string } = {
      purple: '#6b46c1',
      orange: '#ed8936',
      green: '#48bb78',
      blue: '#4299e1',
      red: '#f56565',
    };
    return colors[color] || '#6b46c1'; // Défaut : purple
  }

  getBorderColor(color: string): string {
    const borders: { [key: string]: string } = {
      purple: '#e9d8fd',
      orange: '#fbd38d',
      green: '#c6f6d5',
      blue: '#bee3f8',
      red: '#fed7d7',
    };
    return borders[color] || '#e9d8fd'; // Défaut : purple
  }
}
