<div class="flex items-center gap-4 rounded-lg p-4 shadow-sm bg-background">
  <!-- Icon avec design simplifié -->
  <svg-icon
    [src]="employeeData.icon"
    [svgClass]="
      'h-10 w-10 rounded-lg p-2.5 ' +
      (employeeData.color === 'purple'
        ? 'bg-purple-50 text-purple-500 dark:bg-purple-900/20 dark:text-purple-300'
        : employeeData.color === 'red'
        ? 'bg-red-50 text-red-500 dark:bg-red-900/20 dark:text-red-300'
        : employeeData.color === 'green'
        ? 'bg-green-50 text-green-500 dark:bg-green-900/20 dark:text-green-300'
        : employeeData.color === 'blue'
        ? 'bg-blue-50 text-blue-500 dark:bg-blue-900/20 dark:text-blue-300'
        : employeeData.color === 'orange'
        ? 'bg-orange-50 text-orange-500 dark:bg-orange-900/20 dark:text-orange-300'
        : '')
    "
  >
  </svg-icon>

  <div class="flex flex-1 items-center justify-between">
    <!-- Info section -->
    <div>
      <!-- Titre avec espacement amélioré -->
      <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400">
        {{ employeeData.title }}
      </h3>

      <!-- Valeur principale avec typographie améliorée -->
      <p class="text-2xl font-medium text-gray-800 dark:text-white">
        {{ employeeData.value }}
      </p>

      <!-- Informations de changement redessinées -->
      <div class="flex items-center gap-1 text-xs">
        <svg-icon
          [src]="
            employeeData.change == 'augmentation'
              ? 'assets/icons/heroicons/outline/arrow-sm-up.svg'
              : 'assets/icons/heroicons/outline/arrow-sm-down.svg'
          "
          [svgClass]="
            'h-3 w-3 ' +
            (employeeData.change == 'augmentation'
              ? 'text-green-500 dark:text-green-400'
              : 'text-red-500 dark:text-red-400')
          "
        >
        </svg-icon>
        <span
          [class]="
            employeeData.change == 'augmentation'
              ? 'text-green-500 dark:text-green-400 font-medium'
              : 'text-red-500 dark:text-red-400 font-medium'
          "
        >
          {{ employeeData.percentage }}%
        </span>
        <span class="text-gray-500 dark:text-gray-400">{{
          employeeData.change
        }}</span>
      </div>
    </div>

    <!-- Cercle de progression redessiné et aligné -->
    <div class="relative ml-2">
      <!-- Cercle externe avec design épuré -->
      <div
        class="h-12 w-12 rounded-full"
        [ngStyle]="{
          background:
            'conic-gradient(' +
            getGradientColor(employeeData.color) +
            ' 0% ' +
            employeeData.progress +
            '%, #f3f4f6 ' +
            employeeData.progress +
            '% 100%)'
        }"
      ></div>

      <!-- Cercle interne modernisé -->
      <div
        class="absolute inset-0 m-1 flex h-10 w-10 items-center justify-center rounded-full bg-white dark:bg-gray-900"
      >
        <span class="text-xs font-medium text-gray-700 dark:text-gray-300"
          >{{ employeeData.progress }}%</span
        >
      </div>
    </div>
  </div>
</div>
