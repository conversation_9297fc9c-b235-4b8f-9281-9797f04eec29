<td class="px-4 py-3 text-gray-800 text-xs dark:text-gray-200">
  {{ employee.id | slice : 0 : 13 }}
</td>
<td class="px-4 py-3">
  <div *ngIf="employee._personalInfo.avatar; else defaultAvatar">
    <img
      [src]="employee._personalInfo.avatar"
      alt="Avatar"
      class="w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
    />
  </div>
  <ng-template #defaultAvatar>
    <div
      class="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 text-gray-600"
    >
      <span className="text-sm font-medium">
        {{ employee._personalInfo.firstName?.charAt(0)
        }}{{ employee._personalInfo.lastName?.charAt(0) }}</span
      >
    </div>
  </ng-template>
</td>
<td class="px-4 py-3 font-medium text-gray-800 dark:text-white">
  {{ employee._personalInfo.firstName }} {{ employee._personalInfo.lastName }}
</td>
<td class="px-4 py-3 text-gray-600 text-xs dark:text-gray-300">
  {{ employee._employmentDetails?.department?.name }}
</td>
<td class="px-4 py-3 text-gray-600 text-xs dark:text-gray-300">
  {{ employee._employmentDetails?.position?.title }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ employee._personalInfo.email }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ employee._personalInfo.phoneNumber || "N/A" }}
</td>
<td class="px-4 py-3 text-gray-600 dark:text-gray-300">
  {{ employee._employmentDetails.hireDate | date : "shortDate" }}
</td>
