import { Component, Input, OnInit } from '@angular/core';
import { EmployeeNew } from 'src/app/core/models/employee.model';
import { DatePipe, NgIf, SlicePipe } from '@angular/common';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
  selector: '[overview-employees-table-item]',
  templateUrl: './overview-employees-table-item.component.html',
  imports: [AngularSvgIconModule, DatePipe, NgIf, SlicePipe],
})
export class OverviewEmployeesTableItemComponent implements OnInit {
  @Input() employee: EmployeeNew = {} as EmployeeNew;

  constructor() {}

  ngOnInit(): void {}
}
