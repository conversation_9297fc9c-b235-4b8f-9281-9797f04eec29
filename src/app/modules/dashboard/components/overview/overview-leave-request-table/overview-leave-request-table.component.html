<div class="flex flex-col rounded-xl px-6 py-6 bg-background">
  <div class="mb-6 flex items-center justify-between">
    <div class="flex items-center gap-4">
      <div class="flex flex-col">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">
          <PERSON><PERSON><PERSON> de con<PERSON>
        </h3>
        <span class="text-xs text-gray-500 dark:text-gray-400"
          >Mise à jour il y a 37 minutes</span
        >
      </div>
      <button
        (click)="loadLeaves()"
        class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
        title="Recharger les données"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      </button>
    </div>
    <button
      class="rounded-lg bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600 transition hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
    >
      Tout voir
    </button>
  </div>

  <div
    class="relative overflow-x-auto rounded-lg border border-gray-100 dark:border-gray-700"
  >
    <table class="w-full table-auto">
      <!-- Table head avec fond de couleur subtil -->
      <thead
        class="text-xs uppercase text-gray-700 bg-background dark:text-gray-300"
      >
        <tr>
          <th class="px-4 py-3 text-left">Employé</th>
          <th class="px-4 py-3 text-left">Type</th>
          <th class="px-4 py-3 text-left">Jours</th>
          <th class="px-4 py-3 text-left">Status</th>
        </tr>
      </thead>

      <tbody>
        <tr
          *ngFor="let leave of leaves; let odd = odd"
          [leave]="leave"
          overview-leave-request-table-item
          [ngClass]="odd ? 'bg-background' : 'bg-background'"
          class="border-b border-gray-100 transition hover:bg-blue-50 dark:border-gray-700 dark:hover:bg-gray-700/50"
        ></tr>
      </tbody>
      <!--end::Table body-->
    </table>
  </div>
</div>
