import { Component, OnInit } from '@angular/core';
import { NgClass, NgFor } from '@angular/common';
import { OverviewLeaveRequestTableItemComponent } from '../overview-leave-request-table-item/overview-leave-request-table-item.component';
import { Leave } from 'src/app/core/models/leave.model';
import { LeaveService } from 'src/app/core/services/leave/leave.service';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { PaginatedResult } from 'src/app/core/models/base.model';

@Component({
  selector: '[overview-leave-request-table]',
  templateUrl: './overview-leave-request-table.component.html',
  imports: [NgFor, OverviewLeaveRequestTableItemComponent, NgClass],
})
export class OverviewLeaveRequestTableComponent implements OnInit {
  leaves: Leave[] = [];
  company!: Company;
  constructor(
    private leaveService: LeaveService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });
    this.loadLeaves();
  }

  loadLeaves(): void {
    this.leaveService.getLeaves(this.company.id!).subscribe({
      next: (data: PaginatedResult<Leave>) => {
        this.leaves = data as any;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des demandes de congé', error);
      },
    });
  }
}
