<div class="flex flex-col rounded-xl bg-background px-6 py-6">
  <div class="mb-6 flex items-center justify-between">
    <div class="flex flex-col">
      <h3 class="text-lg font-bold text-gray-800 dark:text-white">
        Liste des employés
      </h3>
      <span class="text-xs text-gray-500 dark:text-gray-400"
        >Mise à jour il y a 37 minutes</span
      >
    </div>
    <div class="flex items-center space-x-2">
      <ng-container *ngIf="selectedEmployee">
        <button
          class="rounded-lg bg-yellow-50 px-4 py-2 text-sm font-medium text-yellow-600 transition hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50"
          (click)="editEmployee(selectedEmployee)"
        >
          Modifier
        </button>

        <button
          class="rounded-lg bg-red-50 px-4 py-2 text-sm font-medium text-red-600 transition hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50"
          (click)="deleteEmployee(selectedEmployee)"
        >
          Supprimer
        </button>
      </ng-container>
      <button
        class="rounded-lg bg-green-50 px-4 py-2 text-sm font-medium text-green-600 transition hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50"
        (click)="openCreateModal()"
      >
        Ajouter un employee
      </button>
    </div>
  </div>

  <div *ngIf="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"
    ></div>
  </div>
  <!-- Table avec bordure légère -->
  <div
    *ngIf="!isLoading"
    class="relative overflow-x-auto rounded-lg border border-gray-100 dark:border-gray-700"
  >
    <table class="w-full table-auto">
      <!-- Table head avec fond de couleur subtil -->
      <thead
        class="text-xs uppercase text-gray-700 bg-background dark:text-gray-300"
      >
        <tr>
          <th class="px-4 py-3 text-left">N°</th>
          <th class="px-4 py-3 text-left">Avatar</th>
          <th class="px-4 py-3 text-left">Nom de l'employé</th>
          <th class="px-4 py-3 text-left">Departement</th>
          <th class="px-4 py-3 text-left">Position</th>
          <th class="px-4 py-3 text-left">E-mail</th>
          <th class="px-4 py-3 text-left">Contact</th>
          <th class="px-4 py-3 text-left">Date d'embauche</th>
        </tr>
      </thead>
      <!-- Table body avec alternance de couleurs -->
      <tbody>
        <tr
          *ngFor="let employee of employees; let odd = odd"
          [employee]="employee"
          overview-employees-table-item
          (click)="selectEmployee(employee); $event.stopPropagation()"
          [ngClass]="{
            'bg-blue-100 dark:bg-blue-700 text-blue-900 dark:text-blue-200 font-semibold':
              selectedEmployee === employee,
            'bg-gray-50 dark:bg-gray-800/50':
              odd && selectedEmployee !== employee,
            'bg-background': !odd && selectedEmployee !== employee
          }"
          class="border-b cursor-pointer border-gray-100 transition hover:bg-blue-50 dark:border-gray-700 dark:hover:bg-gray-700/50"
        ></tr>
      </tbody>
    </table>
  </div>
</div>
<!-- Modal d'ajout d'employé -->
<div
  *ngIf="showCreateModal"
  class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
>
  <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md mx-4">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">Ajouter un nouvel employé</h2>
      <button
        class="text-gray-500 hover:text-gray-700"
        (click)="closeCreateModal()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <app-employee-create-modal
      (close)="closeCreateModal()"
      (employeeCreated)="addEmployee($event)"
    ></app-employee-create-modal>
  </div>
</div>
