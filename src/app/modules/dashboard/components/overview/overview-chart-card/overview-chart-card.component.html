<div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <!-- Graphique principal - Statistiques mensuelles -->
  <div
    class="lg:col-span-2 flex-col rounded-lg bg-background px-8 py-8 shadow-sm"
  >
    <div class="mb-4 flex items-center justify-between">
      <div>
        <h3 class="text-lg font-bold text-foreground">
          Statistiques des employés
        </h3>
        <span class="text-sm text-muted-foreground"
          >Aperçu mensuel des mouvements</span
        >
      </div>
      <div class="flex items-center space-x-2">
        <div class="h-3 w-3 rounded-full bg-green-500"></div>
        <span class="text-xs text-muted-foreground">Embauches</span>
        <div class="h-3 w-3 rounded-full bg-red-500 ml-3"></div>
        <span class="text-xs text-muted-foreground">Départs</span>
        <div class="h-3 w-3 rounded-full bg-orange-500 ml-3"></div>
        <span class="text-xs text-muted-foreground">Congés</span>
      </div>
    </div>
    <apx-chart
      [series]="chartOptions.series!"
      [chart]="chartOptions.chart!"
      [xaxis]="chartOptions.xaxis!"
      [yaxis]="chartOptions.yaxis!"
      [colors]="chartOptions.colors!"
      [tooltip]="chartOptions.tooltip!"
      [legend]="chartOptions.legend!"
      [plotOptions]="chartOptions.plotOptions!"
      [dataLabels]="chartOptions.dataLabels!"
      [grid]="chartOptions.grid!"
    >
    </apx-chart>
  </div>

  <!-- Turnover vs Recrutement -->
  <div
    class="- flex-col rounded-lg bg-background px-8 py-8 shadow-sm flex-col rounded-lg bg-background px-8 py-8 shadow-sm"
  >
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">Turnover</h3>
      <span class="text-sm text-muted-foreground">Embauches vs Départs</span>
    </div>
    <apx-chart
      [series]="turnoverChartOptions.series!"
      [chart]="turnoverChartOptions.chart!"
      [xaxis]="turnoverChartOptions.xaxis!"
      [colors]="turnoverChartOptions.colors!"
      [tooltip]="turnoverChartOptions.tooltip!"
      [legend]="turnoverChartOptions.legend!"
      [grid]="turnoverChartOptions.grid!"
    >
    </apx-chart>
  </div>

  <!-- Démographie par âge -->
  <div class="flex-col rounded-lg bg-background p-6 shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">Répartition par âge</h3>
      <span class="text-sm text-muted-foreground"
        >Tranches d'âge des employés</span
      >
    </div>
    <apx-chart
      [series]="demographicsChartOptions.series!"
      [chart]="demographicsChartOptions.chart!"
      [labels]="demographicsChartOptions.labels!"
      [colors]="demographicsChartOptions.colors!"
      [legend]="demographicsChartOptions.legend!"
      [tooltip]="demographicsChartOptions.tooltip!"
    >
    </apx-chart>
  </div>

  <!-- NOUVEAU: Répartition par sexe -->
  <div class="flex-col rounded-lg bg-background p-6 shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">Répartition par sexe</h3>
      <span class="text-sm text-muted-foreground"
        >Distribution hommes/femmes</span
      >
    </div>
    <apx-chart
      [series]="genderChartOptions.series!"
      [chart]="genderChartOptions.chart!"
      [labels]="genderChartOptions.labels!"
      [colors]="genderChartOptions.colors!"
      [legend]="genderChartOptions.legend!"
      [tooltip]="genderChartOptions.tooltip!"
      [plotOptions]="genderChartOptions.plotOptions!"
    >
    </apx-chart>
  </div>

  <!-- Compétences clés -->
  <div class="flex-col rounded-lg bg-background p-6 shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">Compétences clés</h3>
      <span class="text-sm text-muted-foreground">Top 6 des compétences</span>
    </div>
    <apx-chart
      [series]="skillsChartOptions.series!"
      [chart]="skillsChartOptions.chart!"
      [xaxis]="skillsChartOptions.xaxis!"
      [colors]="skillsChartOptions.colors!"
      [markers]="skillsChartOptions.markers!"
      [tooltip]="skillsChartOptions.tooltip!"
    >
    </apx-chart>
  </div>

  <!-- Satisfaction employés -->
  <div class="lg:col-span-2 flex-col rounded-lg bg-background p-6 shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">Satisfaction employés</h3>
      <span class="text-sm text-muted-foreground"
        >Évolution sur 5 ans (en %)</span
      >
    </div>
    <apx-chart
      [series]="satisfactionChartOptions.series!"
      [chart]="satisfactionChartOptions.chart!"
      [xaxis]="satisfactionChartOptions.xaxis!"
      [yaxis]="satisfactionChartOptions.yaxis!"
      [colors]="satisfactionChartOptions.colors!"
      [tooltip]="satisfactionChartOptions.tooltip!"
      [legend]="satisfactionChartOptions.legend!"
    >
    </apx-chart>
  </div>

  <div class="flex-col rounded-lg bg-background p-6 shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">Formation des employés</h3>
      <span class="text-sm text-muted-foreground">Heures mensuelles</span>
    </div>
    <apx-chart
      [series]="trainingChartOptions.series!"
      [chart]="trainingChartOptions.chart!"
      [xaxis]="trainingChartOptions.xaxis!"
      [colors]="trainingChartOptions.colors!"
      [tooltip]="trainingChartOptions.tooltip!"
      [fill]="trainingChartOptions.fill!"
    >
    </apx-chart>
  </div>
  <!-- Distribution des salaires -->
  <div class="lg:col-span-3 flex-col rounded-lg bg-background p-6 shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-bold text-foreground">
        Distribution des salaires
      </h3>
      <span class="text-sm text-muted-foreground"
        >Box plot par poste (min, Q1, médiane, Q3, max)</span
      >
    </div>
    <apx-chart
      [series]="salaryDistributionChartOptions.series!"
      [chart]="salaryDistributionChartOptions.chart!"
      [xaxis]="salaryDistributionChartOptions.xaxis!"
      [yaxis]="salaryDistributionChartOptions.yaxis!"
      [colors]="salaryDistributionChartOptions.colors!"
      [tooltip]="salaryDistributionChartOptions.tooltip!"
      [plotOptions]="salaryDistributionChartOptions.plotOptions!"
    >
    </apx-chart>
  </div>

  <!-- Formation des employés -->
</div>
