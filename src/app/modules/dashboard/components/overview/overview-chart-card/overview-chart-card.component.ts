import { Component, OnD<PERSON>roy, OnInit, effect } from '@angular/core';
import { Subscription } from 'rxjs';
import { ThemeService } from 'src/app/core/services/theme.service';
import { ChartOptions } from '../../../../../shared/models/chart-options';
import { NgApexchartsModule } from 'ng-apexcharts';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { CommonModule } from '@angular/common';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import {
  EmployeeResponse,
  PaginatedResult,
} from 'src/app/core/models/employee.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: '[overview-chart-card]',
  templateUrl: './overview-chart-card.component.html',
  imports: [AngularSvgIconModule, NgApexchartsModule, CommonModule],
  standalone: true,
})
export class OverviewChartCardComponent implements OnInit, OnDestroy {
  public chartOptions!: Partial<ChartOptions>;
  public turnoverChartOptions!: Partial<ChartOptions>;
  public demographicsChartOptions!: Partial<ChartOptions>;
  public genderChartOptions!: Partial<ChartOptions>;
  public skillsChartOptions!: Partial<ChartOptions>;
  public satisfactionChartOptions!: Partial<ChartOptions>;
  public salaryDistributionChartOptions!: Partial<ChartOptions>;
  public trainingChartOptions!: Partial<ChartOptions>;

  private subscription = new Subscription();
  private companyId!: string;
  private employees: EmployeeResponse[] = [];

  // Palettes de couleurs professionnelles
  private readonly colorPalettes = {
    corporate: {
      primary: '#2563EB', // Bleu professionnel
      secondary: '#7C3AED', // Violet moderne
      success: '#059669', // Vert émeraude
      warning: '#D97706', // Orange ambre
      danger: '#DC2626', // Rouge cardinal
      info: '#0891B2', // Cyan professionnel
      neutral: '#6B7280', // Gris ardoise
      accent: '#EC4899', // Rose moderne
    },
    sophisticated: {
      slate: '#1E293B', // Ardoise foncé
      blue: '#3B82F6', // Bleu moderne
      violet: '#8B5CF6', // Violet intense
      emerald: '#10B981', // Émeraude
      amber: '#F59E0B', // Ambre doré
      rose: '#EC4899', // Rose moderne
      cyan: '#06B6D4', // Cyan électrique
      orange: '#F97316', // Orange vif
    },
  };

  constructor(
    private store: Store<AppState>,
    private themeService: ThemeService,
    private employeeService: EmployeeService
  ) {
    this.initAllCharts();

    effect(() => {
      const theme = this.themeService.theme().mode;
      this.updateChartThemes(theme);
    });
  }

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      if (company?.id) {
        this.companyId = company.id;
        this.loadEmployeeData();
      }
    });
  }

  private loadEmployeeData(): void {
    this.subscription.add(
      this.employeeService
        .getEmployees(this.companyId, { pagination: { limit: 100, page: 1 } })
        .subscribe({
          next: (data: PaginatedResult<EmployeeResponse>) => {
            this.employees = data.data || [];
            this.updateAllCharts();
          },
          error: (error: any) => {
            console.error(
              'Erreur lors du chargement des données employés',
              error
            );
            this.setDummyData();
          },
        })
    );
  }

  private initAllCharts(): void {
    this.initMainChart();
    this.initTurnoverChart();
    this.initDemographicsChart();
    this.initGenderChart();
    this.initSkillsChart();
    this.initSatisfactionChart();
    this.initSalaryDistributionChart();
    this.initTrainingChart();
  }

  private initMainChart(): void {
    const categories = [
      'Jan',
      'Fév',
      'Mar',
      'Avr',
      'Mai',
      'Jun',
      'Jul',
      'Aoû',
      'Sep',
      'Oct',
      'Nov',
      'Déc',
    ];

    this.chartOptions = {
      series: [
        { name: 'Nouveaux employés', data: [] },
        { name: 'Employés démissionnaires', data: [] },
        { name: 'Employés en congé', data: [] },
      ],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      plotOptions: {
        bar: { horizontal: false, columnWidth: '65%', borderRadius: 4 },
      },
      dataLabels: { enabled: false },
      xaxis: {
        categories,
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      yaxis: { labels: { formatter: (val) => Math.round(val).toString() } },
      // NOUVELLES COULEURS SOPHISTIQUÉES
      colors: [
        this.colorPalettes.sophisticated.emerald, // Vert émeraude pour nouveaux
        this.colorPalettes.sophisticated.rose, // Rose pour démissions
        this.colorPalettes.sophisticated.amber, // Ambre pour congés
      ],
      tooltip: { theme: 'light', y: { formatter: (val) => `${val} employés` } },
      legend: { position: 'top', horizontalAlign: 'left', offsetX: 0 },
      grid: {
        borderColor: '#e0e0e0',
        strokeDashArray: 5,
        padding: { left: 0, right: 0 },
      },
    };
  }

  private initTurnoverChart(): void {
    this.turnoverChartOptions = {
      series: [
        { name: 'Embauches', type: 'column', data: [] },
        { name: 'Départs', type: 'line', data: [] },
      ],
      chart: {
        height: 350,
        type: 'line',
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      // COULEURS MODERNES
      colors: [
        this.colorPalettes.corporate.primary, // Bleu professionnel
        this.colorPalettes.corporate.danger, // Rouge cardinal
      ],
      xaxis: {
        categories: [
          'Jan',
          'Fév',
          'Mar',
          'Avr',
          'Mai',
          'Jun',
          'Jul',
          'Aoû',
          'Sep',
          'Oct',
          'Nov',
          'Déc',
        ],
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      tooltip: { theme: 'light', shared: true },
      legend: { position: 'top' },
      grid: { borderColor: '#e0e0e0', strokeDashArray: 5 },
    };
  }

  private initDemographicsChart(): void {
    this.demographicsChartOptions = {
      series: [],
      chart: {
        type: 'pie',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      labels: ['<25 ans', '25-35 ans', '36-45 ans', '>45 ans'],
      // PALETTE DÉGRADÉE BLEUE
      colors: ['#1E40AF', '#3B82F6', '#60A5FA', '#93C5FD'],
      legend: {
        position: 'bottom',
        formatter: function (seriesName, opts) {
          return seriesName + ': ' + opts.w.globals.series[opts.seriesIndex];
        },
      },
      tooltip: {
        theme: 'light',
        y: { formatter: (val) => `${val} employés` },
      },
    };
  }

  private initGenderChart(): void {
    this.genderChartOptions = {
      series: [],
      chart: {
        type: 'donut',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      labels: ['Hommes', 'Femmes', 'Non spécifié'],
      // COULEURS ÉLÉGANTES ET NEUTRES
      colors: [
        this.colorPalettes.sophisticated.slate, // Ardoise foncé
        this.colorPalettes.sophisticated.violet, // Violet moderne
        this.colorPalettes.corporate.neutral, // Gris neutre
      ],
      legend: {
        position: 'bottom',
        formatter: function (seriesName, opts) {
          const total = opts.w.globals.seriesTotals.reduce(
            (a: number, b: number) => a + b,
            0
          );
          const percentage = Math.round(
            (opts.w.globals.series[opts.seriesIndex] / total) * 100
          );
          return (
            seriesName +
            ': ' +
            opts.w.globals.series[opts.seriesIndex] +
            ' (' +
            percentage +
            '%)'
          );
        },
      },
      tooltip: {
        theme: 'light',
        y: { formatter: (val) => `${val} employés` },
      },
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              total: {
                show: true,
                label: 'Total',
                formatter: function (w) {
                  return w.globals.seriesTotals.reduce(
                    (a: number, b: number) => a + b,
                    0
                  );
                },
              },
            },
          },
        },
      },
    };
  }

  private initSkillsChart(): void {
    this.skillsChartOptions = {
      series: [{ name: "Nombre d'employés", data: [] }],
      chart: {
        type: 'radar',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      xaxis: { categories: [], labels: { show: true } },
      // COULEUR CYAN MODERNE
      colors: [this.colorPalettes.sophisticated.cyan],
      markers: { size: 4 },
      tooltip: { theme: 'light' },
      fill: {
        opacity: 0.1,
      },
      stroke: {
        width: 2,
      },
    };
  }

  private initSatisfactionChart(): void {
    this.satisfactionChartOptions = {
      series: [
        { name: 'Insatisfaits', data: [] },
        { name: 'Neutres', data: [] },
        { name: 'Satisfaits', data: [] },
      ],
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,
        stackType: '100%',
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      // COULEURS SÉMANTIQUES SOPHISTIQUÉES
      colors: [
        this.colorPalettes.corporate.danger, // Rouge pour insatisfaits
        this.colorPalettes.corporate.warning, // Orange pour neutres
        this.colorPalettes.corporate.success, // Vert pour satisfaits
      ],
      xaxis: {
        categories: ['2020', '2021', '2022', '2023', '2024'],
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      tooltip: { theme: 'light' },
      yaxis: { labels: { formatter: (val) => val + '%' } },
    };
  }

  private initSalaryDistributionChart(): void {
    this.salaryDistributionChartOptions = {
      series: [{ name: 'Distribution salariale', data: [] }],
      chart: {
        type: 'boxPlot',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      // COULEURS HARMONIEUSES POUR BOX PLOT
      colors: [this.colorPalettes.corporate.primary],
      tooltip: { theme: 'light', shared: false, intersect: true },
      plotOptions: {
        boxPlot: {
          colors: {
            upper: this.colorPalettes.corporate.primary, // Bleu principal
            lower: this.colorPalettes.corporate.secondary, // Violet secondaire
          },
        },
      },
      xaxis: {
        type: 'category',
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      yaxis: {
        labels: {
          formatter: (val) =>
            new Intl.NumberFormat('fr-FR', {
              style: 'currency',
              currency: 'USD',
            }).format(val),
        },
      },
    };
  }

  private initTrainingChart(): void {
    this.trainingChartOptions = {
      series: [{ name: 'Heures de formation', data: [] }],
      chart: {
        type: 'area',
        height: 350,
        fontFamily: 'inherit',
        toolbar: { show: false },
      },
      // COULEUR UNIQUE AVEC GRADIENT SOPHISTIQUÉ
      colors: [this.colorPalettes.sophisticated.violet],
      xaxis: {
        categories: [
          'Jan',
          'Fév',
          'Mar',
          'Avr',
          'Mai',
          'Jun',
          'Jul',
          'Aoû',
          'Sep',
          'Oct',
          'Nov',
          'Déc',
        ],
        axisBorder: { show: false },
        axisTicks: { show: false },
      },
      tooltip: { theme: 'light' },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 90, 100],
          colorStops: [
            {
              offset: 0,
              color: this.colorPalettes.sophisticated.violet,
              opacity: 0.8,
            },
            {
              offset: 100,
              color: this.colorPalettes.corporate.primary,
              opacity: 0.2,
            },
          ],
        },
      },
      stroke: {
        width: 2,
        curve: 'smooth',
      },
    };
  }

  private updateAllCharts(): void {
    if (!this.employees?.length) {
      this.setDummyData();
      return;
    }

    this.updateMainChart();
    this.updateTurnoverChart();
    this.updateDemographicsChart();
    this.updateGenderChart();
    this.updateSkillsChart();
    this.updateSatisfactionChart();
    this.updateSalaryDistributionChart();
    this.updateTrainingChart();
  }

  private updateMainChart(): void {
    const currentYear = new Date().getFullYear();
    const monthlyData = Array(12)
      .fill(0)
      .map((_, month) => {
        const newHires = this.employees.filter(
          (e) =>
            e._employmentDetails.hireDate &&
            new Date(e._employmentDetails.hireDate).getFullYear() ===
              currentYear &&
            new Date(e._employmentDetails.hireDate).getMonth() === month
        ).length;

        const resignations = this.employees.filter((e) =>
          e._employmentDetails.departures?.some(
            (d: any) =>
              d.departureDate &&
              new Date(d.departureDate).getFullYear() === currentYear &&
              new Date(d.departureDate).getMonth() === month
          )
        ).length;

        const onLeave = this.employees.filter((e) =>
          e._employmentDetails?.leaves?.some((l: any) => {
            if (!l.startDate || !l.endDate) return false;
            const startDate = new Date(l.startDate);
            const endDate = new Date(l.endDate);
            const currentMonth = new Date(currentYear, month, 1);
            return startDate <= currentMonth && endDate >= currentMonth;
          })
        ).length;

        return { newHires, resignations, onLeave };
      });

    this.chartOptions.series = [
      { name: 'Nouveaux employés', data: monthlyData.map((m) => m.newHires) },
      {
        name: 'Employés démissionnaires',
        data: monthlyData.map((m) => m.resignations),
      },
      { name: 'Employés en congé', data: monthlyData.map((m) => m.onLeave) },
    ];
  }

  private updateTurnoverChart(): void {
    const currentYear = new Date().getFullYear();
    const monthlyData = Array(12)
      .fill(0)
      .map((_, month) => ({
        hires: this.employees.filter((e) => e._employmentDetails.hireDate)
          .length,
        departures: this.employees.filter((e) =>
          e._employmentDetails.departures?.some((d: any) => d.departureDate)
        ).length,
      }));

    this.turnoverChartOptions.series = [
      {
        name: 'Embauches',
        type: 'column',
        data: monthlyData.map((m) => m.hires),
      },
      {
        name: 'Départs',
        type: 'line',
        data: monthlyData.map((m) => m.departures),
      },
    ];
  }

  private updateDemographicsChart(): void {
    const currentDate = new Date();
    const ageGroups = [
      { name: '<25 ans', filter: (age: number) => age < 25 },
      { name: '25-35 ans', filter: (age: number) => age >= 25 && age <= 35 },
      { name: '36-45 ans', filter: (age: number) => age >= 36 && age <= 45 },
      { name: '>45 ans', filter: (age: number) => age > 45 },
    ];

    const counts = ageGroups.map((group) => {
      return this.employees.filter((employee) => {
        if (!employee._personalInfo?.birthDate) return false;
        const birthDate = new Date(employee._personalInfo.birthDate);
        const age = currentDate.getFullYear() - birthDate.getFullYear();
        return group.filter(age);
      }).length;
    });

    this.demographicsChartOptions.series = counts;
  }

  private updateGenderChart(): void {
    const genderCounts = {
      male: 0,
      female: 0,
      unspecified: 0,
    };

    this.employees.forEach((employee) => {
      const gender = employee._personalInfo?.sex?.toLowerCase();
      if (gender === 'm') {
        genderCounts.male++;
      } else if (gender === 'f') {
        genderCounts.female++;
      } else {
        genderCounts.unspecified++;
      }
    });

    this.genderChartOptions.series = [
      genderCounts.male,
      genderCounts.female,
      genderCounts.unspecified,
    ];
  }

  private updateSkillsChart(): void {
    const skillCounts: Record<string, number> = {};

    this.employees.forEach((employee) => {
      const skills = employee._employmentDetails?.position?.requiredSkills;
      if (Array.isArray(skills)) {
        skills.forEach((skill: string) => {
          skillCounts[skill] = (skillCounts[skill] || 0) + 1;
        });
      }
    });

    const topSkills = Object.entries(skillCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 6);

    if (topSkills.length > 0) {
      this.skillsChartOptions.series = [
        { name: "Nombre d'employés", data: topSkills.map((skill) => skill[1]) },
      ];
      this.skillsChartOptions.xaxis = {
        categories: topSkills.map((skill) => skill[0]),
      };
    }
  }

  private updateSatisfactionChart(): void {
    const years = ['2020', '2021', '2022', '2023', '2024'];
    const satisfactionData = years.map((year) => {
      const evaluations = this.employees.flatMap(
        (e) =>
          e._employmentDetails?.performanceEvaluations?.filter(
            (pe: any) =>
              pe.periodEnd &&
              new Date(pe.periodEnd).getFullYear().toString() === year
          ) || []
      );

      const total = evaluations.length;
      if (total === 0) return { satisfied: 0, neutral: 0, unsatisfied: 0 };

      const satisfied = evaluations.filter(
        (pe: any) =>
          pe.managerEvaluation?.includes('Excellent') ||
          pe.managerEvaluation?.includes('Good')
      ).length;

      const unsatisfied = evaluations.filter(
        (pe: any) =>
          pe.managerEvaluation?.includes('Poor') ||
          pe.managerEvaluation?.includes('Unsatisfactory')
      ).length;

      const neutral = total - satisfied - unsatisfied;

      return {
        satisfied: Math.round((satisfied / total) * 100),
        neutral: Math.round((neutral / total) * 100),
        unsatisfied: Math.round((unsatisfied / total) * 100),
      };
    });

    this.satisfactionChartOptions.series = [
      {
        name: 'Insatisfaits',
        data: satisfactionData.map((d) => d.unsatisfied),
      },
      { name: 'Neutres', data: satisfactionData.map((d) => d.neutral) },
      { name: 'Satisfaits', data: satisfactionData.map((d) => d.satisfied) },
    ];
  }

  private updateSalaryDistributionChart(): void {
    const positionGroups: Record<string, number[]> = {};

    this.employees.forEach((employee) => {
      const position = employee._employmentDetails?.position?.title;
      const salaries = employee._employmentDetails?.salaries;

      if (position && salaries?.length > 0) {
        const latestSalary = salaries.reduce((prev: any, current: any) =>
          new Date(prev.effectiveDate) > new Date(current.effectiveDate)
            ? prev
            : current
        );

        const totalSalary =
          (latestSalary.baseSalary || 0) +
          (latestSalary.housingAllowance || 0) +
          (latestSalary.transportAllowance || 0) +
          (latestSalary.bonus || 0);

        if (!positionGroups[position]) {
          positionGroups[position] = [];
        }
        positionGroups[position].push(totalSalary);
      }
    });

    const seriesData = Object.entries(positionGroups)
      .filter(([_, salaries]) => salaries.length >= 1)
      .map(([position, salaries]) => {
        salaries.sort((a, b) => a - b);
        const q1 = salaries[Math.floor(salaries.length * 0.25)];
        const median = salaries[Math.floor(salaries.length * 0.5)];
        const q3 = salaries[Math.floor(salaries.length * 0.75)];

        return {
          x: position,
          y: [salaries[0], q1, median, q3, salaries[salaries.length - 1]],
        };
      });

    this.salaryDistributionChartOptions.series = [
      { name: 'Distribution salariale', data: seriesData },
    ];
  }

  private updateTrainingChart(): void {
    const currentYear = new Date().getFullYear();
    const monthlyHours = Array(12)
      .fill(0)
      .map((_, month) => {
        return this.employees.reduce((total, employee) => {
          const trainings =
            employee._employmentDetails?.trainings?.filter(
              (t: any) =>
                t.startDate &&
                new Date(t.startDate).getFullYear() === currentYear &&
                new Date(t.startDate).getMonth() === month
            ) || [];

          const monthlyHours = trainings.reduce(
            (sum: number, training: any) => {
              if (training.endDate && training.startDate) {
                const hours =
                  (new Date(training.endDate).getTime() -
                    new Date(training.startDate).getTime()) /
                  (1000 * 60 * 60);
                return sum + Math.max(0, hours);
              }
              return sum;
            },
            0
          );

          return total + monthlyHours;
        }, 0);
      });

    this.trainingChartOptions.series = [
      { name: 'Heures de formation', data: monthlyHours },
    ];
  }

  private setDummyData(): void {
    // Données factices avec les nouvelles couleurs appliquées
    this.chartOptions.series = [
      {
        name: 'Nouveaux employés',
        data: [12, 15, 10, 18, 20, 15, 22, 25, 20, 18, 15, 10],
      },
      {
        name: 'Employés démissionnaires',
        data: [5, 3, 8, 6, 7, 4, 6, 5, 4, 6, 8, 7],
      },
      { name: 'Employés en congé', data: [3, 4, 5, 6, 4, 7, 5, 6, 8, 7, 6, 5] },
    ];

    this.turnoverChartOptions.series = [
      {
        name: 'Embauches',
        type: 'column',
        data: [12, 15, 8, 10, 5, 7, 9, 11, 13, 10, 8, 6],
      },
      {
        name: 'Départs',
        type: 'line',
        data: [5, 8, 3, 6, 2, 4, 7, 5, 6, 4, 5, 7],
      },
    ];

    this.demographicsChartOptions.series = [25, 45, 20, 15];
    this.genderChartOptions.series = [55, 40, 5];

    this.skillsChartOptions.series = [
      { name: "Nombre d'employés", data: [80, 50, 30, 60, 75, 40] },
    ];
    this.skillsChartOptions.xaxis = {
      categories: [
        'Développement',
        'Gestion',
        'Communication',
        'Leadership',
        'Créativité',
        'Analyse',
      ],
    };

    this.satisfactionChartOptions.series = [
      { name: 'Insatisfaits', data: [10, 5, 8, 12, 7] },
      { name: 'Neutres', data: [20, 15, 12, 18, 25] },
      { name: 'Satisfaits', data: [70, 80, 80, 70, 68] },
    ];

    this.salaryDistributionChartOptions.series = [
      {
        name: 'Distribution salariale',
        data: [
          { x: 'Développeurs', y: [40000, 50000, 60000, 70000, 90000] },
          { x: 'Managers', y: [50000, 60000, 75000, 85000, 100000] },
          { x: 'RH', y: [35000, 45000, 55000, 65000, 75000] },
        ],
      },
    ];

    this.trainingChartOptions.series = [
      {
        name: 'Heures de formation',
        data: [120, 90, 150, 200, 180, 210, 170, 190, 220, 240, 210, 230],
      },
    ];
  }

  private updateChartThemes(theme: string): void {
    const isDark = theme === 'dark';
    const borderColor = isDark ? '#2d3748' : '#e0e0e0';
    const tooltipTheme = isDark ? 'dark' : 'light';

    const charts = [
      this.chartOptions,
      this.turnoverChartOptions,
      this.demographicsChartOptions,
      this.genderChartOptions,
      this.skillsChartOptions,
      this.satisfactionChartOptions,
      this.salaryDistributionChartOptions,
      this.trainingChartOptions,
    ];

    charts.forEach((chart) => {
      if (chart) {
        chart.tooltip = { ...chart.tooltip, theme: tooltipTheme };
        if (chart.grid) {
          chart.grid = {
            ...chart.grid,
            borderColor,
            strokeDashArray: 5,
            padding: { left: 0, right: 0 },
          };
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
