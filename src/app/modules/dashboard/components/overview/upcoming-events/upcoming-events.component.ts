import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';

interface CalendarDay {
  date: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  fullDate: Date;
}

@Component({
  selector: 'app-upcoming-events',
  templateUrl: './upcoming-events.component.html',
  imports: [CommonModule, DatePipe],
  providers: [DatePipe],
})
export class UpcomingEventsComponent implements OnInit {
  events = [
    {
      date: '15',
      month: 'Juin',
      title: "Rejoignez-nous pour célébrer l'anniversaire...",
      time: '10h00',
      category: 'Anniversaire',
    },
    {
      date: '22',
      month: 'Juin',
      title: "Bureau fermé à l'occasion du Jour...",
      time: '',
      category: 'Vacances',
    },
    {
      date: '10',
      month: 'Juillet',
      title: 'Événement familial avec jeux, res...',
      time: '09h00 - 18h00',
      category: '<PERSON><PERSON><PERSON>nique',
    },
    {
      date: '18',
      month: 'Ju<PERSON><PERSON>',
      title: 'Fête nationale - Dolore <PERSON>',
      time: '',
      category: 'Vacances',
    },
    {
      date: '5',
      month: 'Août',
      title: 'Anniversaire du chiot de John - M...',
      time: '09h00',
      category: 'Anniversaire',
    },
    {
      date: '20',
      month: 'Août',
      title: 'Amet sed no dolor kasd - Et Dolor...',
      time: '16h00',
      category: 'Annonce',
    },
  ];

  // Propriétés du calendrier
  currentDate = new Date();
  selectedDate: Date | null = null;
  calendarDays: CalendarDay[] = [];
  selectedDayEvents: any[] = [];

  weekDays = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];
  months = [
    'Janvier',
    'Février',
    'Mars',
    'Avril',
    'Mai',
    'Juin',
    'Juillet',
    'Août',
    'Septembre',
    'Octobre',
    'Novembre',
    'Décembre',
  ];

  // Événements du calendrier (simulés)
  calendarEvents: { [key: string]: any[] } = {
    '2025-07-26': [
      { title: 'Réunion équipe', time: '14:30' },
      { title: 'Présentation client', time: '16:00' },
    ],
    '2025-07-28': [{ title: 'Formation Angular', time: '10:00' }],
    '2025-07-30': [{ title: 'Rendez-vous médecin', time: '15:00' }],
    '2025-08-05': [{ title: 'Anniversaire du chiot de John', time: '09:00' }],
  };

  constructor(private datePipe: DatePipe) {}

  ngOnInit() {
    this.generateCalendar();
    this.selectedDate = new Date();
    this.updateSelectedDayEvents();
  }

  get currentMonthYear(): string {
    return `${
      this.months[this.currentDate.getMonth()]
    } ${this.currentDate.getFullYear()}`;
  }

  get todayDate(): string {
    const today = new Date();
    return `Aujourd'hui: ${today.getDate()} ${this.months[today.getMonth()]}`;
  }

  generateCalendar() {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    this.calendarDays = [];
    const today = new Date();

    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      this.calendarDays.push({
        date: date.getDate(),
        isCurrentMonth: date.getMonth() === month,
        isToday: this.isSameDay(date, today),
        isSelected: this.selectedDate
          ? this.isSameDay(date, this.selectedDate)
          : false,
        fullDate: new Date(date),
      });
    }
  }

  previousMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.generateCalendar();
  }

  nextMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    this.generateCalendar();
  }

  selectDate(day: CalendarDay) {
    if (day.isCurrentMonth) {
      this.selectedDate = new Date(day.fullDate);
      this.generateCalendar();
      this.updateSelectedDayEvents();
    }
  }

  getDayClasses(day: CalendarDay): string {
    let classes = '';

    if (!day.isCurrentMonth) {
      classes += 'text-gray-300 cursor-not-allowed ';
    } else if (day.isToday) {
      classes += 'bg-indigo-600 text-white shadow-lg ';
    } else if (day.isSelected) {
      classes += 'bg-indigo-500 text-white ';
    } else {
      classes += 'text-gray-700 hover:bg-indigo-100 ';
    }

    return classes;
  }

  hasEvent(day: CalendarDay): boolean {
    const dateKey = this.formatDateKey(day.fullDate);
    return !!this.calendarEvents[dateKey];
  }

  updateSelectedDayEvents() {
    if (this.selectedDate) {
      const dateKey = this.formatDateKey(this.selectedDate);
      this.selectedDayEvents = this.calendarEvents[dateKey] || [];
    }
  }

  trackByDay(index: number, day: CalendarDay): string {
    return `${day.fullDate.getTime()}-${day.isSelected}`;
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  }

  private formatDateKey(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
