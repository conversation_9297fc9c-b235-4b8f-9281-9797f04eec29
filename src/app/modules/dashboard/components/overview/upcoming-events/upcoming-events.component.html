<div class="flex gap-6">
  <!-- Section Événements à gauche -->
  <div class="flex-1 rounded-lg bg-background p-4 shadow-md">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold text-foreground">Événements à venir</h2>
      <a href="#" class="text-sm text-blue-500 hover:underline">Tout voir</a>
    </div>
    <ul class="space-y-6">
      <li *ngFor="let event of events">
        <div class="flex items-start">
          <div class="flex flex-col items-center">
            <span class="text-sm font-medium text-gray-500">{{
              event.date
            }}</span>
            <span class="text-xs text-gray-400">{{ event.month }}</span>
          </div>
          <div class="ml-4">
            <h3
              class="w-48 truncate text-sm font-semibold text-muted-foreground"
            >
              {{ event.title }}
            </h3>
            <div class="mt-1 flex items-center space-x-2">
              <span class="text-xs text-muted-foreground">{{
                event.time
              }}</span>
              <span
                class="rounded-lg px-2 py-0.5 text-xs"
                [ngClass]="{
                  'bg-red-100 text-red-600': event.category === 'Anniversaire',
                  'bg-blue-100 text-blue-600': event.category === 'Vacances',
                  'bg-green-100 text-green-600':
                    event.category === 'Pique-nique',
                  'bg-purple-100 text-purple-600': event.category === 'Annonce'
                }"
              >
                {{ event.category }}
              </span>
            </div>
            <div class="mt-1 text-xs text-gray-500">...</div>
          </div>
        </div>
      </li>
    </ul>
  </div>

  <!-- Calendrier à droite -->
  <div class="w-80 rounded-lg bg-background p-6 shadow-md">
    <!-- En-tête du calendrier -->
    <div class="mb-6 flex items-center justify-between">
      <button
        (click)="previousMonth()"
        class="rounded-full p-2 text-foreground hover:bg-muted transition-colors"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          ></path>
        </svg>
      </button>

      <div class="text-center">
        <h3 class="text-lg font-bold text-foreground">
          {{ currentMonthYear }}
        </h3>
        <p class="text-sm text-muted-foreground">{{ todayDate }}</p>
      </div>

      <button
        (click)="nextMonth()"
        class="rounded-full p-2 text-foreground hover:bg-muted transition-colors"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5l7 7-7 7"
          ></path>
        </svg>
      </button>
    </div>

    <!-- Jours de la semaine -->
    <div class="mb-2 grid grid-cols-7 gap-1">
      <div
        *ngFor="let day of weekDays"
        class="p-2 text-center text-xs font-semibold text-muted-foreground uppercase tracking-wide"
      >
        {{ day }}
      </div>
    </div>

    <!-- Grille du calendrier -->
    <div class="grid grid-cols-7 gap-1">
      <button
        *ngFor="let day of calendarDays; trackBy: trackByDay"
        (click)="selectDate(day)"
        [class]="getDayClasses(day)"
        class="relative h-10 w-10 rounded-lg text-sm font-medium transition-all duration-200 ease-in-out hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        {{ day.date }}

        <!-- Indicateur d'événement -->
        <div
          *ngIf="hasEvent(day)"
          class="absolute bottom-1 right-1 h-2 w-2 rounded-full bg-red-500"
        ></div>
      </button>
    </div>

    <!-- Événements du jour sélectionné -->
    <div
      *ngIf="selectedDayEvents.length > 0"
      class="mt-6 rounded-lg bg-muted p-4 shadow-sm"
    >
      <h4 class="mb-3 text-sm font-semibold text-foreground">
        Événements du {{ selectedDate | date : "d MMMM" : "fr" }}
      </h4>
      <div class="space-y-2">
        <div
          *ngFor="let event of selectedDayEvents"
          class="flex items-start space-x-3 rounded-md bg-background p-2 border"
        >
          <div class="flex-shrink-0">
            <div class="h-2 w-2 rounded-full bg-blue-500 mt-2"></div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-xs font-medium text-foreground truncate">
              {{ event.title }}
            </p>
            <p class="text-xs text-muted-foreground" *ngIf="event.time">
              {{ event.time }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
