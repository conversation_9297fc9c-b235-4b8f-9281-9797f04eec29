<div class="pt-4" *ngFor="let menu of menuService.pagesMenu">
  <div class="mx-1 mb-2 flex items-center justify-between">
    <small [ngClass]="{ hidden: !menuService.showSideBar }" class="text-xs font-semibold text-muted-foreground/50">
      {{ menu.group }}
    </small>
  </div>
  <ul class="flex flex-col space-y-1">
    <!-- List items -->
    <li *ngFor="let item of menu.items">
      <!-- Menu List -->
      <div (click)="toggleMenu(item)" class="group relative text-muted-foreground">
        <!-- Icon -->
        <div
          [ngClass]="item.active && !menuService.showSideBar ? 'text-primary' : 'text-muted-foreground/50'"
          class="pointer-events-none absolute m-2">
          <svg-icon src="{{ item.icon }}" [svgClass]="'h-5 w-5'"> </svg-icon>
        </div>

        <!-- Condition -->
        <ng-container
          [ngTemplateOutlet]="item.children ? childMenu : parentMenu"
          [ngTemplateOutletContext]="{ item: item }">
        </ng-container>

        <!-- Workaround:: Enable routerLink -->
        <ng-template #parentMenu let-item="item">
          <div
            routerLink="{{ item.route }}"
            class="flex h-9 cursor-pointer items-center justify-start rounded text-muted-foreground hover:bg-card hover:text-foreground">
            <a routerLinkActive="text-primary" class="ml-10 truncate text-xs tracking-wide focus:outline-none">
              {{ item.label }}
            </a>
          </div>
        </ng-template>

        <!-- Workaround:: Disable routerLink -->
        <ng-template #childMenu let-item="item">
          <div class="flex h-9 cursor-pointer items-center justify-start rounded hover:bg-card">
            <a
              class="ml-10 truncate text-xs tracking-wide text-muted-foreground focus:outline-none group-hover:text-foreground">
              {{ item.label }}
            </a>
          </div>
        </ng-template>

        <!-- Arrow Icon -->
        <button
          *ngIf="item.children"
          [ngClass]="{ hidden: !menuService.showSideBar, 'rotate-90': item.expanded }"
          class="pointer-events-none absolute top-1 right-0 flex items-center p-1 text-muted-foreground/50 transition-all duration-500">
          <svg-icon src="assets/icons/heroicons/solid/chevron-right.svg" [svgClass]="'h-5 w-5'"> </svg-icon>
        </button>

        <!-- Tooltip -->
        <div class="fixed w-full" *ngIf="!menuService.showSideBar">
          <span
            class="z-1 absolute left-14 -top-[34px] w-auto min-w-max origin-left scale-0 rounded-md bg-foreground p-2 text-xs font-bold text-background shadow-md transition-all duration-200 group-hover:scale-100">
            {{ item.label }}
          </span>
        </div>
      </div>

      <!-- Submenu items -->
      <app-sidebar-submenu [submenu]="item"></app-sidebar-submenu>
    </li>
  </ul>

  <div class="pt-3" *ngIf="menu.separator">
    <hr class="border-dashed border-border" />
  </div>
</div>
