<nav
  [ngClass]="menuService.showSideBar ? 'w-52 xl:w-64' : 'w-[70px]'"
  class="scrollbar-thumb-rounded scrollbar-track-rounded hidden h-full flex-col justify-between overflow-auto bg-background pt-3 transition-all duration-300 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-card lg:flex"
>
  <div class="px-4">
    <!-- Logo -->
    <div class="relative h-10">
      <div class="flex items-center" *ngIf="menuService.showSideBar">
        <div class="dropdown relative inline-block">
          <a
            class="flex items-center cursor-pointer justify-center p-2 focus:outline-none focus:ring-1"
            href="/dashboard/overview"
          >
            <!-- Logo de l'entreprise ou initiales de son nom officiel -->
            <ng-container *ngIf="company?.logo; else initials">
              <img
                [src]="company.logo"
                alt="Company Logo"
                class="h-8 w-8 rounded-full"
              />
            </ng-container>
            <ng-template #initials>
              <div
                class="h-8 w-8 flex items-center justify-center rounded-full bg-primary text-white"
              >
                {{ company.officialName | slice : 0 : 2 }}
                <!-- Affiche les 2 premières lettres -->
              </div>
            </ng-template>

            <!-- Nom de l'entreprise -->
            <div class="ml-3 flex-1 min-w-0">
              <h2 class="text-sm font-semibold text-foreground truncate">
                {{ company?.officialName || "Mon Entreprise" }}
              </h2>
            </div>
          </a>
        </div>
      </div>
      <button
        (click)="toggleSidebar()"
        data-tour="sidebar-toggle"
        class="absolute top-2 right-2 flex h-5 w-5 items-center justify-center rounded text-muted-foreground/50 transition-all duration-200 focus:outline-none hover:text-muted-foreground"
        [ngClass]="{ 'rotate-180': !menuService.showSideBar }"
      >
        <svg-icon src="assets/icons/heroicons/solid/chevron-double-left.svg">
        </svg-icon>
      </button>
    </div>

    <!-- Separator -->
    <div class="pt-3">
      <hr class="border-dashed border-muted" />
    </div>

    <!-- Menu Items -->
    <app-sidebar-menu data-tour="main-menu"></app-sidebar-menu>
  </div>

  <div class="mx-4 my-4 space-y-3">
    <!-- Carte Upgrade Plan -->
    <div
      data-tour="upgrade-plan"
      class="relative rounded-xl bg-gradient-to-br from-primary/15 via-primary/10 to-primary/5 border border-primary/30 p-5 transition-all duration-300 hover:shadow-lg hover:shadow-primary/20 hover:border-primary/40 group overflow-hidden"
      *ngIf="menuService.showSideBar"
    >
      <!-- Decoration background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      ></div>

      <!-- Floating particles effect -->
      <div
        class="absolute top-2 right-2 w-2 h-2 bg-primary/30 rounded-full animate-pulse"
      ></div>
      <div
        class="absolute top-4 right-6 w-1 h-1 bg-primary/40 rounded-full animate-pulse delay-300"
      ></div>

      <div class="relative z-10">
        <!-- Header with icon and badge -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center">
            <div
              class="flex items-center justify-center w-8 h-8 bg-primary/20 rounded-lg group-hover:bg-primary/30 transition-colors duration-200"
            >
              <svg-icon
                src="assets/icons/heroicons/outline/sparkles.svg"
                [svgClass]="'h-4 w-4 text-primary'"
              >
              </svg-icon>
            </div>
            <h3 class="ml-3 text-sm font-bold text-primary">Upgrade Plan</h3>
          </div>
          <span
            class="text-xs bg-primary/20 text-primary px-2 py-1 rounded-full font-medium"
            >Pro</span
          >
        </div>

        <!-- Description -->
        <p class="text-xs text-muted-foreground mb-4 leading-relaxed">
          Accédez aux fonctionnalités avancées et augmentez votre productivité
        </p>

        <!-- Features list -->
        <div class="space-y-1 mb-4">
          <div class="flex items-center text-xs text-muted-foreground">
            <div class="w-1 h-1 bg-primary rounded-full mr-2"></div>
            <span>Analyses illimitées</span>
          </div>
          <div class="flex items-center text-xs text-muted-foreground">
            <div class="w-1 h-1 bg-primary rounded-full mr-2"></div>
            <span>Support prioritaire</span>
          </div>
        </div>

        <!-- CTA Button -->
        <button
          class="w-full bg-primary text-primary-foreground text-xs font-semibold py-2.5 px-4 rounded-lg hover:bg-primary/90 active:scale-[0.98] transition-all duration-200 shadow-sm hover:shadow-md"
        >
          <span class="flex items-center justify-center">
            Passer au Pro
            <svg
              class="w-3 h-3 ml-1 transition-transform duration-200 group-hover:translate-x-0.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </span>
        </button>
      </div>
    </div>

    <!-- Version -->
    <a
      target="_blank"
      href=""
      class="group flex h-9 cursor-pointer items-center justify-start rounded p-2 hover:bg-card"
    >
      <svg-icon
        src="assets/icons/heroicons/outline/information-circle.svg"
        [svgClass]="'h-5 w-5 text-muted-foreground/50'"
      >
      </svg-icon>

      <div
        class="ml-3 truncate text-[10px] font-semibold tracking-wide focus:outline-none"
        *ngIf="menuService.showSideBar"
      >
        <span class="rounded-lg bg-primary/10 px-2 font-semibold text-primary"
          >v{{ appJson.version }}</span
        >
      </div>

      <div class="fixed w-full" *ngIf="!menuService.showSideBar">
        <span
          class="z-1 absolute left-12 -top-4 w-auto min-w-max origin-left scale-0 rounded-md bg-foreground p-2 text-xs font-bold text-background shadow-md transition-all duration-200 group-hover:scale-100"
        >
          v{{ appJson.version }}
        </span>
      </div>
    </a>
  </div>
</nav>
