import { Component, OnInit } from '@angular/core';
import { ThemeService } from 'src/app/core/services/theme.service';
import packageJson from '../../../../../../package.json';
import { MenuService } from '../../services/menu.service';
import { RouterLink } from '@angular/router';
import { SidebarMenuComponent } from './sidebar-menu/sidebar-menu.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { NgClass, NgIf, SlicePipe } from '@angular/common';
import { Theme } from 'src/app/core/models/theme.model';
import { AppState } from 'src/app/core/store/app.state';
import { select, Store } from '@ngrx/store';
import { Company } from 'src/app/core/models/company.model';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { NavbarSubmenuComponent } from '../navbar/navbar-submenu/navbar-submenu.component';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  standalone: true,
  imports: [
    NgClass,
    NgIf,
    AngularSvgIconModule,
    SidebarMenuComponent,
    SlicePipe,
  ],
})
export class SidebarComponent implements OnInit {
  public appJson: any = packageJson;
  company!: Company;

  menu = [
    {
      icon: 'assets/icons/heroicons/outline/users.svg',
      label: 'Users',
      route: '/settings',
    },
    {
      icon: 'assets/icons/heroicons/outline/cog.svg',
      label: 'Settings',
      route: '/settings',
    },
    {
      icon: 'assets/icons/heroicons/outline/bell.svg',
      label: 'Notifications',
      route: '/gift',
    },
  ];
  constructor(
    public menuService: MenuService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });
  }

  public toggleSidebar() {
    this.menuService.toggleSidebar();
  }
}
