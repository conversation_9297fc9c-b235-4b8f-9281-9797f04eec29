import { Component, OnInit } from '@angular/core';
import { MenuService } from '../../../services/menu.service';
import { NavbarMobileMenuComponent } from './navbar-mobile-menu/navbar-mobile-menu.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { NgClass, NgIf, SlicePipe } from '@angular/common';
import { Company } from 'src/app/core/models/company.model';
import { AppState } from 'src/app/core/store/app.state';
import { select, Store } from '@ngrx/store';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';

@Component({
  selector: 'app-navbar-mobile',
  templateUrl: './navbar-mobile.component.html',
  styleUrls: ['./navbar-mobile.component.scss'],
  standalone: true,
  imports: [
    NgClass,
    AngularSvgIconModule,
    SlicePipe,
    NgIf,
    NavbarMobileMenuComponent,
  ],
})
export class NavbarMobileComponent implements OnInit {
  company!: Company;
  constructor(
    public menuService: MenuService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });
  }

  public toggleMobileMenu(): void {
    this.menuService.showMobileMenu = false;
  }
}
