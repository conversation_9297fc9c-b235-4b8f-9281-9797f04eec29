<!--
	Mobile menu, show/hide based on mobile menu state.
	Entering: "duration-200 ease-out"
		From: "opacity-0 scale-95"
		To: "opacity-100 scale-100"
	Leaving: "duration-100 ease-in"
		From: "opacity-100 scale-100"
		To: "opacity-0 scale-95"
-->
<div
  [ngClass]="
    menuService.showMobileMenu
      ? 'pointer-events-auto scale-100 animate-fade-in-up opacity-100 duration-200'
      : 'pointer-events-none scale-95 opacity-0 duration-100 ease-out'
  "
  class="absolute inset-x-0 top-0 z-10 origin-top-right transform p-2 transition md:hidden">
  <div class="rounded-lg bg-background shadow-lg">
    <div class="pt-5 pb-6">
      <div class="flex items-center justify-between px-5">
        <div>
          <!-- Logo -->
          <div
          class="flex items-center justify-start sm:order-2 md:mr-10 lg:hidden"
        >
          <a
            class="flex items-center justify-center rounded p-2 focus:outline-none focus:ring-1"
          >
            <!-- Logo de l'entreprise ou initiales de son nom officiel -->
            <ng-container *ngIf="company?.logo; else initials">
              <img
                [src]="company.logo"
                alt="Company Logo"
                class="h-8 w-8 rounded-full"
              />
            </ng-container>
            <ng-template #initials>
              <div
                class="h-8 w-8 flex items-center justify-center rounded-full bg-primary text-white"
              >
                {{ company.officialName | slice : 0 : 2 }}
                <!-- Affiche les 2 premières lettres -->
              </div>
            </ng-template>
          </a>
          <b class="hidden pl-3 text-sm font-bold text-foreground sm:block">{{
            company.officialName
          }}</b>
        </div>
        <div class="-mr-2">
          <button
            (click)="toggleMobileMenu()"
            type="button"
            class="inline-flex items-center justify-center rounded-md p-2 text-muted-foreground transition-transform focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary hover:rotate-90 hover:bg-card hover:text-foreground">
            <span class="sr-only">Close menu</span>
            <!-- Heroicon name: outline/x -->
            <svg-icon src="assets/icons/heroicons/outline/x.svg"> </svg-icon>
          </button>
        </div>
      </div>
      <div
        class="scrollbar-thumb-rounded scrollbar-track-rounded max-h-[500px] overflow-y-auto px-5 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted">
        <app-navbar-mobile-menu></app-navbar-mobile-menu>
      </div>
    </div>
  </div>
</div>
