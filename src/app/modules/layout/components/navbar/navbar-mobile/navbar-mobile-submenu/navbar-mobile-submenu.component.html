<div
  class="max-h-0 overflow-hidden pt-1 pl-4 transition-all duration-500"
  [ngClass]="{ 'max-h-screen': submenu.expanded }">
  <ul class="flex flex-col border-l border-dashed border-border pl-2 text-muted-foreground">
    <li *ngFor="let sub of submenu.children">
      <div class="flex rounded hover:bg-card hover:text-foreground" (click)="toggleMenu(sub)">
        <!-- Condition -->
        <ng-container
          [ngTemplateOutlet]="sub.children ? childMenu : parentMenu"
          [ngTemplateOutletContext]="{ sub: sub }">
        </ng-container>

        <!-- Parent Menu -->
        <ng-template #parentMenu let-sub="sub">
          <a
            (click)="closeMobileMenu()"
            [routerLink]="sub.route"
            routerLinkActive="text-primary"
            [routerLinkActiveOptions]="{ exact: true }"
            class="inline-block w-full px-4 py-2 text-xs font-semibold">
            {{ sub.label }}
          </a>
        </ng-template>

        <!-- Child Menu -->
        <ng-template #childMenu let-sub="sub">
          <a class="inline-block w-full cursor-pointer px-4 py-2 text-xs font-semibold">
            {{ sub.label }}
          </a>
          <button
            [ngClass]="{ 'rotate-90': sub.expanded }"
            class="flex items-center p-1 text-muted-foreground transition-all duration-500">
            <svg-icon src="assets/icons/heroicons/solid/chevron-right.svg" [svgClass]="'h-5 w-5'"> </svg-icon>
          </button>
        </ng-template>
      </div>
      <!-- Submenu items -->
      <app-navbar-mobile-submenu [submenu]="sub"></app-navbar-mobile-submenu>
    </li>
  </ul>
</div>
