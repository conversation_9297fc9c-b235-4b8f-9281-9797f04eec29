<div class="relative bg-background">
  <div class="mx-auto px-5">
    <div class="flex items-center justify-between py-3.5 md:justify-start">
      <!-- Mobile Navigation Menu Button-->
      <div class="sm:order-1 md:hidden">
        <button
          (click)="toggleMobileMenu()"
          type="button"
          class="inline-flex items-center justify-center rounded-md bg-muted p-2 text-muted-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary hover:bg-muted-foreground hover:text-muted"
          aria-expanded="false"
        >
          <span class="sr-only">Open menu</span>
          <!-- Heroicon name: outline/menu -->
          <svg-icon
            src="assets/icons/heroicons/outline/menu.svg"
            [svgClass]="'h-6 w-6'"
          >
          </svg-icon>
        </button>
      </div>

      <!-- Logo -->
      <div
        class="flex items-center justify-start sm:order-2 md:mr-10 lg:hidden"
      >
        <a
          class="flex items-center justify-center rounded p-2 focus:outline-none focus:ring-1"
        >
          <!-- Logo de l'entreprise ou initiales de son nom officiel -->
          <ng-container *ngIf="company?.logo; else initials">
            <img
              [src]="company.logo"
              alt="Company Logo"
              class="h-8 w-8 rounded-full"
            />
          </ng-container>
          <ng-template #initials>
            <div
              class="h-8 w-8 flex items-center justify-center rounded-full bg-primary text-white"
            >
              {{ company.officialName | slice : 0 : 2 }}
              <!-- Affiche les 2 premières lettres -->
            </div>
          </ng-template>
        </a>
        <b class="hidden pl-3 text-sm font-bold text-foreground sm:block">{{
          company.officialName
        }}</b>
      </div>

      <!-- Desktop Menu -->
      <div class="hidden space-x-10 sm:order-3 md:flex">
        <app-navbar-menu></app-navbar-menu>
      </div>

      <!-- Barre de recherche centrée -->
      <div class="hidden sm:order-4 md:flex md:flex-1 md:justify-center">
        <div class="search-container relative w-full max-w-md">
          <div
            class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
          >
            <svg-icon
              src="assets/icons/heroicons/outline/search.svg"
              [svgClass]="'h-5 w-5 text-muted-foreground'"
            ></svg-icon>
          </div>
          <input
            type="text"
            [(ngModel)]="searchQuery"
            (input)="onSearchInput($event)"
            (keyup.enter)="onSearchSubmit()"
            placeholder="Rechercher..."
            class="block w-full rounded-lg border border-border bg-background pl-10 pr-3 py-2 text-sm placeholder-muted-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
          <!-- Résultats de recherche (dropdown) -->
          <div
            *ngIf="showSearchResults && searchResults.length > 0"
            class="search-results absolute top-full left-0 right-0 mt-1 bg-background border border-border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
          >
            <div class="p-2">
              <div
                *ngFor="let result of searchResults"
                (click)="selectSearchResult(result)"
                class="flex items-center p-2 rounded-md hover:bg-muted cursor-pointer"
              >
                <svg-icon
                  [src]="getSearchResultIcon(result.type)"
                  [svgClass]="'h-4 w-4 text-muted-foreground mr-3'"
                ></svg-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-foreground">
                    {{ result.title }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    {{ result.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications et Profile menu à droite -->
      <div class="flex items-center space-x-4 sm:order-5">
        <!-- Icône de notifications -->
        <div class="relative">
          <button
            (click)="toggleNotifications()"
            type="button"
            class="relative rounded-full bg-background p-2 text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background"
          >
            <span class="sr-only">Voir les notifications</span>
            <svg-icon
              src="assets/icons/heroicons/outline/bell.svg"
              [svgClass]="'h-6 w-6'"
            ></svg-icon>
            <!-- Badge de notification -->
            <span
              *ngIf="unreadNotificationsCount > 0"
              class="notification-badge absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full"
            >
              {{
                unreadNotificationsCount > 99 ? "99+" : unreadNotificationsCount
              }}
            </span>
          </button>

          <!-- Dropdown des notifications -->
          <div
            *ngIf="showNotifications"
            class="notification-dropdown absolute right-0 top-full mt-2 w-80 bg-background border border-border rounded-lg shadow-lg z-50"
          >
            <div class="p-4 border-b border-border">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-foreground">
                  Notifications
                </h3>
                <button
                  *ngIf="unreadNotificationsCount > 0"
                  (click)="markAllAsRead()"
                  class="text-sm text-primary hover:text-primary/80"
                >
                  Tout marquer comme lu
                </button>
              </div>
            </div>
            <div class="max-h-96 overflow-y-auto">
              <div
                *ngIf="notifications.length === 0"
                class="p-4 text-center text-muted-foreground"
              >
                Aucune notification
              </div>
              <div
                *ngFor="let notification of notifications"
                (click)="selectNotification(notification)"
                class="notification-item flex items-start p-4 hover:bg-muted cursor-pointer border-b border-border last:border-b-0"
                [class.unread]="!notification.read"
              >
                <div class="flex-shrink-0 mr-3">
                  <div
                    class="w-2 h-2 rounded-full"
                    [class.bg-primary]="!notification.read"
                    [class.bg-transparent]="notification.read"
                  ></div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-foreground">
                    {{ notification.title }}
                  </p>
                  <p class="text-sm text-muted-foreground">
                    {{ notification.message }}
                  </p>
                  <p class="text-xs text-muted-foreground mt-1">
                    {{ notification.createdAt | date : "short" }}
                  </p>
                </div>
              </div>
            </div>
            <div class="p-3 border-t border-border">
              <button
                (click)="viewAllNotifications()"
                class="w-full text-center text-sm text-primary hover:text-primary/80"
              >
                Voir toutes les notifications
              </button>
            </div>
          </div>
        </div>

        <!-- Profile menu -->
        <app-profile-menu data-tour="profile-menu"></app-profile-menu>
      </div>
    </div>
  </div>
  <!-- Mobile menu -->
  <app-navbar-mobile></app-navbar-mobile>
</div>
