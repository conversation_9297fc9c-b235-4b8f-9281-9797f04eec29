// Styles pour la barre de recherche
.search-container {
  position: relative;

  input {
    transition: all 0.2s ease-in-out;

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--primary), 0.1);
    }
  }

  .search-results {
    animation: slideDown 0.2s ease-out;
  }
}

// Styles pour les notifications
.notification-dropdown {
  animation: slideDown 0.2s ease-out;

  .notification-item {
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: rgba(var(--muted), 0.5);
    }

    &.unread {
      background-color: rgba(var(--primary), 0.05);
      border-left: 3px solid rgb(var(--primary));
    }
  }
}

// Badge de notification
.notification-badge {
  animation: pulse 2s infinite;
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// Responsive
@media (max-width: 768px) {
  .search-container {
    display: none; // Masquer la recherche sur mobile pour économiser l'espace
  }

  .notification-dropdown {
    width: 90vw;
    right: 5vw;
  }
}
