<div class="relative ml-3">
  <!-- Profile Button -->
  <button
    (click)="toggleMenu()"
    class="relative flex rounded-full bg-card text-sm"
    type="button"
  >
    <span class="sr-only">Ouvrir le menu utilisateur</span>

    <ng-container *ngIf="user$ | async as user">
      <img
        *ngIf="user.profile?.avatar; else initials"
        clickOutside
        (clickOutside)="isOpen = false"
        class="h-9 w-9 rounded-full"
        [src]="user.profile.avatar"
        alt="Avatar de l'utilisateur"
      />

      <!-- Si pas d'avatar, afficher la première lettre -->
      <ng-template #initials>
        <div
          class="h-9 w-9 flex items-center justify-center rounded-full text-white font-bold"
          [ngStyle]="{ 'background-color': getBackgroundColor(user) }"
        >
          {{ getInitials(user) }}
        </div>
      </ng-template>
    </ng-container>
  </button>
  <!-- Dropdown -->

  <div
    [@openClose]="isOpen ? 'open' : 'closed'"
    class="absolute right-0 z-20 mt-2 w-60 origin-top-right transform rounded-md bg-background py-4 shadow-custom ring-1 ring-transparent ring-opacity-5 transition focus:outline-none"
  >
    <div class="flex-row flex items-center px-4 pb-4">
      <div class="w-10 shrink-0">
        <ng-container *ngIf="user$ | async as user">
          <img
            *ngIf="user.profile?.avatar; else initials"
            clickOutside
            (clickOutside)="isOpen = false"
            class="h-9 w-9 rounded-full"
            [src]="user.profile.avatar"
            alt="Avatar de l'utilisateur"
          />

          <!-- Si pas d'avatar, afficher la première lettre -->
          <ng-template #initials>
            <div
              class="h-9 w-9 flex items-center justify-center rounded-full text-white font-bold"
              [ngStyle]="{ 'background-color': getBackgroundColor(user) }"
            >
              {{ getInitials(user) }}
            </div>
          </ng-template>
        </ng-container>
      </div>
      <div class="overflow-hidden px-2 text-sm font-semibold text-foreground">
        {{ (user$ | async)?.profile?.firstName }}
        {{ (user$ | async)?.profile?.lastName }}
        <p
          class="truncate text-ellipsis text-xs font-semibold text-muted-foreground"
        >
          {{ (user$ | async)?.email }}
        </p>
      </div>
    </div>

    <div class="border-b border-dashed border-border"></div>

    <ul class="my-2 mx-4 flex flex-col">
      @for (item of profileMenu; track $index) {
      <li
        routerLink="{{ item.link }}"
        :key="$index"
        class="inline-flex cursor-pointer items-center gap-2 rounded-md px-3 py-2 text-xs font-semibold text-muted-foreground hover:bg-card hover:text-primary"
      >
        <svg-icon
          src="{{ item.icon }}"
          [svgClass]="'h-5 w-5 text-muted-foreground/50'"
        >
        </svg-icon>
        {{ item.title }}
      </li>
      }

      <!-- Bouton pour relancer le tour -->
      <li
        (click)="startTour()"
        class="inline-flex cursor-pointer items-center gap-2 rounded-md px-3 py-2 text-xs font-semibold text-muted-foreground hover:bg-card hover:text-primary"
      >
        <svg-icon
          src="assets/icons/heroicons/outline/information-circle.svg"
          [svgClass]="'h-5 w-5 text-muted-foreground/50'"
        >
        </svg-icon>
        Tour guidé
      </li>
    </ul>
    <hr class="border-dashed border-border" />

    <div class="mx-4 my-2">
      <span class="text-xs font-semibold text-foreground">Thème</span>
      <div class="mt-2 grid grid-cols-2 gap-2">
        @for (item of themeMode; track $index) {
        <div
          :key="$index"
          (click)="toggleThemeMode()"
          [ngClass]="{
            'border-muted-foreground bg-card': item == themeService.theme().mode
          }"
          class="focus-visible:ring-ring inline-flex h-8 cursor-pointer items-center justify-start whitespace-nowrap rounded-md border border-border bg-background px-3 text-xs font-medium text-muted-foreground shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50 hover:bg-card hover:text-foreground"
        >
          <svg-icon
            [src]="
              item == 'light'
                ? 'assets/icons/heroicons/outline/sun.svg'
                : 'assets/icons/heroicons/outline/moon.svg'
            "
            [svgClass]="'h-5 mr-2 w-5 text-muted-foreground/50'"
          >
          </svg-icon>
          <p class="capitalize">{{ getThemeLabel(item) }}</p>
        </div>
        }
      </div>
    </div>
  </div>
</div>
