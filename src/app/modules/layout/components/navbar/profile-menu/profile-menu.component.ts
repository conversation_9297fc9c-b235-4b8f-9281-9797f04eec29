import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { AsyncPip<PERSON>, <PERSON>sonPip<PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON>, <PERSON><PERSON>tyle } from '@angular/common';
import { ClickOutsideDirective } from '../../../../../shared/directives/click-outside.directive';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { ThemeService } from '../../../../../core/services/theme.service';
import {
  trigger,
  state,
  style,
  animate,
  transition,
} from '@angular/animations';
import { Observable } from 'rxjs';
import { Store } from '@ngrx/store';
import { selectCurrentUser } from 'src/app/core/store/user/user.selector';
import { AppState } from 'src/app/core/store/app.state';
import { TourInitializerService } from '../../../../../core/services/tour/tour-initializer.service';

@Component({
  selector: 'app-profile-menu',
  templateUrl: './profile-menu.component.html',
  styleUrls: ['./profile-menu.component.scss'],
  standalone: true,
  imports: [
    ClickOutsideDirective,
    NgClass,
    RouterLink,
    AngularSvgIconModule,
    AsyncPipe,
    NgStyle,
    NgIf,
  ],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          opacity: 1,
          transform: 'translateY(0)',
          visibility: 'visible',
        })
      ),
      state(
        'closed',
        style({
          opacity: 0,
          transform: 'translateY(-20px)',
          visibility: 'hidden',
        })
      ),
      transition('open => closed', [animate('0.2s')]),
      transition('closed => open', [animate('0.2s')]),
    ]),
  ],
})
export class ProfileMenuComponent implements OnInit {
  public isOpen = false;
  public user$!: Observable<any>;
  public profileMenu = [
    {
      title: 'Votre Profil',
      icon: './assets/icons/heroicons/outline/user-circle.svg',
      link: '/dashboard/work-in-progress',
    },
    {
      title: 'Paramètres',
      icon: './assets/icons/heroicons/outline/cog-6-tooth.svg',
      link: '/dashboard/work-in-progress',
    },
    {
      title: 'Se déconnecter',
      icon: './assets/icons/heroicons/outline/logout.svg',
      link: '/auth',
    },
  ];

  public themeMode = ['light', 'dark'];

  constructor(
    public themeService: ThemeService,
    private store: Store<AppState>,
    private tourInitializer: TourInitializerService
  ) {}

  ngOnInit(): void {
    this.user$ = this.store.select(selectCurrentUser);
    console.log(this.store.select(selectCurrentUser));
  }

  public toggleMenu(): void {
    this.isOpen = !this.isOpen;
  }

  toggleThemeMode() {
    this.themeService.theme.update((theme) => {
      const mode = !this.themeService.isDark ? 'dark' : 'light';
      return { ...theme, mode: mode };
    });
  }

  toggleThemeColor(color: string) {
    this.themeService.theme.update((theme) => {
      return { ...theme, color: color };
    });
  }
  getBackgroundColor(user: any): string {
    const colors = [
      '#f44336',
      '#e91e63',
      '#9c27b0',
      '#673ab7',
      '#3f51b5',
      '#2196f3',
      '#03a9f4',
      '#00bcd4',
      '#009688',
      '#4caf50',
    ];

    if (!user?.id) return colors[0];

    // Convertir l'UUID en hash numérique
    let hash = 0;
    for (let i = 0; i < user.id.length; i++) {
      const char = user.id.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convertir en entier 32-bit
    }

    const index = Math.abs(hash) % colors.length;
    return colors[index];
  }
  getInitials(user: any): string {
    return user?.profile?.firstName
      ? user.profile.firstName.charAt(0).toUpperCase()
      : '?';
  }

  getThemeLabel(mode: string): string {
    const themeLabels: { [key: string]: string } = {
      light: 'Clair',
      dark: 'Sombre',
    };
    return themeLabels[mode] || mode;
  }

  startTour(): void {
    this.isOpen = false; // Fermer le menu
    this.tourInitializer.resetAndStartTour();
  }
}
