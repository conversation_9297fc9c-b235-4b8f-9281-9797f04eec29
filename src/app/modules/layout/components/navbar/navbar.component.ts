import { Component, OnInit, HostListener } from '@angular/core';
import { MenuService } from '../../services/menu.service';
import { NavbarMobileComponent } from './navbar-mobile/navbar-mobilecomponent';
import { ProfileMenuComponent } from './profile-menu/profile-menu.component';
import { NavbarMenuComponent } from './navbar-menu/navbar-menu.component';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { Company } from 'src/app/core/models/company.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { NgIf, SlicePipe, NgFor, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

// Interfaces pour les types
interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: 'employee' | 'document' | 'task' | 'report' | 'other';
  url?: string;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  type: 'info' | 'warning' | 'success' | 'error';
  url?: string;
}

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
  standalone: true,
  imports: [
    AngularSvgIconModule,
    NavbarMenuComponent,
    ProfileMenuComponent,
    NavbarMobileComponent,
    NgIf,
    NgFor,
    SlicePipe,
    DatePipe,
    FormsModule,
  ],
})
export class NavbarComponent implements OnInit {
  company!: Company;

  // Propriétés pour la recherche
  searchQuery = '';
  showSearchResults = false;
  searchResults: SearchResult[] = [];

  // Propriétés pour les notifications
  showNotifications = false;
  notifications: Notification[] = [];
  unreadNotificationsCount = 0;

  constructor(
    private menuService: MenuService,
    private store: Store<AppState>,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });

    // Charger les notifications initiales
    this.loadNotifications();
  }

  public toggleMobileMenu(): void {
    this.menuService.showMobileMenu = true;
  }

  // Méthodes pour la recherche
  onSearchInput(event: any): void {
    const query = event.target.value.trim();
    this.searchQuery = query;

    if (query.length >= 2) {
      this.performSearch(query);
      this.showSearchResults = true;
    } else {
      this.showSearchResults = false;
      this.searchResults = [];
    }
  }

  onSearchSubmit(): void {
    if (this.searchQuery.trim()) {
      this.performSearch(this.searchQuery);
      // Optionnel : naviguer vers une page de résultats de recherche
      // this.router.navigate(['/search'], { queryParams: { q: this.searchQuery } });
    }
  }

  private performSearch(query: string): void {
    // Simulation de résultats de recherche
    // Dans un vrai projet, ceci ferait appel à un service de recherche
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: 'Jean Dupont',
        description: 'Développeur Frontend',
        type: 'employee',
        url: '/employees/1',
      },
      {
        id: '2',
        title: 'Rapport mensuel',
        description: 'Rapport de performance - Janvier 2024',
        type: 'report',
        url: '/reports/monthly/1',
      },
      {
        id: '3',
        title: 'Contrat de travail',
        description: 'Template de contrat standard',
        type: 'document',
        url: '/documents/contracts/1',
      },
    ];

    // Filtrer les résultats basés sur la requête
    this.searchResults = mockResults.filter(
      (result) =>
        result.title.toLowerCase().includes(query.toLowerCase()) ||
        result.description.toLowerCase().includes(query.toLowerCase())
    );
  }

  selectSearchResult(result: SearchResult): void {
    this.showSearchResults = false;
    this.searchQuery = '';

    if (result.url) {
      this.router.navigate([result.url]);
    }
  }

  getSearchResultIcon(type: string): string {
    const icons = {
      employee: 'assets/icons/heroicons/outline/user.svg',
      document: 'assets/icons/heroicons/outline/document.svg',
      task: 'assets/icons/heroicons/outline/clipboard-list.svg',
      report: 'assets/icons/heroicons/outline/chart-bar.svg',
      other: 'assets/icons/heroicons/outline/folder.svg',
    };
    return icons[type as keyof typeof icons] || icons.other;
  }

  // Méthodes pour les notifications
  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
  }

  private loadNotifications(): void {
    // Simulation de notifications
    // Dans un vrai projet, ceci ferait appel à un service de notifications
    this.notifications = [
      {
        id: '1',
        title: 'Nouveau employé',
        message: "Marie Martin a rejoint l'équipe",
        read: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        type: 'info',
      },
      {
        id: '2',
        title: 'Rapport prêt',
        message: 'Le rapport mensuel est disponible',
        read: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        type: 'success',
      },
      {
        id: '3',
        title: 'Congé en attente',
        message: 'Demande de congé de Pierre Durand',
        read: true,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        type: 'warning',
      },
    ];

    this.updateUnreadCount();
  }

  private updateUnreadCount(): void {
    this.unreadNotificationsCount = this.notifications.filter(
      (n) => !n.read
    ).length;
  }

  selectNotification(notification: Notification): void {
    // Marquer comme lu
    notification.read = true;
    this.updateUnreadCount();

    // Fermer le dropdown
    this.showNotifications = false;

    // Naviguer si URL fournie
    if (notification.url) {
      this.router.navigate([notification.url]);
    }
  }

  markAllAsRead(): void {
    this.notifications.forEach((notification) => {
      notification.read = true;
    });
    this.updateUnreadCount();
  }

  viewAllNotifications(): void {
    this.showNotifications = false;
    this.router.navigate(['/notifications']);
  }

  // Fermer les dropdowns quand on clique ailleurs
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;

    // Fermer la recherche si on clique ailleurs
    if (!target.closest('.relative')) {
      this.showSearchResults = false;
    }

    // Fermer les notifications si on clique ailleurs
    if (!target.closest('.relative')) {
      this.showNotifications = false;
    }
  }
}
