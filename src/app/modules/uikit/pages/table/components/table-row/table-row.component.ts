import { Component, Input } from '@angular/core';
import { User } from '../../model/user.model';
import { FormsModule } from '@angular/forms';
import { AngularSvgIconModule } from 'angular-svg-icon';

@Component({
    selector: '[app-table-row]',
    imports: [FormsModule, AngularSvgIconModule],
    templateUrl: './table-row.component.html',
    styleUrl: './table-row.component.scss'
})
export class TableRowComponent {
  @Input() user: User = <User>{};

  constructor() {}
}
