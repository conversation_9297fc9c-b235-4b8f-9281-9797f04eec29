import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TourInitializerService } from '../../../../core/services/tour/tour-initializer.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tour-help',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div
      *ngIf="showHelp && isDashboard"
      class="fixed bottom-4 right-4 z-[9998] bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4 max-w-sm"
    >
      <div class="flex items-center justify-between mb-3">
        <h3
          class="text-sm font-semibold text-gray-900 dark:text-white flex items-center"
        >
          <svg
            class="w-4 h-4 mr-2 text-blue-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          Aide & Support
        </h3>
        <button
          (click)="toggleHelp()"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <div class="space-y-3">
        <!-- Tour guidé -->
        <button
          (click)="startTour()"
          class="w-full flex items-center p-3 text-left bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors group"
        >
          <div
            class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-lg mr-3 group-hover:bg-blue-700 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              ></path>
            </svg>
          </div>
          <div>
            <div class="font-medium text-gray-900 dark:text-white text-sm">
              Tour guidé
            </div>
            <div class="text-gray-600 dark:text-gray-400 text-xs">
              Découvrez les fonctionnalités
            </div>
          </div>
        </button>

        <!-- Support -->
        <button
          (click)="contactSupport()"
          class="w-full flex items-center p-3 text-left bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors group"
        >
          <div
            class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-lg mr-3 group-hover:bg-green-700 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"
              ></path>
            </svg>
          </div>
          <div>
            <div class="font-medium text-gray-900 dark:text-white text-sm">
              Contacter le support
            </div>
            <div class="text-gray-600 dark:text-gray-400 text-xs">
              Besoin d'aide ? Nous sommes là
            </div>
          </div>
        </button>

        <!-- Documentation -->
        <button
          (click)="openDocumentation()"
          class="w-full flex items-center p-3 text-left bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors group"
        >
          <div
            class="flex items-center justify-center w-8 h-8 bg-purple-600 text-white rounded-lg mr-3 group-hover:bg-purple-700 transition-colors"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              ></path>
            </svg>
          </div>
          <div>
            <div class="font-medium text-gray-900 dark:text-white text-sm">
              Documentation
            </div>
            <div class="text-gray-600 dark:text-gray-400 text-xs">
              Guide d'utilisation complet
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Bouton flottant d'aide -->
    <button
      *ngIf="!showHelp && isDashboard"
      (click)="toggleHelp()"
      class="fixed bottom-4 right-4 z-[9998] w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-200 hover:scale-105"
      title="Aide & Support"
    >
      <svg
        class="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        ></path>
      </svg>
    </button>
  `,
  styles: [
    `
      :host {
        position: fixed;
        pointer-events: none;
        z-index: 9998;
      }

      :host > * {
        pointer-events: auto;
      }
    `,
  ],
})
export class TourHelpComponent implements OnInit, OnDestroy {
  showHelp = false;
  isDashboard = false;

  private subscription = new Subscription();

  constructor(
    private tourInitializer: TourInitializerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.checkIfDashboard();

    // Écouter les changements de route
    this.subscription.add(
      this.router.events.subscribe(() => {
        this.checkIfDashboard();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private checkIfDashboard(): void {
    this.isDashboard = this.router.url.includes('/dashboard');
    if (!this.isDashboard) {
      this.showHelp = false;
    }
  }

  toggleHelp(): void {
    this.showHelp = !this.showHelp;
  }

  startTour(): void {
    this.showHelp = false;
    this.tourInitializer.resetAndStartTour();
  }

  contactSupport(): void {
    this.showHelp = false;
    // Ici vous pouvez ajouter votre logique de support
    // Par exemple, ouvrir un chat, un formulaire de contact, etc.
    alert(
      'Fonctionnalité de support à implémenter\n\nVous pouvez :\n- Ajouter un chat en ligne\n- Rediriger vers un formulaire de contact\n- Ouvrir un système de tickets'
    );
  }

  openDocumentation(): void {
    this.showHelp = false;
    // Naviguer vers la page de documentation
    this.router.navigate(['/documentation']);
  }
}
