import {
  Component,
  On<PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  ChangeDetectorRef,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TourService } from '../../../../core/services/tour/tour.service';
import { TourStep, TourState } from '../../../../core/models/tour.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tour-step',
  standalone: true,
  imports: [CommonModule],
  host: {
    '[class.active]': 'tourState.isActive',
  },
  template: `
    <div
      *ngIf="tourState.isActive && currentStep"
      class="fixed inset-0 z-[9999] pointer-events-none"
    >
      <!-- Backdrop -->
      <div
        class="absolute inset-0 bg-black/50 transition-opacity duration-300"
        [class.opacity-100]="showBackdrop"
        [class.opacity-0]="!showBackdrop"
      ></div>

      <!-- Highlight overlay -->
      <div
        *ngIf="targetElement"
        class="absolute border-4 border-primary rounded-lg shadow-2xl transition-all duration-300 animate-pulse"
        [style.top.px]="highlightRect.top - highlightPadding"
        [style.left.px]="highlightRect.left - highlightPadding"
        [style.width.px]="highlightRect.width + highlightPadding * 2"
        [style.height.px]="highlightRect.height + highlightPadding * 2"
        style="box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3), 0 0 20px rgba(59, 130, 246, 0.4);"
      ></div>

      <!-- Tour step popup -->
      <div
        #tourPopup
        class="absolute pointer-events-auto bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 max-w-sm w-80 z-10 transform transition-all duration-200"
        [style.top.px]="popupPosition.top"
        [style.left.px]="popupPosition.left"
        style="box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);"
      >
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ currentStep.title }}
            </h3>
            <button
              (click)="skipTour()"
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="p-4">
          <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
            {{ currentStep.content }}
          </p>
        </div>

        <!-- Footer -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <!-- Step counter -->
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ tourState.currentStepIndex + 1 }} de {{ totalSteps }}
            </span>

            <!-- Navigation buttons -->
            <div class="flex space-x-2">
              <button
                *ngIf="tourState.currentStepIndex > 0"
                (click)="previousStep()"
                class="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
              >
                Précédent
              </button>

              <button
                *ngIf="tourState.currentStepIndex < totalSteps - 1"
                (click)="nextStep()"
                class="px-3 py-1.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors"
              >
                Suivant
              </button>

              <button
                *ngIf="tourState.currentStepIndex === totalSteps - 1"
                (click)="completeTour()"
                class="px-3 py-1.5 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
              >
                Terminer
              </button>
            </div>
          </div>
        </div>

        <!-- Arrow pointer -->
        <div
          class="absolute w-3 h-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transform rotate-45"
          [ngClass]="arrowClasses"
          [style.top.px]="arrowPosition.top"
          [style.left.px]="arrowPosition.left"
        ></div>
      </div>
    </div>
  `,
  styles: [
    `
      :host {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 9999;
        display: none;
      }

      :host(.active) {
        display: block;
      }
    `,
  ],
})
export class TourStepComponent implements OnInit, OnDestroy {
  @ViewChild('tourPopup', { static: false }) tourPopup!: ElementRef;

  tourState: TourState = {
    isActive: false,
    currentStepIndex: 0,
    isCompleted: false,
    isFirstVisit: true,
  };

  currentStep: TourStep | null = null;
  targetElement: Element | null = null;
  totalSteps = 0;
  showBackdrop = true;
  highlightPadding = 8;

  highlightRect = { top: 0, left: 0, width: 0, height: 0 };
  popupPosition = { top: 0, left: 0 };
  arrowPosition = { top: 0, left: 0 };
  arrowClasses = '';

  private subscription = new Subscription();

  constructor(
    private tourService: TourService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.subscription.add(
      this.tourService.tourState$.subscribe((state) => {
        this.tourState = state;
        this.updateTourStep();
        this.cdr.detectChanges();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private updateTourStep(): void {
    if (!this.tourState.isActive) {
      this.currentStep = null;
      this.targetElement = null;
      return;
    }

    this.currentStep = this.tourService.getCurrentStep();
    this.totalSteps = this.tourService.getTotalSteps();

    if (this.currentStep) {
      // Attendre un tick pour que le DOM soit mis à jour
      setTimeout(() => {
        this.updateTargetElement();
        this.updatePositions();
      }, 100);
    }
  }

  private updateTargetElement(): void {
    if (!this.currentStep) return;

    this.targetElement = document.querySelector(this.currentStep.target);
    if (this.targetElement) {
      const rect = this.targetElement.getBoundingClientRect();
      this.highlightRect = {
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
        height: rect.height,
      };
    }
  }

  private updatePositions(): void {
    if (!this.currentStep || !this.targetElement) return;

    const targetRect = this.targetElement.getBoundingClientRect();
    const popupWidth = 320; // w-80 = 320px
    const popupHeight = 250; // Estimation plus réaliste
    const arrowSize = 12;
    const margin = 20; // Plus de marge

    let top = 0;
    let left = 0;
    let arrowTop = 0;
    let arrowLeft = 0;
    let arrowClass = '';

    switch (this.currentStep.position) {
      case 'top':
        top = targetRect.top + window.scrollY - popupHeight - margin;
        left =
          targetRect.left +
          window.scrollX +
          targetRect.width / 2 -
          popupWidth / 2;
        arrowTop = popupHeight - 6;
        arrowLeft = popupWidth / 2 - 6;
        arrowClass = 'border-t-0 border-l-0';
        break;

      case 'bottom':
        top = targetRect.bottom + window.scrollY + margin;
        left =
          targetRect.left +
          window.scrollX +
          targetRect.width / 2 -
          popupWidth / 2;
        arrowTop = -6;
        arrowLeft = popupWidth / 2 - 6;
        arrowClass = 'border-b-0 border-r-0';
        break;

      case 'left':
        top =
          targetRect.top +
          window.scrollY +
          targetRect.height / 2 -
          popupHeight / 2;
        left = targetRect.left + window.scrollX - popupWidth - margin;
        arrowTop = popupHeight / 2 - 6;
        arrowLeft = popupWidth - 6;
        arrowClass = 'border-t-0 border-r-0';
        break;

      case 'right':
        top =
          targetRect.top +
          window.scrollY +
          targetRect.height / 2 -
          popupHeight / 2;
        left = targetRect.right + window.scrollX + margin;
        arrowTop = popupHeight / 2 - 6;
        arrowLeft = -6;
        arrowClass = 'border-b-0 border-l-0';
        break;
    }

    // Ajuster pour rester dans la viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY;

    // Ajustements horizontaux
    if (left < margin) left = margin;
    if (left + popupWidth > viewportWidth - margin)
      left = viewportWidth - popupWidth - margin;

    // Ajustements verticaux avec prise en compte du scroll
    const minTop = scrollY + margin;
    const maxTop = scrollY + viewportHeight - popupHeight - margin;

    if (top < minTop) {
      top = minTop;
    }
    if (top > maxTop) {
      top = maxTop;
    }

    this.popupPosition = { top, left };
    this.arrowPosition = { top: arrowTop, left: arrowLeft };
    this.arrowClasses = arrowClass;
  }

  nextStep(): void {
    this.tourService.nextStep();
  }

  previousStep(): void {
    this.tourService.previousStep();
  }

  skipTour(): void {
    this.tourService.skipTour();
  }

  completeTour(): void {
    this.tourService.completeTour();
  }
}
