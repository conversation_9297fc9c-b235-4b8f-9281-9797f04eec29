import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TourService } from '../../../../core/services/tour/tour.service';
import { TourInitializerService } from '../../../../core/services/tour/tour-initializer.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tour-welcome',
  standalone: true,
  imports: [CommonModule],
  host: {
    '[class.show]': 'showWelcome',
  },
  template: `
    <div
      *ngIf="showWelcome"
      class="fixed inset-0 z-[10000] flex items-center justify-center bg-black/50 backdrop-blur-sm"
      style="pointer-events: auto;"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
      >
        <!-- Header avec gradient -->
        <div class="bg-gradient-to-r from-primary to-primary/80 p-6 text-white">
          <div class="flex items-center justify-center mb-4">
            <div
              class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center"
            >
              <svg
                class="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                ></path>
              </svg>
            </div>
          </div>
          <h2 class="text-2xl font-bold text-center mb-2">
            Bienvenue dans LuminaHR ! 👋
          </h2>
          <p class="text-center text-white/90 text-sm">
            Votre nouvelle solution de gestion des ressources humaines
          </p>
        </div>

        <!-- Contenu -->
        <div class="p-6">
          <div class="space-y-4 mb-6">
            <div class="flex items-start space-x-3">
              <div
                class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0"
              >
                <svg
                  class="w-4 h-4 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white text-sm">
                  Gestion complète des employés
                </h3>
                <p class="text-gray-600 dark:text-gray-300 text-xs">
                  Profils, contrats, et suivi des performances
                </p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div
                class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0"
              >
                <svg
                  class="w-4 h-4 text-blue-600 dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white text-sm">
                  Gestion du temps et congés
                </h3>
                <p class="text-gray-600 dark:text-gray-300 text-xs">
                  Feuilles de temps, demandes de congés automatisées
                </p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div
                class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0"
              >
                <svg
                  class="w-4 h-4 text-purple-600 dark:text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white text-sm">
                  Analyses et rapports
                </h3>
                <p class="text-gray-600 dark:text-gray-300 text-xs">
                  Tableaux de bord et métriques en temps réel
                </p>
              </div>
            </div>
          </div>

          <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
            <div class="flex items-center space-x-2 mb-2">
              <svg
                class="w-5 h-5 text-blue-600 dark:text-blue-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
              <h4
                class="font-semibold text-blue-900 dark:text-blue-100 text-sm"
              >
                Tour guidé disponible
              </h4>
            </div>
            <p class="text-blue-800 dark:text-blue-200 text-xs">
              Nous vous proposons un tour guidé de 2 minutes pour découvrir les
              principales fonctionnalités.
            </p>
          </div>

          <!-- Boutons d'action -->
          <div class="flex space-x-3">
            <button
              (click)="skipTour()"
              class="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              Passer
            </button>
            <button
              (click)="startTour()"
              class="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-lg transition-colors shadow-sm"
            >
              Commencer le tour
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      :host {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10000;
      }

      :host(.show) {
        pointer-events: auto;
      }
    `,
  ],
})
export class TourWelcomeComponent implements OnInit, OnDestroy {
  showWelcome = false;
  private subscription = new Subscription();

  constructor(
    private tourService: TourService,
    private tourInitializer: TourInitializerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Vérifier périodiquement si on doit afficher le welcome
    this.checkWelcomeDisplay();

    // Écouter les changements de route
    this.subscription.add(
      this.router.events.subscribe(() => {
        setTimeout(() => this.checkWelcomeDisplay(), 500);
      })
    );
  }

  private checkWelcomeDisplay(): void {
    const shouldShow = this.shouldShowWelcome();
    if (shouldShow && !this.showWelcome) {
      // Attendre un peu pour que la page soit chargée
      setTimeout(() => {
        if (this.shouldShowWelcome()) {
          this.showWelcome = true;
        }
      }, 1000);
    } else if (!shouldShow && this.showWelcome) {
      this.showWelcome = false;
    }
  }

  private shouldShowWelcome(): boolean {
    // Vérifier qu'on est sur le dashboard
    const isDashboard = this.router.url.includes('/dashboard');

    // Vérifier que c'est la première visite et que le tour n'est pas complété
    const isFirstVisit = this.tourService.isFirstVisit();
    const isNotCompleted = !this.tourService.isTourCompleted();

    console.log('Tour Welcome Check:', {
      currentUrl: this.router.url,
      isDashboard,
      isFirstVisit,
      isNotCompleted,
      shouldShow: isDashboard && isFirstVisit && isNotCompleted,
    });

    return isDashboard && isFirstVisit && isNotCompleted;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  startTour(): void {
    console.log('Démarrage du tour...');
    this.showWelcome = false;
    this.tourInitializer.startTourManually();
  }

  skipTour(): void {
    console.log('Tour ignoré...');
    this.showWelcome = false;
    this.tourService.completeTour();
  }
}
