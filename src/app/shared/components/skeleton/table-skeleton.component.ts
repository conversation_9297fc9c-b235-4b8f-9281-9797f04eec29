import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SkeletonComponent } from './skeleton.component';

@Component({
  selector: 'app-table-skeleton',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  template: `
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- Header du tableau -->
      <div *ngIf="showHeader" class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <app-skeleton 
            variant="text" 
            width="200px" 
            height="20px"
          ></app-skeleton>
          <div class="flex space-x-2">
            <app-skeleton 
              variant="rounded" 
              width="100px" 
              height="32px"
            ></app-skeleton>
            <app-skeleton 
              variant="rounded" 
              width="80px" 
              height="32px"
            ></app-skeleton>
          </div>
        </div>
      </div>

      <!-- Tableau -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- En-têtes de colonnes -->
          <thead class="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th *ngFor="let col of columns" class="px-6 py-3 text-left">
                <app-skeleton 
                  variant="text" 
                  [width]="col.width" 
                  height="16px"
                ></app-skeleton>
              </th>
            </tr>
          </thead>
          
          <!-- Lignes du tableau -->
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr *ngFor="let row of rows" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td *ngFor="let col of columns; let colIndex = index" class="px-6 py-4 whitespace-nowrap">
                <!-- Avatar pour la première colonne si activé -->
                <div *ngIf="colIndex === 0 && showAvatars" class="flex items-center space-x-3">
                  <app-skeleton 
                    variant="circular" 
                    width="32px" 
                    height="32px"
                  ></app-skeleton>
                  <app-skeleton 
                    variant="text" 
                    [width]="col.contentWidth" 
                    height="14px"
                  ></app-skeleton>
                </div>
                
                <!-- Contenu normal -->
                <app-skeleton 
                  *ngIf="!(colIndex === 0 && showAvatars)"
                  variant="text" 
                  [width]="col.contentWidth" 
                  height="14px"
                ></app-skeleton>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div *ngIf="showPagination" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <app-skeleton 
            variant="text" 
            width="150px" 
            height="14px"
          ></app-skeleton>
          <div class="flex space-x-2">
            <app-skeleton 
              variant="rounded" 
              width="32px" 
              height="32px"
            ></app-skeleton>
            <app-skeleton 
              variant="rounded" 
              width="32px" 
              height="32px"
            ></app-skeleton>
            <app-skeleton 
              variant="rounded" 
              width="32px" 
              height="32px"
            ></app-skeleton>
            <app-skeleton 
              variant="rounded" 
              width="32px" 
              height="32px"
            ></app-skeleton>
          </div>
        </div>
      </div>
    </div>
  `
})
export class TableSkeletonComponent {
  @Input() showHeader: boolean = true;
  @Input() showPagination: boolean = true;
  @Input() showAvatars: boolean = false;
  @Input() rowsCount: number = 5;
  @Input() columnsCount: number = 4;

  get columns() {
    const columns = [];
    const widths = ['120px', '100px', '80px', '90px', '110px'];
    const contentWidths = ['80%', '60%', '70%', '85%', '75%'];
    
    for (let i = 0; i < this.columnsCount; i++) {
      columns.push({
        width: widths[i % widths.length],
        contentWidth: contentWidths[i % contentWidths.length]
      });
    }
    return columns;
  }

  get rows() {
    const rows = [];
    for (let i = 0; i < this.rowsCount; i++) {
      rows.push({ id: i });
    }
    return rows;
  }
}
