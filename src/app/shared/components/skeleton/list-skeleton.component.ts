import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SkeletonComponent } from './skeleton.component';

@Component({
  selector: 'app-list-skeleton',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  template: `
    <div class="space-y-4">
      <div 
        *ngFor="let item of items" 
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4"
      >
        <div class="flex items-center space-x-4">
          <!-- Avatar/Image -->
          <app-skeleton 
            *ngIf="showAvatar"
            [variant]="avatarVariant" 
            [width]="avatarSize" 
            [height]="avatarSize"
          ></app-skeleton>

          <!-- Contenu principal -->
          <div class="flex-1 space-y-2">
            <!-- Titre -->
            <app-skeleton 
              variant="text" 
              [width]="titleWidth" 
              height="16px"
            ></app-skeleton>
            
            <!-- Sous-titre -->
            <app-skeleton 
              *ngIf="showSubtitle"
              variant="text" 
              [width]="subtitleWidth" 
              height="14px"
            ></app-skeleton>
            
            <!-- Description -->
            <div *ngIf="showDescription" class="space-y-1">
              <app-skeleton 
                *ngFor="let line of descriptionLines"
                variant="text" 
                [width]="line.width" 
                height="12px"
              ></app-skeleton>
            </div>
          </div>

          <!-- Actions à droite -->
          <div *ngIf="showActions" class="flex flex-col space-y-2">
            <app-skeleton 
              *ngFor="let action of actions"
              variant="rounded" 
              [width]="action.width" 
              height="28px"
            ></app-skeleton>
          </div>

          <!-- Badge/Status -->
          <app-skeleton 
            *ngIf="showBadge"
            variant="rounded" 
            width="60px" 
            height="20px"
          ></app-skeleton>
        </div>

        <!-- Métadonnées en bas -->
        <div *ngIf="showMetadata" class="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div class="flex space-x-4">
            <app-skeleton 
              *ngFor="let meta of metadata"
              variant="text" 
              [width]="meta.width" 
              height="12px"
            ></app-skeleton>
          </div>
          <app-skeleton 
            variant="text" 
            width="80px" 
            height="12px"
          ></app-skeleton>
        </div>
      </div>
    </div>
  `
})
export class ListSkeletonComponent {
  @Input() itemsCount: number = 5;
  @Input() showAvatar: boolean = true;
  @Input() showSubtitle: boolean = true;
  @Input() showDescription: boolean = true;
  @Input() showActions: boolean = true;
  @Input() showBadge: boolean = false;
  @Input() showMetadata: boolean = false;
  @Input() avatarVariant: 'circular' | 'rounded' = 'circular';
  @Input() avatarSize: string = '48px';
  @Input() titleWidth: string = '60%';
  @Input() subtitleWidth: string = '40%';
  @Input() descriptionLinesCount: number = 2;
  @Input() actionsCount: number = 2;
  @Input() metadataCount: number = 3;

  get items() {
    const items = [];
    for (let i = 0; i < this.itemsCount; i++) {
      items.push({ id: i });
    }
    return items;
  }

  get descriptionLines() {
    const lines = [];
    const widths = ['90%', '75%', '85%'];
    
    for (let i = 0; i < this.descriptionLinesCount; i++) {
      lines.push({
        width: widths[i % widths.length]
      });
    }
    return lines;
  }

  get actions() {
    const actions = [];
    for (let i = 0; i < this.actionsCount; i++) {
      actions.push({
        width: i === 0 ? '80px' : '60px'
      });
    }
    return actions;
  }

  get metadata() {
    const metadata = [];
    const widths = ['60px', '80px', '70px'];
    
    for (let i = 0; i < this.metadataCount; i++) {
      metadata.push({
        width: widths[i % widths.length]
      });
    }
    return metadata;
  }
}
