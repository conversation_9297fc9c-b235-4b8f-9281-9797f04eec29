import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SkeletonComponent } from './skeleton.component';
import { CardSkeletonComponent } from './card-skeleton.component';
import { TableSkeletonComponent } from './table-skeleton.component';
import { ListSkeletonComponent } from './list-skeleton.component';

@Component({
  selector: 'app-page-skeleton',
  standalone: true,
  imports: [
    CommonModule, 
    SkeletonComponent, 
    CardSkeletonComponent, 
    TableSkeletonComponent,
    ListSkeletonComponent
  ],
  template: `
    <div class="space-y-6">
      <!-- Header de la page -->
      <div *ngIf="showPageHeader" class="flex items-center justify-between">
        <div class="space-y-2">
          <app-skeleton 
            variant="text" 
            width="300px" 
            height="32px"
          ></app-skeleton>
          <app-skeleton 
            variant="text" 
            width="200px" 
            height="16px"
          ></app-skeleton>
        </div>
        <div class="flex space-x-3">
          <app-skeleton 
            variant="rounded" 
            width="120px" 
            height="40px"
          ></app-skeleton>
          <app-skeleton 
            variant="rounded" 
            width="100px" 
            height="40px"
          ></app-skeleton>
        </div>
      </div>

      <!-- Breadcrumb -->
      <div *ngIf="showBreadcrumb" class="flex items-center space-x-2">
        <app-skeleton 
          *ngFor="let crumb of breadcrumbs; let last = last"
          variant="text" 
          [width]="crumb.width" 
          height="14px"
        ></app-skeleton>
        <span *ngIf="!last" class="text-gray-400">/</span>
      </div>

      <!-- Statistiques/KPIs -->
      <div *ngIf="showStats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div *ngFor="let stat of stats" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between">
            <div class="space-y-2">
              <app-skeleton 
                variant="text" 
                width="80px" 
                height="12px"
              ></app-skeleton>
              <app-skeleton 
                variant="text" 
                width="60px" 
                height="24px"
              ></app-skeleton>
            </div>
            <app-skeleton 
              variant="rounded" 
              width="40px" 
              height="40px"
            ></app-skeleton>
          </div>
        </div>
      </div>

      <!-- Filtres et recherche -->
      <div *ngIf="showFilters" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div class="flex flex-wrap items-center gap-4">
          <app-skeleton 
            variant="rounded" 
            width="200px" 
            height="36px"
          ></app-skeleton>
          <app-skeleton 
            variant="rounded" 
            width="120px" 
            height="36px"
          ></app-skeleton>
          <app-skeleton 
            variant="rounded" 
            width="100px" 
            height="36px"
          ></app-skeleton>
          <app-skeleton 
            variant="rounded" 
            width="80px" 
            height="36px"
          ></app-skeleton>
        </div>
      </div>

      <!-- Contenu principal selon le type -->
      <div [ngSwitch]="contentType">
        <!-- Tableau -->
        <app-table-skeleton 
          *ngSwitchCase="'table'"
          [rowsCount]="tableRows"
          [columnsCount]="tableColumns"
          [showAvatars]="tableShowAvatars"
        ></app-table-skeleton>

        <!-- Liste -->
        <app-list-skeleton 
          *ngSwitchCase="'list'"
          [itemsCount]="listItems"
          [showAvatar]="listShowAvatar"
          [showDescription]="listShowDescription"
          [showMetadata]="listShowMetadata"
        ></app-list-skeleton>

        <!-- Grille de cartes -->
        <div *ngSwitchCase="'cards'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <app-card-skeleton 
            *ngFor="let card of cards"
            [showImage]="cardShowImage"
            [showStats]="cardShowStats"
          ></app-card-skeleton>
        </div>

        <!-- Contenu mixte -->
        <div *ngSwitchDefault class="space-y-6">
          <app-card-skeleton></app-card-skeleton>
          <app-table-skeleton [rowsCount]="3"></app-table-skeleton>
        </div>
      </div>
    </div>
  `
})
export class PageSkeletonComponent {
  @Input() showPageHeader: boolean = true;
  @Input() showBreadcrumb: boolean = false;
  @Input() showStats: boolean = false;
  @Input() showFilters: boolean = false;
  @Input() contentType: 'table' | 'list' | 'cards' | 'mixed' = 'mixed';
  
  // Configuration pour les tableaux
  @Input() tableRows: number = 5;
  @Input() tableColumns: number = 4;
  @Input() tableShowAvatars: boolean = false;
  
  // Configuration pour les listes
  @Input() listItems: number = 5;
  @Input() listShowAvatar: boolean = true;
  @Input() listShowDescription: boolean = true;
  @Input() listShowMetadata: boolean = false;
  
  // Configuration pour les cartes
  @Input() cardsCount: number = 6;
  @Input() cardShowImage: boolean = false;
  @Input() cardShowStats: boolean = false;

  get breadcrumbs() {
    return [
      { width: '60px' },
      { width: '80px' },
      { width: '100px' }
    ];
  }

  get stats() {
    return [
      { id: 1 },
      { id: 2 },
      { id: 3 },
      { id: 4 }
    ];
  }

  get cards() {
    const cards = [];
    for (let i = 0; i < this.cardsCount; i++) {
      cards.push({ id: i });
    }
    return cards;
  }
}
