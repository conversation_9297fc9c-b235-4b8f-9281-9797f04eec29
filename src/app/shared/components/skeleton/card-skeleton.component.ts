import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SkeletonComponent } from './skeleton.component';

@Component({
  selector: 'app-card-skeleton',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  template: `
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 space-y-4">
      <!-- Header avec avatar et titre -->
      <div *ngIf="showHeader" class="flex items-center space-x-3">
        <app-skeleton 
          variant="circular" 
          width="40px" 
          height="40px"
        ></app-skeleton>
        <div class="flex-1 space-y-2">
          <app-skeleton 
            variant="text" 
            width="60%" 
            height="16px"
          ></app-skeleton>
          <app-skeleton 
            variant="text" 
            width="40%" 
            height="12px"
          ></app-skeleton>
        </div>
      </div>

      <!-- Image principale -->
      <app-skeleton 
        *ngIf="showImage"
        variant="rounded" 
        width="100%" 
        [height]="imageHeight"
      ></app-skeleton>

      <!-- Titre principal -->
      <div *ngIf="showTitle" class="space-y-2">
        <app-skeleton 
          variant="text" 
          width="80%" 
          height="20px"
        ></app-skeleton>
        <app-skeleton 
          *ngIf="showSubtitle"
          variant="text" 
          width="60%" 
          height="16px"
        ></app-skeleton>
      </div>

      <!-- Contenu / Description -->
      <div *ngIf="showContent" class="space-y-2">
        <app-skeleton 
          *ngFor="let line of contentLines" 
          variant="text" 
          [width]="line.width" 
          height="14px"
        ></app-skeleton>
      </div>

      <!-- Actions / Boutons -->
      <div *ngIf="showActions" class="flex space-x-3 pt-4">
        <app-skeleton 
          *ngFor="let action of actions"
          variant="rounded" 
          [width]="action.width" 
          height="36px"
        ></app-skeleton>
      </div>

      <!-- Stats ou métriques -->
      <div *ngIf="showStats" class="flex justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div *ngFor="let stat of stats" class="text-center">
          <app-skeleton 
            variant="text" 
            width="40px" 
            height="20px"
            className="mx-auto mb-1"
          ></app-skeleton>
          <app-skeleton 
            variant="text" 
            width="60px" 
            height="12px"
            className="mx-auto"
          ></app-skeleton>
        </div>
      </div>
    </div>
  `
})
export class CardSkeletonComponent {
  @Input() showHeader: boolean = true;
  @Input() showImage: boolean = false;
  @Input() showTitle: boolean = true;
  @Input() showSubtitle: boolean = true;
  @Input() showContent: boolean = true;
  @Input() showActions: boolean = true;
  @Input() showStats: boolean = false;
  @Input() imageHeight: string = '200px';
  @Input() contentLinesCount: number = 3;
  @Input() actionsCount: number = 2;
  @Input() statsCount: number = 3;

  get contentLines() {
    const lines = [];
    for (let i = 0; i < this.contentLinesCount; i++) {
      const widths = ['100%', '95%', '85%', '90%', '75%'];
      lines.push({
        width: widths[i % widths.length]
      });
    }
    return lines;
  }

  get actions() {
    const actions = [];
    for (let i = 0; i < this.actionsCount; i++) {
      actions.push({
        width: i === 0 ? '100px' : '80px'
      });
    }
    return actions;
  }

  get stats() {
    const stats = [];
    for (let i = 0; i < this.statsCount; i++) {
      stats.push({ id: i });
    }
    return stats;
  }
}
