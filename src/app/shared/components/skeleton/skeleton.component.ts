import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-skeleton',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%] animate-shimmer"
      [ngClass]="getSkeletonClasses()"
      [style.width]="width"
      [style.height]="height"
    ></div>
  `,
  styles: [`
    @keyframes shimmer {
      0% {
        background-position: -200% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }
    
    .animate-shimmer {
      animation: shimmer 2s ease-in-out infinite;
    }
  `]
})
export class SkeletonComponent {
  @Input() width: string = '100%';
  @Input() height: string = '1rem';
  @Input() variant: 'text' | 'circular' | 'rectangular' | 'rounded' = 'rectangular';
  @Input() className: string = '';

  getSkeletonClasses(): string {
    let classes = this.className;
    
    switch (this.variant) {
      case 'text':
        classes += ' rounded';
        break;
      case 'circular':
        classes += ' rounded-full';
        break;
      case 'rectangular':
        classes += ' rounded-none';
        break;
      case 'rounded':
        classes += ' rounded-lg';
        break;
    }
    
    return classes;
  }
}
