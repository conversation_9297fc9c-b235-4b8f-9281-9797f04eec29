import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  trigger, 
  state, 
  style, 
  transition, 
  animate,
  query,
  stagger
} from '@angular/animations';
import { ToastService, Toast } from '../../services/toast.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-toast-container',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="fixed top-4 right-4 z-[10000] space-y-3 max-w-sm w-full">
      <div
        *ngFor="let toast of toasts; trackBy: trackByToast"
        [@slideIn]
        class="toast-item"
        [ngClass]="getToastClasses(toast)"
        (click)="removeToast(toast.id)"
      >
        <!-- Icône -->
        <div class="flex-shrink-0">
          <div [ngClass]="getIconClasses(toast)">
            <svg *ngIf="toast.type === 'success'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <svg *ngIf="toast.type === 'error'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
            <svg *ngIf="toast.type === 'warning'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <svg *ngIf="toast.type === 'info'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>

        <!-- Contenu -->
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium" [ngClass]="getTitleClasses(toast)">
            {{ toast.title }}
          </p>
          <p *ngIf="toast.message" class="text-sm mt-1" [ngClass]="getMessageClasses(toast)">
            {{ toast.message }}
          </p>
        </div>

        <!-- Bouton fermer -->
        <div class="flex-shrink-0 ml-4">
          <button
            type="button"
            class="inline-flex rounded-md p-1.5 transition-colors duration-200"
            [ngClass]="getCloseButtonClasses(toast)"
            (click)="removeToast(toast.id); $event.stopPropagation()"
          >
            <span class="sr-only">Fermer</span>
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>

        <!-- Barre de progression pour les toasts temporaires -->
        <div 
          *ngIf="toast.duration && toast.duration > 0 && !toast.persistent"
          class="absolute bottom-0 left-0 h-1 bg-current opacity-30 animate-progress"
          [style.animation-duration.ms]="toast.duration"
        ></div>
      </div>
    </div>
  `,
  styles: [`
    .toast-item {
      @apply relative flex items-start p-4 rounded-lg shadow-lg backdrop-blur-md border cursor-pointer;
      @apply transition-all duration-300 ease-in-out;
      @apply hover:shadow-xl hover:scale-[1.02];
    }

    @keyframes progress {
      from { width: 100%; }
      to { width: 0%; }
    }

    .animate-progress {
      animation: progress linear;
    }

    /* Classes pour les différents types de toast */
    .toast-success {
      @apply bg-green-50/90 border-green-200/50 dark:bg-green-900/20 dark:border-green-700/50;
    }

    .toast-error {
      @apply bg-red-50/90 border-red-200/50 dark:bg-red-900/20 dark:border-red-700/50;
    }

    .toast-warning {
      @apply bg-yellow-50/90 border-yellow-200/50 dark:bg-yellow-900/20 dark:border-yellow-700/50;
    }

    .toast-info {
      @apply bg-blue-50/90 border-blue-200/50 dark:bg-blue-900/20 dark:border-blue-700/50;
    }
  `],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ 
          transform: 'translateX(100%)', 
          opacity: 0,
          scale: 0.8
        }),
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)', 
          style({ 
            transform: 'translateX(0)', 
            opacity: 1,
            scale: 1
          })
        )
      ]),
      transition(':leave', [
        animate('200ms cubic-bezier(0.4, 0, 1, 1)', 
          style({ 
            transform: 'translateX(100%)', 
            opacity: 0,
            scale: 0.8
          })
        )
      ])
    ])
  ]
})
export class ToastComponent implements OnInit, OnDestroy {
  toasts: Toast[] = [];
  private subscription: Subscription = new Subscription();

  constructor(private toastService: ToastService) {}

  ngOnInit(): void {
    this.subscription.add(
      this.toastService.toasts$.subscribe(toasts => {
        this.toasts = toasts;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  removeToast(id: string): void {
    this.toastService.remove(id);
  }

  trackByToast(index: number, toast: Toast): string {
    return toast.id;
  }

  getToastClasses(toast: Toast): string {
    const baseClasses = 'toast-item';
    return `${baseClasses} toast-${toast.type}`;
  }

  getIconClasses(toast: Toast): string {
    const baseClasses = 'flex items-center justify-center w-8 h-8 rounded-full';
    
    switch (toast.type) {
      case 'success':
        return `${baseClasses} bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300`;
      case 'error':
        return `${baseClasses} bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-300`;
      case 'warning':
        return `${baseClasses} bg-yellow-100 text-yellow-600 dark:bg-yellow-800 dark:text-yellow-300`;
      case 'info':
        return `${baseClasses} bg-blue-100 text-blue-600 dark:bg-blue-800 dark:text-blue-300`;
      default:
        return baseClasses;
    }
  }

  getTitleClasses(toast: Toast): string {
    switch (toast.type) {
      case 'success':
        return 'text-green-800 dark:text-green-200';
      case 'error':
        return 'text-red-800 dark:text-red-200';
      case 'warning':
        return 'text-yellow-800 dark:text-yellow-200';
      case 'info':
        return 'text-blue-800 dark:text-blue-200';
      default:
        return 'text-gray-800 dark:text-gray-200';
    }
  }

  getMessageClasses(toast: Toast): string {
    switch (toast.type) {
      case 'success':
        return 'text-green-700 dark:text-green-300';
      case 'error':
        return 'text-red-700 dark:text-red-300';
      case 'warning':
        return 'text-yellow-700 dark:text-yellow-300';
      case 'info':
        return 'text-blue-700 dark:text-blue-300';
      default:
        return 'text-gray-700 dark:text-gray-300';
    }
  }

  getCloseButtonClasses(toast: Toast): string {
    const baseClasses = 'hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (toast.type) {
      case 'success':
        return `${baseClasses} text-green-500 hover:bg-green-500 focus:ring-green-500 dark:text-green-400`;
      case 'error':
        return `${baseClasses} text-red-500 hover:bg-red-500 focus:ring-red-500 dark:text-red-400`;
      case 'warning':
        return `${baseClasses} text-yellow-500 hover:bg-yellow-500 focus:ring-yellow-500 dark:text-yellow-400`;
      case 'info':
        return `${baseClasses} text-blue-500 hover:bg-blue-500 focus:ring-blue-500 dark:text-blue-400`;
      default:
        return `${baseClasses} text-gray-500 hover:bg-gray-500 focus:ring-gray-500`;
    }
  }
}
