import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastService } from '../../services/toast.service';
import { SkeletonService } from '../../services/skeleton.service';
import { PageSkeletonComponent } from '../skeleton/page-skeleton.component';
import { CardSkeletonComponent } from '../skeleton/card-skeleton.component';
import { TableSkeletonComponent } from '../skeleton/table-skeleton.component';
import { ListSkeletonComponent } from '../skeleton/list-skeleton.component';

@Component({
  selector: 'app-toast-skeleton-demo',
  standalone: true,
  imports: [
    CommonModule,
    PageSkeletonComponent,
    CardSkeletonComponent,
    TableSkeletonComponent,
    ListSkeletonComponent
  ],
  template: `
    <div class="p-6 space-y-8">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Démonstration Toast & Squelettes
        </h2>

        <!-- Section Toast -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Tests des Toasts
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              (click)="showSuccessToast()"
              class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Succès
            </button>
            <button
              (click)="showErrorToast()"
              class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Erreur
            </button>
            <button
              (click)="showWarningToast()"
              class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
            >
              Avertissement
            </button>
            <button
              (click)="showInfoToast()"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Information
            </button>
          </div>
          <div class="mt-4 flex space-x-4">
            <button
              (click)="simulateHttpError()"
              class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Simuler erreur HTTP
            </button>
            <button
              (click)="clearAllToasts()"
              class="px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors"
            >
              Effacer tous
            </button>
          </div>
        </div>

        <!-- Section Squelettes -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Tests des Squelettes
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <button
              (click)="togglePageSkeleton()"
              class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Page complète
            </button>
            <button
              (click)="toggleTableSkeleton()"
              class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Tableau
            </button>
            <button
              (click)="toggleCardSkeleton()"
              class="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
            >
              Cartes
            </button>
            <button
              (click)="toggleListSkeleton()"
              class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              Liste
            </button>
          </div>
          <button
            (click)="simulateDataLoading()"
            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all"
          >
            Simuler chargement de données (3s)
          </button>
        </div>

        <!-- Exemples de squelettes -->
        <div class="space-y-8">
          <!-- Page Skeleton -->
          <div *ngIf="skeletonService.isVisibleSync('page')" class="border-2 border-dashed border-purple-300 rounded-lg p-4">
            <h4 class="text-sm font-medium text-purple-600 mb-4">Squelette de page complète</h4>
            <app-page-skeleton
              [showStats]="true"
              [showFilters]="true"
              contentType="mixed"
            ></app-page-skeleton>
          </div>

          <!-- Table Skeleton -->
          <div *ngIf="skeletonService.isVisibleSync('table')" class="border-2 border-dashed border-indigo-300 rounded-lg p-4">
            <h4 class="text-sm font-medium text-indigo-600 mb-4">Squelette de tableau</h4>
            <app-table-skeleton
              [rowsCount]="6"
              [columnsCount]="5"
              [showAvatars]="true"
            ></app-table-skeleton>
          </div>

          <!-- Card Skeleton -->
          <div *ngIf="skeletonService.isVisibleSync('cards')" class="border-2 border-dashed border-pink-300 rounded-lg p-4">
            <h4 class="text-sm font-medium text-pink-600 mb-4">Squelettes de cartes</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <app-card-skeleton
                *ngFor="let i of [1,2,3,4,5,6]"
                [showImage]="true"
                [showStats]="true"
              ></app-card-skeleton>
            </div>
          </div>

          <!-- List Skeleton -->
          <div *ngIf="skeletonService.isVisibleSync('list')" class="border-2 border-dashed border-teal-300 rounded-lg p-4">
            <h4 class="text-sm font-medium text-teal-600 mb-4">Squelette de liste</h4>
            <app-list-skeleton
              [itemsCount]="8"
              [showMetadata]="true"
              [showBadge]="true"
            ></app-list-skeleton>
          </div>
        </div>
      </div>
    </div>
  `
})
export class ToastSkeletonDemoComponent {
  
  constructor(
    private toastService: ToastService,
    public skeletonService: SkeletonService
  ) {}

  // Méthodes pour les toasts
  showSuccessToast(): void {
    this.toastService.success(
      'Opération réussie !',
      'Vos données ont été sauvegardées avec succès.'
    );
  }

  showErrorToast(): void {
    this.toastService.error(
      'Erreur de validation',
      'Veuillez vérifier les champs obligatoires.'
    );
  }

  showWarningToast(): void {
    this.toastService.warning(
      'Attention',
      'Cette action ne peut pas être annulée.'
    );
  }

  showInfoToast(): void {
    this.toastService.info(
      'Information',
      'Une nouvelle mise à jour est disponible.'
    );
  }

  simulateHttpError(): void {
    // Simuler une erreur HTTP
    const mockError = {
      status: 422,
      error: {
        message: 'Les données fournies ne sont pas valides'
      }
    };
    this.toastService.handleHttpError(mockError);
  }

  clearAllToasts(): void {
    this.toastService.clear();
  }

  // Méthodes pour les squelettes
  togglePageSkeleton(): void {
    const isVisible = this.skeletonService.isVisibleSync('page');
    if (isVisible) {
      this.skeletonService.hide('page');
    } else {
      this.skeletonService.show('page');
    }
  }

  toggleTableSkeleton(): void {
    const isVisible = this.skeletonService.isVisibleSync('table');
    if (isVisible) {
      this.skeletonService.hide('table');
    } else {
      this.skeletonService.show('table');
    }
  }

  toggleCardSkeleton(): void {
    const isVisible = this.skeletonService.isVisibleSync('cards');
    if (isVisible) {
      this.skeletonService.hide('cards');
    } else {
      this.skeletonService.show('cards');
    }
  }

  toggleListSkeleton(): void {
    const isVisible = this.skeletonService.isVisibleSync('list');
    if (isVisible) {
      this.skeletonService.hide('list');
    } else {
      this.skeletonService.show('list');
    }
  }

  async simulateDataLoading(): Promise<void> {
    // Afficher tous les squelettes
    this.skeletonService.showMultiple(['page', 'table', 'cards', 'list']);
    
    // Simuler un chargement de 3 secondes
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Masquer tous les squelettes
    this.skeletonService.hideMultiple(['page', 'table', 'cards', 'list']);
    
    // Afficher un toast de succès
    this.toastService.success(
      'Données chargées !',
      'Toutes les données ont été chargées avec succès.'
    );
  }
}
