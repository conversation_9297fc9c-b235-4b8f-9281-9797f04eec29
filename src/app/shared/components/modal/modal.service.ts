// shared/modal.service.ts
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(private dialog: MatDialog) {}

  open(component: any, data?: any, options?: any): Observable<any> {
    const dialogRef = this.dialog.open(component, {
      width: '600px',
      maxHeight: '90vh',
      data,
      ...options,
    });
    return dialogRef.afterClosed();
  }
}
