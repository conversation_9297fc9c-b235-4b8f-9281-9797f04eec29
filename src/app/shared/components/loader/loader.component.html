<div
      *ngIf="visible"
      [@fadeAnimation]="'visible'"
      (@fadeAnimation.done)="animationDone($event)"
      class="fixed inset-0 flex items-center justify-center z-[9999]"
      [class.backdrop-blur-md]="overlay"
      [class.bg-white/80]="overlay && !isDark"
      [class.bg-gray-900/80]="overlay && isDark"
      (click)="preventBackgroundActions($event)"
    >
      <div
        class="flex flex-col items-center gap-4 p-8 rounded-xl bg-white dark:bg-gray-800 shadow-2xl border border-gray-100 dark:border-gray-700"
      >
        <!-- Loader avec animation améliorée -->
        <div class="relative" [style.width.px]="size" [style.height.px]="size">
          <!-- Cercle de base -->
          <div 
            class="absolute inset-0 rounded-full border-4 border-gray-200 dark:border-gray-700"
          ></div>
          <!-- Spinner -->
          <div 
            class="absolute inset-0 rounded-full border-4 border-transparent border-t-primary-500 border-b-primary-500 animate-spin"
          ></div>
          <!-- Effet de lueur autour du spinner -->
          <div
            *ngIf="!isDark"
            class="absolute inset-0 rounded-full border-4 border-transparent border-t-primary-300/50 border-b-primary-300/50 animate-pulse"
            [style.transform]="'scale(1.1)'"
          ></div>
        </div>
        
        <!-- Message avec animation de chargement -->
        <div *ngIf="message" class="text-center">
          <p class="text-gray-700 dark:text-gray-300 font-medium">
            {{ message }}
            <span class="inline-flex w-12">
              <span class="animate-loadingDots">.</span>
              <span class="animate-loadingDots animation-delay-300">.</span>
              <span class="animate-loadingDots animation-delay-600">.</span>
            </span>
          </p>
          <p *ngIf="subMessage" class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {{ subMessage }}
          </p>
        </div>
      </div>
    </div>