import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class LoaderService {
  private isLoading = new BehaviorSubject<boolean>(false);
  private loadingMessage = new BehaviorSubject<string>(
    'Chargement en cours...'
  );

  public isLoading$ = this.isLoading.asObservable();
  public loadingMessage$ = this.loadingMessage.asObservable();

  show(message?: string): void {
    message && this.loadingMessage.next(message);
    this.isLoading.next(true);
  }

  hide(): void {
    this.isLoading.next(false);
  }

  setMessage(message: string): void {
    this.loadingMessage.next(message);
  }
}
