import { Component, Input, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AnimationEvent } from '@angular/animations';
import {
  trigger,
  state,
  style,
  animate,
  transition,
} from '@angular/animations';

@Component({
  selector: 'app-loader',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div
      *ngIf="visible"
      [@fadeAnimation]="'visible'"
      (@fadeAnimation.done)="animationDone($event)"
      class="fixed inset-0 flex items-center justify-center z-[9999]"
      [class.backdrop-blur-md]="overlay"
      [ngClass]="{
        'bg-gray-300/10': overlay && !isDark,
        'bg-gray-900/10': overlay && isDark
      }"
      (click)="preventBackgroundActions($event)"
    >
      <div
        class="flex flex-col items-center gap-4 p-8 rounded-xl bg-white dark:bg-gray-800 shadow-2xl border border-gray-100 dark:border-gray-700"
      >
        <!-- <PERSON>ader avec animation améliorée -->
        <div class="relative" [style.width.px]="size" [style.height.px]="size">
          <!-- Cercle de base -->
          <div
            class="absolute inset-0 rounded-full border-4 border-gray-200 dark:border-gray-700"
          ></div>
          <!-- Spinner -->
          <div
            class="absolute inset-0 rounded-full border-4 border-transparent border-t-primary-500 border-b-primary-500 animate-spin"
          ></div>
          <!-- Effet de lueur autour du spinner -->
          <div
            *ngIf="!isDark"
            class="absolute inset-0 rounded-full border-4 border-transparent border-t-primary-300 border-b-primary-300 opacity-50 animate-pulse"
            [style.transform]="'scale(1.1)'"
          ></div>
        </div>

        <!-- Message avec animation de chargement -->
        <div *ngIf="message" class="text-center">
          <p class="text-gray-700 dark:text-gray-300 font-medium">
            {{ message }}
            <span class="inline-flex w-12">
              <span class="animate-loadingDots">.</span>
              <span class="animate-loadingDots animation-delay-300">.</span>
              <span class="animate-loadingDots animation-delay-600">.</span>
            </span>
          </p>
          <p
            *ngIf="subMessage"
            class="text-sm text-gray-500 dark:text-gray-400 mt-1"
          >
            {{ subMessage }}
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      @keyframes loadingDots {
        0%,
        80%,
        100% {
          opacity: 0;
        }
        40% {
          opacity: 1;
        }
      }
      .animate-loadingDots {
        animation: loadingDots 1.4s infinite ease-in-out both;
      }
      .animation-delay-300 {
        animation-delay: 0.3s;
      }
      .animation-delay-600 {
        animation-delay: 0.6s;
      }
    `,
  ],
  animations: [
    trigger('fadeAnimation', [
      state('visible', style({ opacity: 1 })),
      transition(':enter', [
        style({ opacity: 0 }),
        animate('200ms ease-out', style({ opacity: 1 })),
      ]),
      transition(':leave', [animate('200ms ease-in', style({ opacity: 0 }))]),
    ]),
  ],
})
export class LoaderComponent {
  @Input() size: number = 60;
  @Input() message: string = 'Chargement en cours';
  @Input() subMessage: string = '';
  @Input() overlay: boolean = true;
  @Input() isDark: boolean = false;
  @Input() blockNavigation: boolean = true;

  visible: boolean = true;
  private animationInProgress: boolean = false;

  constructor() {
    // Intercepter les événements de navigation si blockNavigation est activé
    if (this.blockNavigation) {
      history.pushState(null, document.title, location.href);
    }
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event: Event) {
    if (this.blockNavigation && this.visible) {
      // Empêcher la navigation arrière
      history.pushState(null, document.title, location.href);
      // Optionnel: afficher une notification
      console.log('Navigation arrière bloquée pendant le chargement');
      event.preventDefault();
      return false;
    }
    return true;
  }

  // Empêcher les clics sur l'arrière-plan
  preventBackgroundActions(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  // Méthode pour masquer le loader avec animation
  hide(): void {
    if (!this.animationInProgress) {
      this.visible = false;
    }
  }

  // Méthode pour afficher le loader avec animation
  show(): void {
    this.visible = true;
  }

  // Gérer la fin de l'animation
  animationDone(event: AnimationEvent): void {
    this.animationInProgress = false;
  }

  // Nettoyage lors de la destruction du composant
  ngOnDestroy(): void {
    // Permettre la navigation arrière lorsque le composant est détruit
    if (this.blockNavigation) {
      window.removeEventListener('popstate', this.onPopState.bind(this));
    }
  }
}
