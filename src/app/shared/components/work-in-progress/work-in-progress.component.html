<div
  class="flex min-h-[400px] flex-col items-center justify-center rounded-xl bg-white p-8 dark:bg-gray-800"
>
  <!-- Icône animée -->
  <div class="mb-8">
    <div class="relative">
      <div
        class="h-32 w-32 rounded-full bg-gradient-to-br from-blue-50 to-blue-100 p-4 dark:from-blue-900/30 dark:to-blue-800/30"
      >
        <svg
          class="h-full w-full animate-spin text-blue-500 dark:text-blue-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
      <div
        class="absolute -right-2 -top-2 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200 text-yellow-600 shadow-lg dark:from-yellow-900/30 dark:to-yellow-800/30 dark:text-yellow-400"
      >
        <svg
          class="h-6 w-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
      </div>
    </div>
  </div>

  <!-- Titre -->
  <h2 class="mb-3 text-3xl font-bold text-gray-800 dark:text-white">
    {{ title }}
  </h2>

  <!-- Description -->
  <p class="mb-8 max-w-md text-center text-lg text-gray-600 dark:text-gray-400">
    {{ description }}
  </p>

  <!-- Barre de progression -->
  <div class="mb-8 w-full max-w-md">
    <div class="mb-2 flex justify-between text-sm">
      <span class="text-gray-600 dark:text-gray-400">Progression</span>
      <span class="font-medium text-blue-600 dark:text-blue-400">75%</span>
    </div>
    <div
      class="h-2.5 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"
    >
      <div
        class="h-full animate-pulse rounded-full bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500"
        style="width: 75%"
      ></div>
    </div>
  </div>

  <!-- Message d'encouragement -->
  <div
    class="rounded-xl bg-gradient-to-r from-blue-50 to-blue-100 p-6 text-center shadow-sm dark:from-blue-900/30 dark:to-blue-800/30"
  >
    <p class="text-base font-medium text-blue-600 dark:text-blue-400">
      Notre équipe travaille dur pour vous offrir la meilleure expérience
      possible 🚀
    </p>
    <p class="mt-2 text-sm text-blue-500 dark:text-blue-300">
      Restez à l'écoute pour les mises à jour !
    </p>
  </div>
</div>
