# Guide d'utilisation - Toasts et Squelettes

## 🎯 Vue d'ensemble

Ce guide explique comment utiliser le nouveau système de toasts élégants et les composants squelettes pour remplacer le loader global lors de la navigation.

## 📢 Service de Toast

### Importation et injection

```typescript
import { ToastService } from '../shared/services/toast.service';

constructor(private toastService: ToastService) {}
```

### Types de toasts disponibles

#### 1. To<PERSON> de succès
```typescript
this.toastService.success(
  'Opération réussie !',
  'Vos données ont été sauvegardées avec succès.'
);
```

#### 2. Toast d'erreur
```typescript
this.toastService.error(
  'Erreur de validation',
  'Veuillez vérifier les champs obligatoires.',
  true // persistent (optionnel)
);
```

#### 3. Toast d'avertissement
```typescript
this.toastService.warning(
  'Attention',
  'Cette action ne peut pas être annulée.'
);
```

#### 4. Toast d'information
```typescript
this.toastService.info(
  'Information',
  'Une nouvelle mise à jour est disponible.'
);
```

### Gestion automatique des erreurs HTTP

Le service gère automatiquement les erreurs HTTP via l'intercepteur :

```typescript
// L'erreur sera automatiquement affichée en toast
this.httpClient.get('/api/data').subscribe({
  next: (data) => console.log(data),
  // Pas besoin de gérer l'erreur, elle sera automatiquement affichée
});

// Pour désactiver l'affichage automatique sur une requête spécifique
const headers = new HttpHeaders().set('X-Skip-Error-Toast', 'true');
this.httpClient.get('/api/data', { headers }).subscribe(...);
```

### Méthodes utilitaires

```typescript
// Effacer tous les toasts
this.toastService.clear();

// Effacer les toasts d'un type spécifique
this.toastService.clearByType('error');

// Supprimer un toast spécifique
this.toastService.remove(toastId);
```

## 🦴 Composants Squelettes

### Service Skeleton

```typescript
import { SkeletonService } from '../shared/services/skeleton.service';

constructor(public skeletonService: SkeletonService) {}
```

### Utilisation basique

```typescript
// Afficher un squelette
this.skeletonService.show('my-component');

// Masquer un squelette
this.skeletonService.hide('my-component');

// Vérifier si un squelette est visible
const isVisible = this.skeletonService.isVisibleSync('my-component');
```

### Gestion avec des opérations asynchrones

```typescript
// Avec une Promise
async loadData() {
  await this.skeletonService.withSkeleton('data-loading', async () => {
    const data = await this.dataService.getData().toPromise();
    this.data = data;
  });
}

// Avec un Observable
loadDataObservable() {
  return this.skeletonService.withSkeletonObservable('data-loading', () => {
    return this.dataService.getData();
  });
}
```

### Composants squelettes disponibles

#### 1. Squelette de base
```html
<app-skeleton 
  width="100%" 
  height="20px" 
  variant="text"
></app-skeleton>
```

#### 2. Squelette de carte
```html
<app-card-skeleton
  [showHeader]="true"
  [showImage]="false"
  [showActions]="true"
  [contentLinesCount]="3"
></app-card-skeleton>
```

#### 3. Squelette de tableau
```html
<app-table-skeleton
  [rowsCount]="5"
  [columnsCount]="4"
  [showAvatars]="true"
  [showPagination]="true"
></app-table-skeleton>
```

#### 4. Squelette de liste
```html
<app-list-skeleton
  [itemsCount]="5"
  [showAvatar]="true"
  [showDescription]="true"
  [showMetadata]="false"
></app-list-skeleton>
```

#### 5. Squelette de page complète
```html
<app-page-skeleton
  [showPageHeader]="true"
  [showStats]="true"
  [showFilters]="true"
  contentType="table"
></app-page-skeleton>
```

## 🔄 Migration d'une page existante

### Avant (avec loader global)
```typescript
export class MyComponent implements OnInit {
  isLoading = true;
  
  constructor(private loaderService: LoaderService) {}
  
  ngOnInit() {
    this.loaderService.show('Chargement...');
    this.loadData().finally(() => {
      this.loaderService.hide();
      this.isLoading = false;
    });
  }
}
```

### Après (avec squelettes)
```typescript
export class MyComponent implements OnInit {
  constructor(
    private skeletonService: SkeletonService,
    private toastService: ToastService
  ) {}
  
  ngOnInit() {
    this.loadData();
  }
  
  async loadData() {
    try {
      this.skeletonService.show('my-data');
      const data = await this.dataService.getData().toPromise();
      this.data = data;
      this.toastService.success('Données chargées', 'Mise à jour réussie');
    } catch (error) {
      // L'erreur sera gérée automatiquement par l'intercepteur
    } finally {
      this.skeletonService.hide('my-data');
    }
  }
}
```

### Template correspondant
```html
<div class="container">
  <!-- Squelette pendant le chargement -->
  <app-table-skeleton 
    *ngIf="skeletonService.isVisibleSync('my-data')"
    [rowsCount]="8"
    [columnsCount]="5"
  ></app-table-skeleton>
  
  <!-- Contenu normal -->
  <div *ngIf="!skeletonService.isVisibleSync('my-data')">
    <!-- Votre contenu ici -->
  </div>
</div>
```

## 🎨 Personnalisation

### Couleurs et thèmes
Les toasts s'adaptent automatiquement au thème sombre/clair de l'application.

### Durées personnalisées
```typescript
// Toast avec durée personnalisée (en millisecondes)
this.toastService.success('Titre', 'Message', 10000); // 10 secondes

// Toast persistant (ne disparaît pas automatiquement)
this.toastService.error('Erreur critique', 'Message', true);
```

### Squelettes personnalisés
```html
<app-skeleton 
  width="200px" 
  height="40px" 
  variant="circular"
  className="my-custom-class"
></app-skeleton>
```

## 📱 Responsive et accessibilité

- Les toasts sont positionnés de manière responsive
- Support complet du mode sombre
- Animations fluides et performantes
- Lecteurs d'écran supportés
- Navigation au clavier

## 🚀 Bonnes pratiques

1. **Utilisez des clés descriptives** pour les squelettes : `'user-profile'`, `'data-table'`, etc.
2. **Nettoyez les squelettes** dans `ngOnDestroy()`
3. **Groupez les opérations** avec `showMultiple()` et `hideMultiple()`
4. **Utilisez les toasts avec parcimonie** - évitez le spam
5. **Préférez les squelettes au loader global** pour une meilleure UX
6. **Testez avec des connexions lentes** pour valider l'expérience

## 🔧 Dépannage

### Les toasts ne s'affichent pas
- Vérifiez que `<app-toast-container>` est présent dans `app.component.html`
- Vérifiez l'import du `ToastComponent` dans `app.component.ts`

### Les squelettes ne se masquent pas
- Vérifiez que `hide()` est appelé dans le `finally` ou `ngOnDestroy()`
- Utilisez `hideAll()` en cas de doute

### Erreurs HTTP non affichées
- Vérifiez que l'intercepteur `ErrorToastInterceptor` est configuré
- Vérifiez l'ordre des intercepteurs dans `main.ts`
