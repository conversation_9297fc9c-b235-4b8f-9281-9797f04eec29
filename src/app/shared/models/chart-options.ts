import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexTitleSubtitle,
  ApexLegend,
  ApexDataLabels,
  ApexFill,
  ApexStroke,
  ApexYAxis,
  ApexStates,
  ApexTooltip,
  ApexGrid,
  ApexTheme,
  ApexAnnotations,
  ApexResponsive,
  ApexPlotOptions,
  ApexMarkers,
  ApexNonAxisChartSeries,
  ApexForecastDataPoints,
  ApexNoData,
} from 'ng-apexcharts';

export type ChartOptions = {
  series: ApexAxisChartSeries | ApexNonAxisChartSeries;
  chart: ApexChart;
  legend: ApexLegend;
  dataLabels: ApexDataLabels;
  fill: ApexFill;
  stroke: ApexStroke;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis | ApexYAxis[];
  states: ApexStates;
  tooltip: ApexTooltip;
  colors: string[];
  grid: ApexGrid;
  title: ApexTitleSubtitle;
  subtitle: ApexTitleSubtitle;
  theme: ApexTheme;
  annotations: ApexAnnotations;
  responsive: ApexResponsive[];
  plotOptions: ApexPlotOptions;
  markers: ApexMarkers;
  forecastDataPoints?: ApexForecastDataPoints;
  noData?: ApexNoData;
  labels?: string[];
};
