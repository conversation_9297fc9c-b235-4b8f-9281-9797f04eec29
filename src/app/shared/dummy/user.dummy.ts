import { User } from 'src/app/modules/uikit/pages/table/model/user.model';

export const dummyData: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    age: 30,
    username: 'johndo<PERSON>',
    email: '<EMAIL>',
    phone: '******-555-0156',
    website: 'johndoe.com',
    occupation: 'Software Engineer',
    hobbies: ['coding', 'hiking', 'reading'],
    selected: false,
    status: 1,
    created_at: '2024-10-12T12:34:56Z',
  },
  {
    id: 2,
    name: '<PERSON>',
    age: 25,
    username: 'jane<PERSON>',
    email: '<EMAIL>',
    phone: '******-555-0123',
    website: 'janesmith.net',
    occupation: 'Graphic Designer',
    hobbies: ['drawing', 'photography', 'travel'],
    selected: false,
    status: 1,
    created_at: '2024-10-14T12:34:56Z',
  },
  {
    id: 3,
    name: '<PERSON>',
    age: 35,
    username: 'micha<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '******-555-0189',
    website: 'michaelbrown.me',
    occupation: 'Data Scientist',
    hobbies: ['data analysis', 'cycling', 'music'],
    selected: true,
    status: 2,
    created_at: '2024-10-16T12:34:56Z',
  },
  {
    id: 4,
    name: 'Emily White',
    age: 28,
    username: 'emilyw',
    email: '<EMAIL>',
    phone: '******-555-0147',
    website: 'emilywhite.org',
    occupation: 'Marketing Specialist',
    hobbies: ['writing', 'yoga', 'baking'],
    selected: false,
    status: 2,
    created_at: '2024-10-18T12:34:56Z',
  },
  {
    id: 5,
    name: 'David Johnson',
    age: 40,
    username: 'davidj',
    email: '<EMAIL>',
    phone: '******-555-0168',
    website: 'davidjohnson.co',
    occupation: 'Product Manager',
    hobbies: ['innovation', 'gaming', 'finance'],
    selected: true,
    status: 1,
    created_at: '2024-10-20T12:34:56Z',
  },
  {
    id: 6,
    name: 'Sarah Davis',
    age: 32,
    username: 'sarahd',
    email: '<EMAIL>',
    phone: '******-555-0190',
    website: 'sarahdavis.dev',
    occupation: 'UI/UX Designer',
    hobbies: ['design', 'gardening', 'swimming'],
    selected: false,
    status: 1,
    created_at: '2024-10-22T12:34:56Z',
  },
  {
    id: 7,
    name: 'Chris Lee',
    age: 29,
    username: 'chrislee',
    email: '<EMAIL>',
    phone: '******-555-0134',
    website: 'chrislee.io',
    occupation: 'Mobile Developer',
    hobbies: ['app development', 'traveling', 'reading'],
    selected: false,
    status: 1,
    created_at: '2024-10-26T12:34:56Z',
  },
  {
    id: 8,
    name: 'Emma Wilson',
    age: 27,
    username: 'emmawilson',
    email: '<EMAIL>',
    phone: '******-555-0175',
    website: 'emmawilson.tech',
    occupation: 'DevOps Engineer',
    hobbies: ['automation', 'gaming', 'blogging'],
    selected: true,
    status: 3,
    created_at: '2024-10-28T12:34:56Z',
  },
];
