import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { ToastService } from '../services/toast.service';
import { SkeletonService } from '../services/skeleton.service';
import { TableSkeletonComponent } from '../components/skeleton/table-skeleton.component';
import { PageSkeletonComponent } from '../components/skeleton/page-skeleton.component';
import { Employee, PaginatedResult } from 'src/app/core/models/employee.model';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { Company } from 'src/app/core/models/company.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-employees-with-skeleton',
  standalone: true,
  imports: [
    CommonModule,
    TableSkeletonComponent,
    PageSkeletonComponent
  ],
  template: `
    <div class="container lg:max-w-none mx-auto p-4">
      <!-- Header avec squelette -->
      <div *ngIf="skeletonService.isVisibleSync('employees-header')" class="mb-6">
        <app-page-skeleton
          [showPageHeader]="true"
          [showBreadcrumb]="false"
          [showStats]="false"
          [showFilters]="true"
          contentType="table"
        ></app-page-skeleton>
      </div>

      <!-- Header normal -->
      <div *ngIf="!skeletonService.isVisibleSync('employees-header')" class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-2xl text-foreground font-bold">Gestion des employés</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            {{ employees.length }} employé(s) trouvé(s)
          </p>
        </div>
        <div class="flex space-x-3">
          <button
            (click)="refreshEmployees()"
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            [disabled]="isLoading"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Actualiser
          </button>
          <button
            (click)="openCreateModal()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nouvel employé
          </button>
        </div>
      </div>

      <!-- Filtres avec squelette -->
      <div *ngIf="skeletonService.isVisibleSync('employees-filters')" class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-wrap items-center gap-4">
            <div class="animate-pulse bg-gray-200 dark:bg-gray-700 h-10 w-64 rounded-lg"></div>
            <div class="animate-pulse bg-gray-200 dark:bg-gray-700 h-10 w-32 rounded-lg"></div>
            <div class="animate-pulse bg-gray-200 dark:bg-gray-700 h-10 w-24 rounded-lg"></div>
          </div>
        </div>
      </div>

      <!-- Filtres normaux -->
      <div *ngIf="!skeletonService.isVisibleSync('employees-filters')" class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Rechercher un employé..."
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                [(ngModel)]="searchTerm"
                (input)="onSearch()"
              >
            </div>
            <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
              <option value="">Tous les départements</option>
              <option value="it">IT</option>
              <option value="hr">RH</option>
              <option value="finance">Finance</option>
            </select>
            <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
              <option value="">Tous les statuts</option>
              <option value="active">Actif</option>
              <option value="inactive">Inactif</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Tableau avec squelette -->
      <div *ngIf="skeletonService.isVisibleSync('employees-table')">
        <app-table-skeleton
          [rowsCount]="8"
          [columnsCount]="6"
          [showAvatars]="true"
          [showHeader]="true"
          [showPagination]="true"
        ></app-table-skeleton>
      </div>

      <!-- Tableau normal -->
      <div *ngIf="!skeletonService.isVisibleSync('employees-table')" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Employé
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Poste
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Département
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Statut
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Date d'embauche
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr *ngFor="let employee of filteredEmployees" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <img class="h-10 w-10 rounded-full" [src]="employee.avatar || '/assets/avatars/default.png'" [alt]="employee.firstName + ' ' + employee.lastName">
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ employee.firstName }} {{ employee.lastName }}
                      </div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ employee.email }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ employee.position }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ employee.department }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                        [ngClass]="{
                          'bg-green-100 text-green-800': employee.status === 'active',
                          'bg-red-100 text-red-800': employee.status === 'inactive'
                        }">
                    {{ employee.status === 'active' ? 'Actif' : 'Inactif' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ employee.hireDate | date:'dd/MM/yyyy' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-blue-600 hover:text-blue-900 mr-3">Voir</button>
                  <button class="text-green-600 hover:text-green-900 mr-3">Modifier</button>
                  <button class="text-red-600 hover:text-red-900">Supprimer</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Précédent
              </button>
              <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Suivant
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  Affichage de <span class="font-medium">1</span> à <span class="font-medium">{{ employees.length }}</span>
                  sur <span class="font-medium">{{ employees.length }}</span> résultats
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Message si aucun employé -->
      <div *ngIf="!skeletonService.isVisibleSync('employees-table') && employees.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun employé</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Commencez par ajouter un nouvel employé.</p>
        <div class="mt-6">
          <button
            (click)="openCreateModal()"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Nouvel employé
          </button>
        </div>
      </div>
    </div>
  `
})
export class EmployeesWithSkeletonComponent implements OnInit, OnDestroy {
  employees: Employee[] = [];
  filteredEmployees: Employee[] = [];
  searchTerm: string = '';
  isLoading = false;
  company!: Company;
  private subscriptions = new Subscription();

  constructor(
    private employeeService: EmployeeService,
    private toastService: ToastService,
    public skeletonService: SkeletonService,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    // S'abonner aux changements de company
    this.subscriptions.add(
      this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
        if (company) {
          this.company = company;
          this.loadEmployees();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    // Nettoyer les squelettes
    this.skeletonService.hideMultiple(['employees-header', 'employees-filters', 'employees-table']);
  }

  async loadEmployees(): Promise<void> {
    if (!this.company?.id) return;

    try {
      // Afficher les squelettes
      this.skeletonService.showMultiple(['employees-header', 'employees-filters', 'employees-table']);
      this.isLoading = true;

      // Simuler un délai minimum pour voir les squelettes
      await new Promise(resolve => setTimeout(resolve, 500));

      // Charger les données
      const data = await this.employeeService.getEmployees(this.company.id).toPromise();
      
      if (data) {
        this.employees = data.data as Employee[];
        this.filteredEmployees = [...this.employees];
        
        // Afficher un toast de succès
        this.toastService.success(
          'Données chargées',
          `${this.employees.length} employé(s) trouvé(s)`
        );
      }
    } catch (error) {
      // L'erreur sera gérée automatiquement par l'intercepteur
      console.error('Erreur lors du chargement des employés:', error);
    } finally {
      // Masquer les squelettes
      this.skeletonService.hideMultiple(['employees-header', 'employees-filters', 'employees-table']);
      this.isLoading = false;
    }
  }

  async refreshEmployees(): Promise<void> {
    await this.loadEmployees();
  }

  onSearch(): void {
    if (!this.searchTerm.trim()) {
      this.filteredEmployees = [...this.employees];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredEmployees = this.employees.filter(employee =>
      employee.firstName.toLowerCase().includes(term) ||
      employee.lastName.toLowerCase().includes(term) ||
      employee.email.toLowerCase().includes(term) ||
      employee.position?.toLowerCase().includes(term) ||
      employee.department?.toLowerCase().includes(term)
    );
  }

  openCreateModal(): void {
    this.toastService.info(
      'Fonctionnalité en développement',
      'La création d\'employé sera bientôt disponible'
    );
  }
}
