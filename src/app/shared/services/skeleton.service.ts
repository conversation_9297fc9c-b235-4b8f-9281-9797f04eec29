import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface SkeletonState {
  [key: string]: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SkeletonService {
  private skeletonStates = new BehaviorSubject<SkeletonState>({});
  public skeletonStates$ = this.skeletonStates.asObservable();

  constructor() {}

  /**
   * Active le squelette pour une clé donnée
   */
  show(key: string): void {
    const currentStates = this.skeletonStates.value;
    this.skeletonStates.next({
      ...currentStates,
      [key]: true
    });
  }

  /**
   * Désactive le squelette pour une clé donnée
   */
  hide(key: string): void {
    const currentStates = this.skeletonStates.value;
    this.skeletonStates.next({
      ...currentStates,
      [key]: false
    });
  }

  /**
   * Vérifie si un squelette est actif
   */
  isVisible(key: string): Observable<boolean> {
    return new Observable(observer => {
      this.skeletonStates$.subscribe(states => {
        observer.next(!!states[key]);
      });
    });
  }

  /**
   * Obtient l'état d'un squelette de manière synchrone
   */
  isVisibleSync(key: string): boolean {
    return !!this.skeletonStates.value[key];
  }

  /**
   * Active plusieurs squelettes
   */
  showMultiple(keys: string[]): void {
    const currentStates = this.skeletonStates.value;
    const newStates = { ...currentStates };
    
    keys.forEach(key => {
      newStates[key] = true;
    });
    
    this.skeletonStates.next(newStates);
  }

  /**
   * Désactive plusieurs squelettes
   */
  hideMultiple(keys: string[]): void {
    const currentStates = this.skeletonStates.value;
    const newStates = { ...currentStates };
    
    keys.forEach(key => {
      newStates[key] = false;
    });
    
    this.skeletonStates.next(newStates);
  }

  /**
   * Désactive tous les squelettes
   */
  hideAll(): void {
    this.skeletonStates.next({});
  }

  /**
   * Utilitaire pour gérer le cycle de vie d'un squelette avec une promesse
   */
  async withSkeleton<T>(key: string, operation: () => Promise<T>): Promise<T> {
    this.show(key);
    try {
      const result = await operation();
      return result;
    } finally {
      this.hide(key);
    }
  }

  /**
   * Utilitaire pour gérer le cycle de vie d'un squelette avec un Observable
   */
  withSkeletonObservable<T>(key: string, operation: () => Observable<T>): Observable<T> {
    return new Observable(observer => {
      this.show(key);
      
      const subscription = operation().subscribe({
        next: (value) => observer.next(value),
        error: (error) => {
          this.hide(key);
          observer.error(error);
        },
        complete: () => {
          this.hide(key);
          observer.complete();
        }
      });

      return () => {
        this.hide(key);
        subscription.unsubscribe();
      };
    });
  }
}
