import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toasts = new BehaviorSubject<Toast[]>([]);
  public toasts$ = this.toasts.asObservable();

  private defaultDuration = 5000; // 5 secondes

  constructor() {}

  /**
   * Affiche un toast de succès
   */
  success(title: string, message?: string, duration?: number): void {
    this.addToast({
      type: 'success',
      title,
      message,
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Affiche un toast d'erreur
   */
  error(title: string, message?: string, persistent: boolean = false): void {
    this.addToast({
      type: 'error',
      title,
      message,
      duration: persistent ? 0 : this.defaultDuration * 2, // Erreurs durent plus longtemps
      persistent
    });
  }

  /**
   * Affiche un toast d'avertissement
   */
  warning(title: string, message?: string, duration?: number): void {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Affiche un toast d'information
   */
  info(title: string, message?: string, duration?: number): void {
    this.addToast({
      type: 'info',
      title,
      message,
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Gère les erreurs HTTP automatiquement
   */
  handleHttpError(error: any): void {
    let title = 'Erreur';
    let message = 'Une erreur inattendue s\'est produite';

    if (error?.error) {
      if (typeof error.error === 'string') {
        message = error.error;
      } else if (error.error.message) {
        message = error.error.message;
      } else if (error.error.error) {
        message = error.error.error;
      }
    } else if (error?.message) {
      message = error.message;
    }

    // Personnaliser selon le code d'erreur
    switch (error?.status) {
      case 400:
        title = 'Requête invalide';
        break;
      case 401:
        title = 'Non autorisé';
        message = 'Veuillez vous reconnecter';
        break;
      case 403:
        title = 'Accès refusé';
        message = 'Vous n\'avez pas les permissions nécessaires';
        break;
      case 404:
        title = 'Ressource introuvable';
        break;
      case 422:
        title = 'Données invalides';
        break;
      case 500:
        title = 'Erreur serveur';
        message = 'Le serveur a rencontré une erreur';
        break;
      case 0:
        title = 'Erreur de connexion';
        message = 'Impossible de contacter le serveur';
        break;
    }

    this.error(title, message, error?.status === 401);
  }

  /**
   * Supprime un toast spécifique
   */
  remove(id: string): void {
    const currentToasts = this.toasts.value;
    const updatedToasts = currentToasts.filter(toast => toast.id !== id);
    this.toasts.next(updatedToasts);
  }

  /**
   * Supprime tous les toasts
   */
  clear(): void {
    this.toasts.next([]);
  }

  /**
   * Supprime tous les toasts d'un type spécifique
   */
  clearByType(type: Toast['type']): void {
    const currentToasts = this.toasts.value;
    const updatedToasts = currentToasts.filter(toast => toast.type !== type);
    this.toasts.next(updatedToasts);
  }

  private addToast(toastData: Omit<Toast, 'id' | 'timestamp'>): void {
    const toast: Toast = {
      ...toastData,
      id: this.generateId(),
      timestamp: new Date()
    };

    const currentToasts = this.toasts.value;
    
    // Limiter le nombre de toasts affichés (max 5)
    const maxToasts = 5;
    let updatedToasts = [toast, ...currentToasts];
    
    if (updatedToasts.length > maxToasts) {
      updatedToasts = updatedToasts.slice(0, maxToasts);
    }

    this.toasts.next(updatedToasts);

    // Auto-suppression si durée définie
    if (toast.duration && toast.duration > 0) {
      setTimeout(() => {
        this.remove(toast.id);
      }, toast.duration);
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }
}
