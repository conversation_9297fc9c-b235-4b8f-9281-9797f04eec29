import { Component } from '@angular/core';
import { ThemeService } from './core/services/theme.service';
import { CommonModule, NgClass } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { ResponsiveHelperComponent } from './shared/components/responsive-helper/responsive-helper.component';
import { LoaderService } from './shared/components/loader/loade.service';
import { LoaderComponent } from './shared/components/loader/loader.component';
import { ToastComponent } from './shared/components/toast/toast.component';
import { TourStepComponent } from './shared/components/tour/tour-step/tour-step.component';
import { TourWelcomeComponent } from './shared/components/tour/tour-welcome/tour-welcome.component';
import { TourHelpComponent } from './shared/components/tour/tour-help/tour-help.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  imports: [
    RouterOutlet,
    LoaderComponent,
    ToastComponent,
    CommonModule,
    TourStepComponent,
    TourWelcomeComponent,
    TourHelpComponent,
  ],
})
export class AppComponent {
  title = 'lumina-hr-global';
  constructor(
    public themeService: ThemeService,
    public loader: LoaderService
  ) {}
}
