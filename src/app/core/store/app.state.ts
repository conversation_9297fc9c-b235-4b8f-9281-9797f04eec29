import { Company } from '../models/company.model';
import {
  companyFormInitialState,
  CompanyFormState,
} from './company/company-form.state';
import { companyInitialState, CompanyState } from './company/company.state';
import { userInitialState, UserState } from './user/user.state';

export interface AppState {
  companyForm: CompanyFormState;
  company: CompanyState;
  user: UserState;
}

export const loadFromLocalStorage = (): AppState => {
  try {
    const serializedState = localStorage.getItem('appState');
    if (serializedState === null) {
      return {
        companyForm: companyFormInitialState,
        company: companyInitialState,
        user: userInitialState,
      };
    }
    return JSON.parse(serializedState);
  } catch (e) {
    console.error('Could not load state from localStorage', e);
    return {
      companyForm: companyFormInitialState,
      company: companyInitialState,
      user: userInitialState,
    };
  }
};
export const saveToLocalStorage = (state: AppState) => {
  try {
    const serializedState = JSON.stringify(state);
    localStorage.setItem('appState', serializedState);
  } catch (e) {
    console.error('Could not save state to localStorage', e);
  }
};
