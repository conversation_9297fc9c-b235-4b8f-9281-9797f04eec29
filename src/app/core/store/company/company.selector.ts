import { createSelector, createFeatureSelector } from '@ngrx/store';
import { CompanyState } from './company.state';

export const selectCompanyState =
  createFeatureSelector<CompanyState>('company');

export const selectCurrentCompany = createSelector(
  selectCompanyState,
  (state) => state.company
);

export const selectCurrentCompanyId = createSelector(
  selectCurrentCompany,
  (company) => company?.id || null
);
