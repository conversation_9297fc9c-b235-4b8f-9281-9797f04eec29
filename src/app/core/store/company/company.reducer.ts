import { createReducer, on } from '@ngrx/store';
import { loadCompanySuccess, loadCompanyFailure } from './company.actions';
import { loadFromLocalStorage, saveToLocalStorage } from '../app.state';
import { companyInitialState } from './company.state';

// Function to save to localStorage

export const companyReducer = createReducer(
  companyInitialState,

  on(loadCompanySuccess, (state, { company }) => {
    const newState = {
      ...state,
      company: { ...state.company, ...company },
      error: null,
    };
    const globalState = loadFromLocalStorage();
    saveToLocalStorage({ ...globalState, company: newState });
    return newState;
  }),
  on(loadCompanyFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
