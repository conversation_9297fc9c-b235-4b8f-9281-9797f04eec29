import { createReducer, on } from '@ngrx/store';
import { AppState, loadFromLocalStorage } from '../app.state';
import { companyFormInitialState } from './company-form.state';
import { resetForm, updateForm, updateStep } from './company-form.actions';

// Function to save to localStorage
const saveToLocalStorage = (state: AppState) => {
  try {
    const serializedState = JSON.stringify(state);
    localStorage.setItem('appState', serializedState);
  } catch (e) {
    console.error('Could not save state to localStorage', e);
  }
};

export const companyFormReducer = createReducer(
  companyFormInitialState,

  on(updateForm, (state, { form }) => {
    const newState = { ...state, form: { ...state.form, ...form } };
    const globalState = loadFromLocalStorage();
    saveToLocalStorage({ ...globalState, companyForm: newState }); // Save the full state
    return newState;
  }),

  on(updateStep, (state, { step }) => {
    const newState = { ...state, currentStep: step };
    const globalState = loadFromLocalStorage();
    saveToLocalStorage({ ...globalState, companyForm: newState }); // Save the full state
    return newState;
  }),

  on(resetForm, () => {
    const globalState = loadFromLocalStorage();
    const newState = { ...globalState, companyForm: companyFormInitialState };
    saveToLocalStorage(newState); // Save updated state with company reset
    return companyFormInitialState; // Reset only company state
  })
);
