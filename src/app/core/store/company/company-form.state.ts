import {
  Company,
  Address,
  PayrollCycleEnum,
  CompanyTaxSettings,
  PayrollConfiguration,
} from '../../models/company.model';

export interface CompanyFormState {
  form: Omit<
    Company,
    | 'id'
    | 'createdAt'
    | 'updatedAt'
    | 'employees'
    | 'departments'
    | 'documents'
    | 'jobOffers'
  > & {
    taxSettings?: Omit<
      CompanyTaxSettings,
      'id' | 'companyId' | 'createdAt' | 'updatedAt'
    >;
    payrollConfiguration?: Omit<
      PayrollConfiguration,
      'id' | 'companyId' | 'createdAt' | 'updatedAt'
    >;
  };
  currentStep: number;
  isLoading: boolean;
  error: string | null;
}

export const companyFormInitialState: CompanyFormState = {
  form: {
    companyName: '',
    email: '',
    phoneNumbers: [''],
    website: '',
    logo: '',
    officialName: '',
    taxIdentificationNumber: '',
    industry: '',
    description: '',
    address: {
      street: '',
      city: '',
      postalCode: '',
      country: '',
    },
    taxSettings: {
      incomeTaxRate: 0,
      socialSecurityRate: 0,
      unEmploymentInsuranceRate: 0,
      healthInsuranceRate: 0,
      pensionContributionRate: 0,
    },
    payrollConfiguration: {
      payrollCycle: PayrollCycleEnum.MONTHLY,
      paymentDay: 1,
      overtimeMultiplier: 1.5,
    },
  },
  currentStep: 1,
  isLoading: false,
  error: null,
};
