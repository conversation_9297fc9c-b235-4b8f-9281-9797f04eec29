import {
  Company,
  Address,
  PayrollCycleEnum,
  CompanyTaxSettings,
  PayrollConfiguration,
} from '../../models/company.model';

export interface CompanyState {
  company: Omit<
    Company,
    'employees' | 'departments' | 'documents' | 'jobOffers'
  > & {
    address?: Address;
    taxSettings?: Omit<CompanyTaxSettings, 'companyId'>;
    payrollConfiguration?: Omit<PayrollConfiguration, 'companyId'>;
  };
  isLoading: boolean;
  error: string | null;
  lastUpdated?: string;
}

export const companyInitialState: CompanyState = {
  company: {
    id: '',
    companyName: '',
    email: '',
    phoneNumbers: [],
    website: '',
    logo: '',
    officialName: '',
    taxIdentificationNumber: '',
    industry: '',
    description: '',
    address: {
      id: '',
      street: '',
      city: '',
      postalCode: '',
      country: '',
    },
    taxSettings: {
      id: '',
      incomeTaxRate: 0,
      socialSecurityRate: 0,
      unEmploymentInsuranceRate: 0,
      healthInsuranceRate: 0,
      pensionContributionRate: 0,
    },
    payrollConfiguration: {
      id: '',
      payrollCycle: PayrollCycleEnum.MONTHLY,
      paymentDay: 1,
      overtimeMultiplier: 1.5,
    },
    createdAt: '',
    updatedAt: '',
  },
  isLoading: false,
  error: null,
  lastUpdated: undefined,
};
