import { createReducer, on } from '@ngrx/store';
import { loadUserSuccess, loadUserFailure, loadUser } from './user.actions';
import { loadFromLocalStorage, saveToLocalStorage } from '../app.state';
import { userInitialState } from './user.state';

export const userReducer = createReducer(
  userInitialState,

  on(loadUser, (state) => ({
    ...state,
    isLoading: true,
    error: null,
  })),

  on(loadUserSuccess, (state, { user }) => {
    const newState = {
      ...state,
      user,
      isAuthenticated: true,
      isLoading: false,
      error: null,
    };
    const globalState = loadFromLocalStorage();
    saveToLocalStorage({ ...globalState, user: newState });
    return newState;
  }),

  on(loadUserFailure, (state, { error }) => ({
    ...state,
    isAuthenticated: false,
    isLoading: false,
    error,
  }))
);
