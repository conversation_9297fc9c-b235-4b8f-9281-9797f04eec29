import { ActionReducerMap } from '@ngrx/store';
import { AppState } from './app.state';
import { companyReducer } from './company/company.reducer';
import { userReducer } from './user/user.reducer';
import { companyFormReducer } from './company/company-form.reducer';

export const reducers: ActionReducerMap<AppState> = {
  companyForm: companyFormReducer,
  company: companyReducer,
  user: userReducer,
};
