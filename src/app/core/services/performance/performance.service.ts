import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export enum PerformanceReviewStatus {
  DRAFT = 'DRAFT',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export enum PerformanceReviewType {
  ANNUAL = 'ANNUAL',
  QUARTERLY = 'QUARTERLY',
  MONTHLY = 'MONTHLY',
  PROJECT_BASED = 'PROJECT_BASED',
  PROBATION = 'PROBATION',
  PROMOTION = 'PROMOTION',
}

export enum GoalStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
}

export enum GoalPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface PerformanceReview {
  id: string;
  title: string;
  description?: string;
  reviewType: PerformanceReviewType;
  status: PerformanceReviewStatus;
  reviewPeriodStart: string;
  reviewPeriodEnd: string;
  dueDate: string;
  employeeId: string;
  reviewerId: string;
  selfAssessmentScore?: number;
  managerAssessmentScore?: number;
  finalScore?: number;
  strengths?: string[];
  areasForImprovement?: string[];
  goals?: string[];
  comments?: string;
  employee?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  reviewer?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface Goal {
  id: string;
  title: string;
  description?: string;
  status: GoalStatus;
  priority: GoalPriority;
  targetDate: string;
  completedDate?: string;
  progress: number;
  employeeId: string;
  assignedById: string;
  category?: string;
  metrics?: string[];
  employee?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  assignedBy?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreatePerformanceReviewDto {
  title: string;
  description?: string;
  reviewType: PerformanceReviewType;
  reviewPeriodStart: string;
  reviewPeriodEnd: string;
  dueDate: string;
  employeeId: string;
  reviewerId: string;
}

export interface UpdatePerformanceReviewDto {
  title?: string;
  description?: string;
  reviewType?: PerformanceReviewType;
  status?: PerformanceReviewStatus;
  dueDate?: string;
  selfAssessmentScore?: number;
  managerAssessmentScore?: number;
  finalScore?: number;
  strengths?: string[];
  areasForImprovement?: string[];
  goals?: string[];
  comments?: string;
}

export interface CreateGoalDto {
  title: string;
  description?: string;
  priority: GoalPriority;
  targetDate: string;
  employeeId: string;
  assignedById: string;
  category?: string;
  metrics?: string[];
}

export interface UpdateGoalDto {
  title?: string;
  description?: string;
  status?: GoalStatus;
  priority?: GoalPriority;
  targetDate?: string;
  progress?: number;
  category?: string;
  metrics?: string[];
}

export interface PerformanceQueryOptions {
  page?: number;
  limit?: number;
  employeeId?: string;
  reviewerId?: string;
  status?: PerformanceReviewStatus;
  reviewType?: PerformanceReviewType;
  reviewPeriodStart?: string;
  reviewPeriodEnd?: string;
}

export interface GoalQueryOptions {
  page?: number;
  limit?: number;
  employeeId?: string;
  assignedById?: string;
  status?: GoalStatus;
  priority?: GoalPriority;
  category?: string;
}

export interface PerformanceStatistics {
  // Propriétés de base du backend
  totalReviews: number;
  byStatus: Record<PerformanceReviewStatus, number>;
  byType: Record<PerformanceReviewType, number>;
  averageOverallRating: number;
  averageGoalCompletionRate: number;
  overdueReviews: number;
  pendingReviews: number;
  completedThisPeriod: number;

  // Propriétés calculées pour compatibilité frontend
  completedReviews?: number;
  averageScore?: number;
  totalGoals?: number;
  completedGoals?: number;
  overDueGoals?: number;
  averageGoalCompletion?: number;
  reviewsByStatus?: Array<{
    status: PerformanceReviewStatus;
    count: number;
  }>;
  goalsByStatus?: Array<{
    status: GoalStatus;
    count: number;
  }>;
}

@Injectable({
  providedIn: 'root',
})
export class PerformanceService {
  private apiUrl = `${environment.API_PREFIX}/performance`;

  constructor(private http: HttpClient) {}

  // Performance Review methods

  /**
   * Creates a new performance review
   */
  createReview(
    reviewData: CreatePerformanceReviewDto
  ): Observable<PerformanceReview> {
    return this.http
      .post<PerformanceReview>(`${this.apiUrl}/reviews`, reviewData)
      .pipe(
        catchError((error) => {
          console.error('Error creating performance review', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets all performance reviews with filtering
   */
  getReviews(
    options?: PerformanceQueryOptions
  ): Observable<PerformanceReview[]> {
    let params = new HttpParams();

    if (options) {
      if (options.page) params = params.set('page', options.page.toString());
      if (options.limit) params = params.set('limit', options.limit.toString());
      if (options.employeeId)
        params = params.set('employeeId', options.employeeId);
      if (options.reviewerId)
        params = params.set('reviewerId', options.reviewerId);
      if (options.status) params = params.set('status', options.status);
      if (options.reviewType)
        params = params.set('reviewType', options.reviewType);
      if (options.reviewPeriodStart)
        params = params.set('reviewPeriodStart', options.reviewPeriodStart);
      if (options.reviewPeriodEnd)
        params = params.set('reviewPeriodEnd', options.reviewPeriodEnd);
    }

    return this.http
      .get<PerformanceReview[]>(`${this.apiUrl}/reviews`, { params })
      .pipe(
        catchError((error) => {
          console.error('Error fetching performance reviews', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets a single performance review by ID
   */
  getReview(reviewId: string): Observable<PerformanceReview> {
    return this.http
      .get<PerformanceReview>(`${this.apiUrl}/reviews/${reviewId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching performance review ${reviewId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates a performance review
   */
  updateReview(
    reviewId: string,
    updateData: UpdatePerformanceReviewDto
  ): Observable<PerformanceReview> {
    return this.http
      .patch<PerformanceReview>(
        `${this.apiUrl}/reviews/${reviewId}`,
        updateData
      )
      .pipe(
        catchError((error) => {
          console.error(`Error updating performance review ${reviewId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a performance review
   */
  deleteReview(reviewId: string): Observable<PerformanceReview> {
    return this.http
      .delete<PerformanceReview>(`${this.apiUrl}/reviews/${reviewId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error deleting performance review ${reviewId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Submits a performance review for approval
   */
  submitReview(reviewId: string): Observable<PerformanceReview> {
    return this.http
      .patch<PerformanceReview>(`${this.apiUrl}/reviews/${reviewId}/submit`, {})
      .pipe(
        catchError((error) => {
          console.error(
            `Error submitting performance review ${reviewId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Approves a performance review
   */
  approveReview(
    reviewId: string,
    comments?: string
  ): Observable<PerformanceReview> {
    const data = { comments };
    return this.http
      .patch<PerformanceReview>(
        `${this.apiUrl}/reviews/${reviewId}/approve`,
        data
      )
      .pipe(
        catchError((error) => {
          console.error(
            `Error approving performance review ${reviewId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Rejects a performance review
   */
  rejectReview(
    reviewId: string,
    comments: string
  ): Observable<PerformanceReview> {
    const data = { comments };
    return this.http
      .patch<PerformanceReview>(
        `${this.apiUrl}/reviews/${reviewId}/reject`,
        data
      )
      .pipe(
        catchError((error) => {
          console.error(
            `Error rejecting performance review ${reviewId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  // Goal methods

  /**
   * Creates a new goal
   */
  createGoal(goalData: CreateGoalDto): Observable<Goal> {
    return this.http.post<Goal>(`${this.apiUrl}/goals`, goalData).pipe(
      catchError((error) => {
        console.error('Error creating goal', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets all goals with filtering
   */
  getGoals(options?: GoalQueryOptions): Observable<Goal[]> {
    let params = new HttpParams();

    if (options) {
      if (options.page) params = params.set('page', options.page.toString());
      if (options.limit) params = params.set('limit', options.limit.toString());
      if (options.employeeId)
        params = params.set('employeeId', options.employeeId);
      if (options.assignedById)
        params = params.set('assignedById', options.assignedById);
      if (options.status) params = params.set('status', options.status);
      if (options.priority) params = params.set('priority', options.priority);
      if (options.category) params = params.set('category', options.category);
    }

    return this.http.get<Goal[]>(`${this.apiUrl}/goals`, { params }).pipe(
      catchError((error) => {
        console.error('Error fetching goals', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets a single goal by ID
   */
  getGoal(goalId: string): Observable<Goal> {
    return this.http.get<Goal>(`${this.apiUrl}/goals/${goalId}`).pipe(
      catchError((error) => {
        console.error(`Error fetching goal ${goalId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates a goal
   */
  updateGoal(goalId: string, updateData: UpdateGoalDto): Observable<Goal> {
    return this.http
      .patch<Goal>(`${this.apiUrl}/goals/${goalId}`, updateData)
      .pipe(
        catchError((error) => {
          console.error(`Error updating goal ${goalId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a goal
   */
  deleteGoal(goalId: string): Observable<Goal> {
    return this.http.delete<Goal>(`${this.apiUrl}/goals/${goalId}`).pipe(
      catchError((error) => {
        console.error(`Error deleting goal ${goalId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates goal progress
   */
  updateGoalProgress(goalId: string, progress: number): Observable<Goal> {
    const data = { progress };
    return this.http
      .patch<Goal>(`${this.apiUrl}/goals/${goalId}/progress`, data)
      .pipe(
        catchError((error) => {
          console.error(`Error updating goal progress ${goalId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets performance statistics
   */
  getStatistics(): Observable<PerformanceStatistics> {
    return this.http
      .get<PerformanceStatistics>(`${this.apiUrl}/statistics`)
      .pipe(
        catchError((error) => {
          console.error('Error fetching performance statistics', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets employee performance reviews
   */
  getEmployeeReviews(employeeId: string): Observable<PerformanceReview[]> {
    const params = new HttpParams().set('employeeId', employeeId);
    return this.http
      .get<PerformanceReview[]>(`${this.apiUrl}/reviews`, { params })
      .pipe(
        catchError((error) => {
          console.error(
            `Error fetching reviews for employee ${employeeId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Gets employee goals
   */
  getEmployeeGoals(employeeId: string): Observable<Goal[]> {
    const params = new HttpParams().set('employeeId', employeeId);
    return this.http.get<Goal[]>(`${this.apiUrl}/goals`, { params }).pipe(
      catchError((error) => {
        console.error(`Error fetching goals for employee ${employeeId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Exports performance data
   */
  export(format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    const params = new HttpParams().set('format', format);
    return this.http
      .get(`${this.apiUrl}/export`, {
        params,
        responseType: 'blob',
      })
      .pipe(
        catchError((error) => {
          console.error('Error exporting performance data', error);
          return throwError(error);
        })
      );
  }
}
