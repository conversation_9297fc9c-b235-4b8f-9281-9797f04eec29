import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export enum ReportType {
  EMPLOYEE_REPORT = 'EMPLOYEE_REPORT',
  PAYROLL_REPORT = 'PAYROLL_REPORT',
  ATTENDANCE_REPORT = 'ATTENDANCE_REPORT',
  LEAVE_REPORT = 'LEAVE_REPORT',
  PERFORMANCE_REPORT = 'PERFORMANCE_REPORT',
  RECRUITMENT_REPORT = 'RECRUITMENT_REPORT',
  TURNOVER_REPORT = 'TURNOVER_REPORT',
  CUSTOM_REPORT = 'CUSTOM_REPORT',
}

export enum ReportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
  JSON = 'JSON',
  ZIP = 'ZIP',
}

export interface Report {
  id: string;
  name: string;
  description?: string;
  reportType: ReportType;
  status: ReportStatus;
  format: ReportFormat;
  parameters: Record<string, any>;
  filePath?: string;
  fileSize?: number;
  companyId: string;
  generatedById: string;
  generatedBy?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  generatedAt?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  reportType: ReportType;
  templateConfig: Record<string, any>;
  isActive: boolean;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateReportDto {
  name: string;
  description?: string;
  reportType: ReportType;
  format: ReportFormat;
  parameters: Record<string, any>;
  companyId: string;
}

export interface UpdateReportDto {
  name?: string;
  description?: string;
  parameters?: Record<string, any>;
}

export interface CreateReportTemplateDto {
  name: string;
  description?: string;
  reportType: ReportType;
  templateConfig: Record<string, any>;
  companyId: string;
}

export interface UpdateReportTemplateDto {
  name?: string;
  description?: string;
  templateConfig?: Record<string, any>;
  isActive?: boolean;
}

export interface ReportQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  reportType?: ReportType;
  status?: ReportStatus;
  format?: ReportFormat;
  generatedById?: string;
}

export interface DashboardMetrics {
  totalEmployees: number;
  activeEmployees: number;
  newHiresThisMonth: number;
  totalPayroll: number;
  averageSalary: number;
  pendingLeaves: number;
  overduePerformanceReviews: number;
  openJobPositions: number;
  employeeTurnoverRate: number;
  employeeSatisfactionScore: number;
}

export interface AnalyticsData {
  employeeGrowth: Array<{
    month: string;
    count: number;
  }>;
  departmentDistribution: Array<{
    department: string;
    count: number;
  }>;
  salaryDistribution: Array<{
    range: string;
    count: number;
  }>;
  leavesTrend: Array<{
    month: string;
    approved: number;
    pending: number;
    rejected: number;
  }>;
  performanceScores: Array<{
    month: string;
    averageScore: number;
  }>;
  recruitmentMetrics: Array<{
    month: string;
    applications: number;
    hired: number;
  }>;
}

export interface ReportStatistics {
  totalReports: number;
  reportsThisMonth: number;
  reportsByType: Array<{
    type: ReportType;
    count: number;
  }>;
  reportsByStatus: Array<{
    status: ReportStatus;
    count: number;
  }>;
  mostUsedTemplates: Array<{
    templateName: string;
    usageCount: number;
  }>;
}

@Injectable({
  providedIn: 'root',
})
export class ReportsService {
  private apiUrl = `${environment.API_PREFIX}/reports`;

  constructor(private http: HttpClient) {}

  // Report methods

  /**
   * Creates a new report
   */
  createReport(reportData: CreateReportDto): Observable<Report> {
    return this.http.post<Report>(this.apiUrl, reportData).pipe(
      catchError((error) => {
        console.error('Error creating report', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets all reports with filtering
   */
  getReports(options?: ReportQueryOptions): Observable<Report[]> {
    let params = new HttpParams();

    if (options) {
      if (options.page) params = params.set('page', options.page.toString());
      if (options.limit) params = params.set('limit', options.limit.toString());
      if (options.search) params = params.set('search', options.search);
      if (options.reportType)
        params = params.set('reportType', options.reportType);
      if (options.status) params = params.set('status', options.status);
      if (options.format) params = params.set('format', options.format);
      if (options.generatedById)
        params = params.set('generatedById', options.generatedById);
    }

    return this.http.get<Report[]>(this.apiUrl, { params }).pipe(
      catchError((error) => {
        console.error('Error fetching reports', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets a single report by ID
   */
  getReport(reportId: string): Observable<Report> {
    return this.http.get<Report>(`${this.apiUrl}/${reportId}`).pipe(
      catchError((error) => {
        console.error(`Error fetching report ${reportId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates a report
   */
  updateReport(
    reportId: string,
    updateData: UpdateReportDto
  ): Observable<Report> {
    return this.http
      .patch<Report>(`${this.apiUrl}/${reportId}`, updateData)
      .pipe(
        catchError((error) => {
          console.error(`Error updating report ${reportId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a report
   */
  deleteReport(reportId: string): Observable<Report> {
    return this.http.delete<Report>(`${this.apiUrl}/${reportId}`).pipe(
      catchError((error) => {
        console.error(`Error deleting report ${reportId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Generates a report
   */
  generateReport(reportId: string): Observable<Report> {
    return this.http
      .post<Report>(`${this.apiUrl}/${reportId}/generate`, {})
      .pipe(
        catchError((error) => {
          console.error(`Error generating report ${reportId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Downloads a generated report
   */
  downloadReport(reportId: string): Observable<Blob> {
    return this.http
      .get(`${this.apiUrl}/${reportId}/download`, {
        responseType: 'blob',
      })
      .pipe(
        catchError((error) => {
          console.error(`Error downloading report ${reportId}`, error);
          return throwError(error);
        })
      );
  }

  // Report Template methods

  /**
   * Creates a new report template
   */
  createTemplate(
    templateData: CreateReportTemplateDto
  ): Observable<ReportTemplate> {
    return this.http
      .post<ReportTemplate>(`${this.apiUrl}/templates`, templateData)
      .pipe(
        catchError((error) => {
          console.error('Error creating report template', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets all report templates
   */
  getTemplates(): Observable<ReportTemplate[]> {
    return this.http.get<ReportTemplate[]>(`${this.apiUrl}/templates`).pipe(
      catchError((error) => {
        console.error('Error fetching report templates', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets a single report template by ID
   */
  getTemplate(templateId: string): Observable<ReportTemplate> {
    return this.http
      .get<ReportTemplate>(`${this.apiUrl}/templates/${templateId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching report template ${templateId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates a report template
   */
  updateTemplate(
    templateId: string,
    updateData: UpdateReportTemplateDto
  ): Observable<ReportTemplate> {
    return this.http
      .patch<ReportTemplate>(
        `${this.apiUrl}/templates/${templateId}`,
        updateData
      )
      .pipe(
        catchError((error) => {
          console.error(`Error updating report template ${templateId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a report template
   */
  deleteTemplate(templateId: string): Observable<ReportTemplate> {
    return this.http
      .delete<ReportTemplate>(`${this.apiUrl}/templates/${templateId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error deleting report template ${templateId}`, error);
          return throwError(error);
        })
      );
  }

  // Dashboard and Analytics methods

  /**
   * Gets dashboard metrics
   */
  getDashboardMetrics(companyId: string): Observable<DashboardMetrics> {
    return this.http
      .get<DashboardMetrics>(`${this.apiUrl}/dashboard/${companyId}`)
      .pipe(
        catchError((error) => {
          console.error('Error fetching dashboard metrics', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets analytics data
   */
  getAnalyticsData(
    companyId: string,
    dateRange?: { start: string; end: string }
  ): Observable<AnalyticsData> {
    let params = new HttpParams();
    if (dateRange) {
      params = params.set('startDate', dateRange.start);
      params = params.set('endDate', dateRange.end);
    }

    return this.http
      .get<AnalyticsData>(`${this.apiUrl}/analytics/${companyId}`, { params })
      .pipe(
        catchError((error) => {
          console.error('Error fetching analytics data', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets report statistics
   */
  getReportStatistics(): Observable<ReportStatistics> {
    return this.http.get<ReportStatistics>(`${this.apiUrl}/statistics`).pipe(
      catchError((error) => {
        console.error('Error fetching report statistics', error);
        return throwError(error);
      })
    );
  }

  /**
   * Generates a quick report
   */
  generateQuickReport(
    reportType: ReportType,
    parameters: Record<string, any>
  ): Observable<Blob> {
    const data = { reportType, parameters };
    return this.http
      .post(`${this.apiUrl}/quick-generate`, data, {
        responseType: 'blob',
      })
      .pipe(
        catchError((error) => {
          console.error('Error generating quick report', error);
          return throwError(error);
        })
      );
  }

  /**
   * Schedules a recurring report
   */
  scheduleReport(
    reportId: string,
    schedule: Record<string, any>
  ): Observable<any> {
    return this.http.post(`${this.apiUrl}/${reportId}/schedule`, schedule).pipe(
      catchError((error) => {
        console.error(`Error scheduling report ${reportId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets scheduled reports
   */
  getScheduledReports(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/scheduled`).pipe(
      catchError((error) => {
        console.error('Error fetching scheduled reports', error);
        return throwError(error);
      })
    );
  }

  /**
   * Exports multiple reports
   */
  bulkExport(
    reportIds: string[],
    format: ReportFormat = ReportFormat.ZIP
  ): Observable<Blob> {
    const data = { reportIds, format };
    return this.http
      .post(`${this.apiUrl}/bulk-export`, data, {
        responseType: 'blob',
      })
      .pipe(
        catchError((error) => {
          console.error('Error bulk exporting reports', error);
          return throwError(error);
        })
      );
  }
}
