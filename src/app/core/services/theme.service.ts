import { Injectable, signal } from '@angular/core';
import { Theme } from '../models/theme.model';
import { effect } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  public theme = signal<Theme>({ mode: 'dark', color: 'base' });

  constructor() {
    this.loadTheme();
    effect(() => {
      this.setTheme();
    });
  }

  private loadTheme() {
    const theme = localStorage.getItem('theme');
    if (theme) {
      try {
        const parsedTheme = JSON.parse(theme);
        // Vérifier si c'est un objet valide avec les propriétés attendues
        if (
          parsedTheme &&
          typeof parsedTheme === 'object' &&
          'mode' in parsedTheme &&
          'color' in parsedTheme
        ) {
          this.theme.set(parsedTheme);
        } else {
          // Si c'est une ancienne valeur (comme "light" ou "dark"), la convertir
          this.migrateOldTheme(theme);
        }
      } catch (error) {
        // Si le parsing échoue, traiter comme une ancienne valeur
        this.migrateOldTheme(theme);
      }
    }
  }

  private migrateOldTheme(oldTheme: string) {
    // Convertir les anciennes valeurs de thème en nouveau format
    const cleanTheme = oldTheme.replace(/"/g, ''); // Enlever les guillemets si présents
    const newTheme: Theme = {
      mode:
        cleanTheme === 'light' || cleanTheme === 'dark'
          ? (cleanTheme as 'light' | 'dark')
          : 'dark',
      color: 'base',
    };
    this.theme.set(newTheme);
    // Sauvegarder immédiatement le nouveau format
    localStorage.setItem('theme', JSON.stringify(newTheme));
  }

  private setTheme() {
    localStorage.setItem('theme', JSON.stringify(this.theme()));
    this.setThemeClass();
  }

  public get isDark(): boolean {
    return this.theme().mode == 'dark';
  }

  private setThemeClass() {
    document.querySelector('html')!.className = this.theme().mode;
    document.querySelector('html')!.setAttribute('data-theme', 'blue');
  }
}
