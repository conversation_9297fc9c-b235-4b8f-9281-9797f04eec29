import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { CompanyContextService } from '../company/company-context.service';

export enum LeaveType {
  ANNUAL = 'ANNUAL',
  SICK = 'SICK',
  MATERNITY = 'MATERNITY',
  PATERNITY = 'PATERNITY',
  PERSONAL = 'PERSONAL',
  EMERGENCY = 'EMERGENCY',
  UNPAID = 'UNPAID',
  STUDY = 'STUDY',
  BEREAVEMENT = 'BEREAVEMENT',
  COMPENSATORY = 'COMPENSATORY',
}

export enum LeaveStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export interface Leave {
  id: string;
  leaveType: LeaveType;
  startDate: string;
  endDate: string;
  reason: string;
  status: LeaveStatus;
  employeeId: string;
  approverId?: string;
  approvalComments?: string;
  rejectionReason?: string;
  approvedAt?: string;
  rejectedAt?: string;
  cancelledAt?: string;
  employee?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateLeaveDto {
  leaveType: LeaveType;
  startDate: string;
  endDate: string;
  reason: string;
  employeeId: string;
}

export interface UpdateLeaveDto {
  leaveType?: LeaveType;
  startDate?: string;
  endDate?: string;
  reason?: string;
  status?: LeaveStatus;
}

@Injectable({
  providedIn: 'root',
})
export class LeaveService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(
    private http: HttpClient,
    private companyContext: CompanyContextService
  ) {}

  /**
   * Creates a new leave request
   */
  createLeave(companyId: string, leaveData: CreateLeaveDto): Observable<Leave> {
    return this.http
      .post<Leave>(`${this.apiUrl}/${companyId}/leaves`, leaveData)
      .pipe(
        catchError((error) => {
          console.error('Error creating leave', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Creates a new leave request using current company context
   */
  createLeaveForCurrentCompany(leaveData: CreateLeaveDto): Observable<Leave> {
    const companyId = this.companyContext.getCurrentCompanyIdSync();
    if (!companyId) {
      return throwError(() => new Error('No company ID available'));
    }
    return this.createLeave(companyId, leaveData);
  }

  /**
   * Récupère toutes les demandes de congé pour une entreprise donnée.
   * @param companyId - L'ID de l'entreprise.
   * @returns Un Observable contenant la liste des demandes de congé.
   */
  getLeaves(companyId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${companyId}/leaves`).pipe(
      catchError((error) => {
        console.error(
          'Erreur lors de la récupération des demandes de congé',
          error
        );
        return throwError(() => error);
      })
    );
  }

  /**
   * Récupère toutes les demandes de congé pour l'entreprise actuelle
   */
  getLeavesForCurrentCompany(): Observable<any> {
    const companyId = this.companyContext.getCurrentCompanyIdSync();
    console.log(
      'LeaveService - getLeavesForCurrentCompany - companyId:',
      companyId
    );
    console.log(
      'LeaveService - URL will be:',
      `${this.apiUrl}/${companyId}/leaves`
    );

    if (!companyId) {
      console.error('LeaveService - No company ID available!');
      return throwError(() => new Error('No company ID available'));
    }
    return this.getLeaves(companyId);
  }

  /**
   * Récupère toutes les demandes de congé pour un employé donné.
   * @param companyId - L'ID de l'entreprise.
   * @param employeeId - L'ID de l'employé.
   * @returns Un Observable contenant la liste des demandes de congé.
   */
  getEmployeeLeaves(companyId: string, employeeId: string): Observable<any> {
    return this.http
      .get(`${this.apiUrl}/${companyId}/employee/${employeeId}/leaves`)
      .pipe(
        catchError((error) => {
          console.error(
            'Erreur lors de la récupération des demandes de congé',
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Récupère les détails d'une demande de congé par ID.
   * @param leaveId - L'ID de la demande de congé.
   * @returns Un Observable contenant les détails de la demande.
   */
  getLeaveById(leaveId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/leaves/${leaveId}`).pipe(
      catchError((error) => {
        console.error(
          `Erreur lors de la récupération de la demande de congé ${leaveId}`,
          error
        );
        return throwError(error);
      })
    );
  }

  /**
   * Met à jour une demande de congé existante.
   * @param leaveId - L'ID de la demande de congé.
   * @param leaveData - Les nouvelles données de la demande.
   * @returns Un Observable contenant la réponse du serveur.
   */
  updateLeave(leaveId: string, leaveData: any): Observable<any> {
    return this.http.patch(`${this.apiUrl}/leaves/${leaveId}`, leaveData).pipe(
      catchError((error) => {
        console.error(
          `Erreur lors de la mise à jour de la demande de congé ${leaveId}`,
          error
        );
        return throwError(error);
      })
    );
  }

  /**
   * Supprime une demande de congé par ID.
   * @param leaveId - L'ID de la demande de congé.
   * @returns Un Observable contenant la réponse du serveur.
   */
  deleteLeave(leaveId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/leaves/${leaveId}`).pipe(
      catchError((error) => {
        console.error(
          `Erreur lors de la suppression de la demande de congé ${leaveId}`,
          error
        );
        return throwError(error);
      })
    );
  }
}
