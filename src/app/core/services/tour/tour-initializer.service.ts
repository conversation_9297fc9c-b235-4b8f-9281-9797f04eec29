import { Injectable } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, take } from 'rxjs/operators';
import { TourService } from './tour.service';
import { LUMINA_HR_TOUR } from '../../constants/tour-steps';

@Injectable({
  providedIn: 'root',
})
export class TourInitializerService {
  private hasInitialized = false;

  constructor(private tourService: TourService, private router: Router) {}

  public initializeTour(): void {
    if (this.hasInitialized) {
      return;
    }

    this.hasInitialized = true;

    // Attendre que la navigation soit terminée avant de démarrer le tour
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        take(1)
      )
      .subscribe(() => {
        // Attendre un peu pour que la page soit complètement chargée
        setTimeout(() => {
          this.checkAndStartTour();
        }, 1000);
      });
  }

  private checkAndStartTour(): void {
    // Vérifier si c'est la première visite et si le tour n'a pas été complété
    if (
      this.tourService.isFirstVisit() &&
      !this.tourService.isTourCompleted()
    ) {
      // S'assurer qu'on est sur la page dashboard overview
      if (
        this.router.url.includes('/dashboard/overview') ||
        this.router.url === '/dashboard' ||
        this.router.url === '/'
      ) {
        // Ne pas démarrer automatiquement le tour, laisser le composant welcome le faire
        console.log('Tour disponible - utilisateur sur le dashboard');
      }
    }
  }

  public startTourManually(): void {
    this.tourService.startTour(LUMINA_HR_TOUR);
  }

  public resetAndStartTour(): void {
    this.tourService.resetTour();
    setTimeout(() => {
      this.tourService.startTour(LUMINA_HR_TOUR);
    }, 100);
  }
}
