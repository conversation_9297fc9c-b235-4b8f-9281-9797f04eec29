import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { TourConfig, TourStep, TourState } from '../../models/tour.model';

@Injectable({
  providedIn: 'root',
})
export class TourService {
  private readonly TOUR_STORAGE_KEY = 'lumina-hr-tour-completed';
  private readonly FIRST_VISIT_KEY = 'lumina-hr-first-visit';

  private tourStateSubject = new BehaviorSubject<TourState>({
    isActive: false,
    currentStepIndex: 0,
    isCompleted: false,
    isFirstVisit: true,
  });

  public tourState$: Observable<TourState> =
    this.tourStateSubject.asObservable();
  private currentConfig: TourConfig | null = null;

  constructor(private router: Router) {
    this.initializeTourState();
  }

  private initializeTourState(): void {
    const isCompleted = localStorage.getItem(this.TOUR_STORAGE_KEY) === 'true';
    const isFirstVisit = localStorage.getItem(this.FIRST_VISIT_KEY) !== 'false';

    this.tourStateSubject.next({
      ...this.tourStateSubject.value,
      isCompleted,
      isFirstVisit,
    });
  }

  public startTour(config: TourConfig): void {
    this.currentConfig = config;
    this.tourStateSubject.next({
      isActive: true,
      currentStepIndex: 0,
      isCompleted: false,
      isFirstVisit: this.tourStateSubject.value.isFirstVisit,
    });

    // Marquer que ce n'est plus la première visite
    localStorage.setItem(this.FIRST_VISIT_KEY, 'false');

    this.showCurrentStep();
  }

  public nextStep(): void {
    const currentState = this.tourStateSubject.value;
    if (
      !this.currentConfig ||
      currentState.currentStepIndex >= this.currentConfig.steps.length - 1
    ) {
      this.completeTour();
      return;
    }

    const nextIndex = currentState.currentStepIndex + 1;
    this.tourStateSubject.next({
      ...currentState,
      currentStepIndex: nextIndex,
    });

    this.showCurrentStep();
  }

  public previousStep(): void {
    const currentState = this.tourStateSubject.value;
    if (currentState.currentStepIndex <= 0) {
      return;
    }

    const prevIndex = currentState.currentStepIndex - 1;
    this.tourStateSubject.next({
      ...currentState,
      currentStepIndex: prevIndex,
    });

    this.showCurrentStep();
  }

  public skipTour(): void {
    this.completeTour();
  }

  public completeTour(): void {
    localStorage.setItem(this.TOUR_STORAGE_KEY, 'true');
    this.tourStateSubject.next({
      isActive: false,
      currentStepIndex: 0,
      isCompleted: true,
      isFirstVisit: false,
    });
    this.currentConfig = null;
  }

  public resetTour(): void {
    localStorage.removeItem(this.TOUR_STORAGE_KEY);
    localStorage.removeItem(this.FIRST_VISIT_KEY);
    this.tourStateSubject.next({
      isActive: false,
      currentStepIndex: 0,
      isCompleted: false,
      isFirstVisit: true,
    });
  }

  public getCurrentStep(): TourStep | null {
    if (!this.currentConfig) {
      return null;
    }

    const currentState = this.tourStateSubject.value;
    return this.currentConfig.steps[currentState.currentStepIndex] || null;
  }

  public getTotalSteps(): number {
    return this.currentConfig?.steps.length || 0;
  }

  public getCurrentStepIndex(): number {
    return this.tourStateSubject.value.currentStepIndex;
  }

  public isFirstVisit(): boolean {
    return this.tourStateSubject.value.isFirstVisit;
  }

  public isTourCompleted(): boolean {
    return this.tourStateSubject.value.isCompleted;
  }

  private async showCurrentStep(): Promise<void> {
    const currentStep = this.getCurrentStep();
    if (!currentStep) {
      return;
    }

    // Naviguer vers la route si nécessaire
    if (currentStep.route && this.router.url !== currentStep.route) {
      await this.router.navigate([currentStep.route]);
      // Attendre un peu pour que la navigation se termine
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    // Exécuter l'action personnalisée si définie
    if (currentStep.action) {
      currentStep.action();
    }

    // Faire défiler vers l'élément cible si configuré
    if (this.currentConfig?.scrollToTarget) {
      this.scrollToTarget(currentStep.target);
    }
  }

  private scrollToTarget(selector: string): void {
    const element = document.querySelector(selector);
    if (element) {
      const offset = this.currentConfig?.scrollOffset || 120;
      const elementRect = element.getBoundingClientRect();
      const elementPosition = elementRect.top + window.pageYOffset;

      // Calculer la position optimale pour centrer l'élément dans la vue
      const viewportHeight = window.innerHeight;
      const elementHeight = elementRect.height;
      const optimalPosition =
        elementPosition - viewportHeight / 2 + elementHeight / 2;

      // S'assurer qu'on ne scroll pas trop haut
      const finalPosition = Math.max(0, optimalPosition);

      window.scrollTo({
        top: finalPosition,
        behavior: 'smooth',
      });
    }
  }
}
