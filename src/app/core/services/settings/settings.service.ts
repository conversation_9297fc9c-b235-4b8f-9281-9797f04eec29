import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export enum SettingType {
  SYSTEM = 'SYSTEM',
  COMPANY = 'COMPANY',
  USER = 'USER',
  NOTIFICATION = 'NOTIFICATION',
  SECURITY = 'SECURITY',
  INTEGRATION = 'INTEGRATION',
}

export enum SettingDataType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  DATE = 'DATE',
  EMAIL = 'EMAIL',
  URL = 'URL',
}

export interface Setting {
  id: string;
  key: string;
  value: any;
  defaultValue: any;
  type: SettingType;
  dataType: SettingDataType;
  category: string;
  description?: string;
  isEditable: boolean;
  isVisible: boolean;
  validation?: Record<string, any>;
  companyId?: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationSetting {
  id: string;
  userId: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  notificationTypes: {
    leaveRequests: boolean;
    performanceReviews: boolean;
    payrollUpdates: boolean;
    systemAlerts: boolean;
    documentApprovals: boolean;
    recruitmentUpdates: boolean;
  };
  frequency: 'IMMEDIATE' | 'DAILY' | 'WEEKLY';
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface SecuritySetting {
  id: string;
  companyId: string;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    expirationDays: number;
    preventReuse: number;
  };
  sessionSettings: {
    timeoutMinutes: number;
    maxConcurrentSessions: number;
    requireReauthentication: boolean;
  };
  twoFactorAuth: {
    enabled: boolean;
    required: boolean;
    methods: string[];
  };
  ipWhitelist: string[];
  auditLog: {
    enabled: boolean;
    retentionDays: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface IntegrationSetting {
  id: string;
  companyId: string;
  name: string;
  type: string;
  isEnabled: boolean;
  configuration: Record<string, any>;
  credentials: Record<string, any>;
  webhookUrl?: string;
  lastSyncAt?: string;
  syncStatus: 'ACTIVE' | 'INACTIVE' | 'ERROR';
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSettingDto {
  key: string;
  value: any;
  type: SettingType;
  dataType: SettingDataType;
  category: string;
  description?: string;
  isEditable?: boolean;
  isVisible?: boolean;
  validation?: Record<string, any>;
  companyId?: string;
  userId?: string;
}

export interface UpdateSettingDto {
  value?: any;
  description?: string;
  isEditable?: boolean;
  isVisible?: boolean;
  validation?: Record<string, any>;
}

export interface UpdateNotificationSettingDto {
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  smsNotifications?: boolean;
  notificationTypes?: Partial<NotificationSetting['notificationTypes']>;
  frequency?: NotificationSetting['frequency'];
  quietHours?: Partial<NotificationSetting['quietHours']>;
}

export interface UpdateSecuritySettingDto {
  passwordPolicy?: Partial<SecuritySetting['passwordPolicy']>;
  sessionSettings?: Partial<SecuritySetting['sessionSettings']>;
  twoFactorAuth?: Partial<SecuritySetting['twoFactorAuth']>;
  ipWhitelist?: string[];
  auditLog?: Partial<SecuritySetting['auditLog']>;
}

export interface CreateIntegrationDto {
  name: string;
  type: string;
  configuration: Record<string, any>;
  credentials: Record<string, any>;
  webhookUrl?: string;
  companyId: string;
}

export interface UpdateIntegrationDto {
  name?: string;
  isEnabled?: boolean;
  configuration?: Record<string, any>;
  credentials?: Record<string, any>;
  webhookUrl?: string;
}

export interface SettingQueryOptions {
  page?: number;
  limit?: number;
  type?: SettingType;
  category?: string;
  companyId?: string;
  userId?: string;
  isEditable?: boolean;
  isVisible?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private apiUrl = `${environment.API_PREFIX}/settings`;

  constructor(private http: HttpClient) {}

  // General Settings methods

  /**
   * Creates a new setting
   */
  createSetting(settingData: CreateSettingDto): Observable<Setting> {
    return this.http.post<Setting>(this.apiUrl, settingData).pipe(
      catchError((error) => {
        console.error('Error creating setting', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets all settings with filtering
   */
  getSettings(options?: SettingQueryOptions): Observable<Setting[]> {
    let params = new HttpParams();
    
    if (options) {
      if (options.page) params = params.set('page', options.page.toString());
      if (options.limit) params = params.set('limit', options.limit.toString());
      if (options.type) params = params.set('type', options.type);
      if (options.category) params = params.set('category', options.category);
      if (options.companyId) params = params.set('companyId', options.companyId);
      if (options.userId) params = params.set('userId', options.userId);
      if (options.isEditable !== undefined) params = params.set('isEditable', options.isEditable.toString());
      if (options.isVisible !== undefined) params = params.set('isVisible', options.isVisible.toString());
    }

    return this.http.get<Setting[]>(this.apiUrl, { params }).pipe(
      catchError((error) => {
        console.error('Error fetching settings', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets a single setting by key
   */
  getSetting(key: string, companyId?: string, userId?: string): Observable<Setting> {
    let params = new HttpParams();
    if (companyId) params = params.set('companyId', companyId);
    if (userId) params = params.set('userId', userId);

    return this.http.get<Setting>(`${this.apiUrl}/${key}`, { params }).pipe(
      catchError((error) => {
        console.error(`Error fetching setting ${key}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates a setting
   */
  updateSetting(key: string, updateData: UpdateSettingDto, companyId?: string, userId?: string): Observable<Setting> {
    let params = new HttpParams();
    if (companyId) params = params.set('companyId', companyId);
    if (userId) params = params.set('userId', userId);

    return this.http.patch<Setting>(`${this.apiUrl}/${key}`, updateData, { params }).pipe(
      catchError((error) => {
        console.error(`Error updating setting ${key}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Deletes a setting
   */
  deleteSetting(key: string, companyId?: string, userId?: string): Observable<Setting> {
    let params = new HttpParams();
    if (companyId) params = params.set('companyId', companyId);
    if (userId) params = params.set('userId', userId);

    return this.http.delete<Setting>(`${this.apiUrl}/${key}`, { params }).pipe(
      catchError((error) => {
        console.error(`Error deleting setting ${key}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Resets a setting to its default value
   */
  resetSetting(key: string, companyId?: string, userId?: string): Observable<Setting> {
    let params = new HttpParams();
    if (companyId) params = params.set('companyId', companyId);
    if (userId) params = params.set('userId', userId);

    return this.http.post<Setting>(`${this.apiUrl}/${key}/reset`, {}, { params }).pipe(
      catchError((error) => {
        console.error(`Error resetting setting ${key}`, error);
        return throwError(error);
      })
    );
  }

  // Notification Settings methods

  /**
   * Gets user notification settings
   */
  getNotificationSettings(userId: string): Observable<NotificationSetting> {
    return this.http.get<NotificationSetting>(`${this.apiUrl}/notifications/${userId}`).pipe(
      catchError((error) => {
        console.error(`Error fetching notification settings for user ${userId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates user notification settings
   */
  updateNotificationSettings(userId: string, updateData: UpdateNotificationSettingDto): Observable<NotificationSetting> {
    return this.http.patch<NotificationSetting>(`${this.apiUrl}/notifications/${userId}`, updateData).pipe(
      catchError((error) => {
        console.error(`Error updating notification settings for user ${userId}`, error);
        return throwError(error);
      })
    );
  }

  // Security Settings methods

  /**
   * Gets company security settings
   */
  getSecuritySettings(companyId: string): Observable<SecuritySetting> {
    return this.http.get<SecuritySetting>(`${this.apiUrl}/security/${companyId}`).pipe(
      catchError((error) => {
        console.error(`Error fetching security settings for company ${companyId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates company security settings
   */
  updateSecuritySettings(companyId: string, updateData: UpdateSecuritySettingDto): Observable<SecuritySetting> {
    return this.http.patch<SecuritySetting>(`${this.apiUrl}/security/${companyId}`, updateData).pipe(
      catchError((error) => {
        console.error(`Error updating security settings for company ${companyId}`, error);
        return throwError(error);
      })
    );
  }

  // Integration Settings methods

  /**
   * Creates a new integration
   */
  createIntegration(integrationData: CreateIntegrationDto): Observable<IntegrationSetting> {
    return this.http.post<IntegrationSetting>(`${this.apiUrl}/integrations`, integrationData).pipe(
      catchError((error) => {
        console.error('Error creating integration', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets all integrations for a company
   */
  getIntegrations(companyId: string): Observable<IntegrationSetting[]> {
    const params = new HttpParams().set('companyId', companyId);
    return this.http.get<IntegrationSetting[]>(`${this.apiUrl}/integrations`, { params }).pipe(
      catchError((error) => {
        console.error(`Error fetching integrations for company ${companyId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets a single integration by ID
   */
  getIntegration(integrationId: string): Observable<IntegrationSetting> {
    return this.http.get<IntegrationSetting>(`${this.apiUrl}/integrations/${integrationId}`).pipe(
      catchError((error) => {
        console.error(`Error fetching integration ${integrationId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates an integration
   */
  updateIntegration(integrationId: string, updateData: UpdateIntegrationDto): Observable<IntegrationSetting> {
    return this.http.patch<IntegrationSetting>(`${this.apiUrl}/integrations/${integrationId}`, updateData).pipe(
      catchError((error) => {
        console.error(`Error updating integration ${integrationId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Deletes an integration
   */
  deleteIntegration(integrationId: string): Observable<IntegrationSetting> {
    return this.http.delete<IntegrationSetting>(`${this.apiUrl}/integrations/${integrationId}`).pipe(
      catchError((error) => {
        console.error(`Error deleting integration ${integrationId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Tests an integration connection
   */
  testIntegration(integrationId: string): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.apiUrl}/integrations/${integrationId}/test`, {}).pipe(
      catchError((error) => {
        console.error(`Error testing integration ${integrationId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Syncs an integration
   */
  syncIntegration(integrationId: string): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.apiUrl}/integrations/${integrationId}/sync`, {}).pipe(
      catchError((error) => {
        console.error(`Error syncing integration ${integrationId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets available integration types
   */
  getIntegrationTypes(): Observable<Array<{ type: string; name: string; description: string; configSchema: any }>> {
    return this.http.get<Array<{ type: string; name: string; description: string; configSchema: any }>>(`${this.apiUrl}/integrations/types`).pipe(
      catchError((error) => {
        console.error('Error fetching integration types', error);
        return throwError(error);
      })
    );
  }

  /**
   * Exports settings configuration
   */
  exportSettings(companyId: string, format: 'json' | 'yaml' = 'json'): Observable<Blob> {
    const params = new HttpParams().set('format', format);
    return this.http.get(`${this.apiUrl}/export/${companyId}`, { 
      params, 
      responseType: 'blob' 
    }).pipe(
      catchError((error) => {
        console.error('Error exporting settings', error);
        return throwError(error);
      })
    );
  }

  /**
   * Imports settings configuration
   */
  importSettings(companyId: string, file: File): Observable<{ imported: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<{ imported: number; errors: string[] }>(`${this.apiUrl}/import/${companyId}`, formData).pipe(
      catchError((error) => {
        console.error('Error importing settings', error);
        return throwError(error);
      })
    );
  }
}
