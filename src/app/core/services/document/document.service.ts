import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: string;
  category: string;
  tags: string[];
}

@Injectable({
  providedIn: 'root',
})
export class DocumentService {
  private apiUrl = `${environment.apiUrl}/documents`;

  constructor(private http: HttpClient) {}

  getDocuments(filter?: any): Observable<Document[]> {
    // Mock data pour l'instant
    return of([]);
  }

  getDocumentStats(): Observable<any> {
    // Mock data pour l'instant
    return of({
      total: 0,
      totalSize: 0,
      byCategory: [],
    });
  }

  uploadDocument(file: File, metadata: any): Observable<Document> {
    // Mock data pour l'instant
    const mockDocument: Document = {
      id: '1',
      name: file.name,
      type: file.type,
      size: file.size,
      url: 'mock-url',
      uploadedBy: 'current-user',
      uploadedAt: new Date().toISOString(),
      category: metadata.category || 'general',
      tags: metadata.tags || [],
    };
    return of(mockDocument);
  }

  downloadDocument(id: string): Observable<Blob> {
    // Mock data pour l'instant
    return of(new Blob());
  }

  deleteDocument(id: string): Observable<void> {
    return of(void 0);
  }

  /**
   * Opération en lot sur les documents
   */
  bulkOperation(operation: any): Observable<any> {
    return of({ success: true });
  }
}
