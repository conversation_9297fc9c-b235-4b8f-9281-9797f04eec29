import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { JwtHelperService } from '@auth0/angular-jwt';
import { Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Store } from '@ngrx/store';
import { AppState } from '../../store/app.state';
import {
  loadUserFailure,
  loadUserSuccess,
} from '../../store/user/user.actions';
import {
  loadCompanyFailure,
  loadCompanySuccess,
} from '../../store/company/company.actions';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly ACCESS_TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private jwtHelper = new JwtHelperService();
  private apiUrl = `${environment.API_PREFIX}/auth`;
  private companyApiUrl = `${environment.API_PREFIX}/companies`;

  constructor(
    private http: HttpClient,
    private router: Router,
    private store: Store<AppState>
  ) {}

  signUp(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    role?: string;
  }): Observable<any> {
    return this.http.post(`${this.apiUrl}/signup`, userData).pipe(
      tap((res: any) => {
        this.setTokens(res.accessToken, res.refreshToken);
      })
    );
  }

  /**
   * Authenticates the user and stores the received tokens.
   * @param credentials - The login credentials (email and password).
   * @returns An Observable of the server response.
   */
  login(credentials: { email: string; password: string }): Observable<any> {
    return this.http.post<unknown>(`${this.apiUrl}/signin`, credentials).pipe(
      tap((res: any) => {
        // Stocker les tokens
        this.setTokens(res.accessToken, res.refreshToken);

        // Extraire et stocker le companyId depuis la réponse de login
        const companyId = res.user?.companyId;
        console.log('Full login response:', res); // Debug complet
        console.log('User object:', res.user); // Debug user
        console.log('Company ID from login response:', companyId); // Debug companyId

        if (companyId) {
          // Stocker le companyId dans localStorage pour un accès rapide
          localStorage.setItem('currentCompanyId', companyId);
          console.log(
            'Company ID stored in localStorage:',
            localStorage.getItem('currentCompanyId')
          );
        } else {
          console.error('No company ID found in login response!');
        }

        // Navigation vers le dashboard
        this.router.navigate(['/']);
        this.store.dispatch(loadUserSuccess({ user: res.user }));
        this.fetchCompanyData(companyId);
      }),

      catchError((error) => {
        console.error('Login failed', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Logs out the user by removing tokens and redirecting to the login page.
   */
  logout(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    this.router.navigate(['/auth/sign-in']);
  }

  /**
   * Verifies user account with OTP
   * @param userId - User ID to verify
   * @param otp - OTP code
   * @returns Observable of verification result
   */
  verifyAccount(userId: string, otp: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/verify-account/${userId}`, { otp });
  }

  /**
   * Requests password reset OTP
   * @param email - User email
   * @returns Observable of request result
   */
  requestPasswordReset(email: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/request-password-reset`, { email });
  }

  /**
   * Resets password with OTP
   * @param email - User email
   * @param otp - OTP code
   * @param newPassword - New password
   * @returns Observable of reset result
   */
  resetPassword(
    email: string,
    otp: string,
    newPassword: string
  ): Observable<any> {
    return this.http.post(`${this.apiUrl}/reset-password/${email}`, {
      otp,
      newPassword,
    });
  }

  /**
   * Gets current user profile
   * @returns Observable of user profile
   */
  getProfile(): Observable<any> {
    return this.http.get(`${this.apiUrl}/profile`);
  }

  /**
   * Updates current user profile
   * @param profileData - Profile update data
   * @returns Observable of updated profile
   */
  updateProfile(profileData: any): Observable<any> {
    return this.http.patch(`${this.apiUrl}/profile`, profileData);
  }

  /**
   * Changes current user password
   * @param currentPassword - Current password
   * @param newPassword - New password
   * @returns Observable of change result
   */
  changePassword(
    currentPassword: string,
    newPassword: string
  ): Observable<any> {
    return this.http.patch(`${this.apiUrl}/change-password`, {
      currentPassword,
      newPassword,
    });
  }

  /**
   * Checks if the user is authenticated.
   * @returns `true` if the user is authenticated, otherwise `false`.
   */
  isAuthenticated(): boolean {
    const token = this.getAccessToken();
    return !!token && !this.jwtHelper.isTokenExpired(token);
  }

  fetchUserData(): Observable<any> {
    // Créer les headers avec le token manuellement pour s'assurer qu'il est inclus
    const token = this.getAccessToken();
    const options = token
      ? {
          headers: { Authorization: `Bearer ${token}` },
        }
      : {};

    return this.http.get<any>(`${this.apiUrl}/profile`, options).pipe(
      tap((user) => {
        this.store.dispatch(loadUserSuccess({ user })); // Dispatch user data to the store
      }),
      catchError((error) => {
        this.store.dispatch(loadUserFailure({ error })); // Dispatch error if something goes wrong
        return throwError(() => error);
      })
    );
  }

  fetchCompanyData(id: number): void {
    // Créer les headers avec le token manuellement
    const token = this.getAccessToken();
    const options = token
      ? {
          headers: { Authorization: `Bearer ${token}` },
        }
      : {};

    // API call to fetch company details
    this.http
      .get<any>(`${this.companyApiUrl}/${id}`, options)
      .pipe(
        tap((company) => {
          this.store.dispatch(loadCompanySuccess({ company })); // Dispatch company data to the store
        }),
        catchError((error) => {
          this.store.dispatch(loadCompanyFailure({ error })); // Dispatch error if something goes wrong
          return throwError(() => error);
        })
      )
      .subscribe();
  }

  /**
   * Refreshes the access token using the refresh token.
   * @returns An Observable containing the new tokens.
   */
  refreshToken(): Observable<any> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      this.logout();
      return throwError('No refresh token available');
    }

    return this.http
      .post('/api/auth/refresh-tokens', { refresh_token: refreshToken })
      .pipe(
        tap((res: any) => this.setTokens(res.access_token, res.refresh_token)),
        catchError((error) => {
          console.error('Refresh token failed', error);
          this.logout();
          return throwError(error);
        })
      );
  }

  /**
   * Stores tokens in localStorage.
   * @param accessToken - The access token.
   * @param refreshToken - The refresh token.
   */
  private setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  /**
   * Retrieves the access token from localStorage.
   * @returns The access token or `null` if it doesn't exist.
   */
  getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  /**
   * Retrieves the refresh token from localStorage.
   * @returns The refresh token or `null` if it doesn't exist.
   */
  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  /**
   * Retrieves the current access token (alias for getAccessToken).
   * @returns The access token or `null` if it doesn't exist.
   */
  getToken(): string | null {
    return this.getAccessToken();
  }

  /**
   * Retrieves the current company ID from localStorage.
   * @returns The company ID or `null` if it doesn't exist.
   */
  getCurrentCompanyId(): string | null {
    // Essayer d'abord localStorage
    let companyId = localStorage.getItem('currentCompanyId');

    // Si pas trouvé, essayer d'extraire du token JWT
    if (!companyId) {
      companyId = this.getCompanyIdFromToken();
    }

    console.log('AuthService - getCurrentCompanyId:', companyId);
    return companyId;
  }

  /**
   * Extrait le companyId depuis le token JWT
   * @returns Le company ID ou null si non trouvé
   */
  private getCompanyIdFromToken(): string | null {
    try {
      const token = this.getAccessToken();
      if (!token) return null;

      // Décoder le payload du JWT (partie entre les deux points)
      const payload = token.split('.')[1];
      if (!payload) return null;

      // Décoder base64
      const decodedPayload = JSON.parse(atob(payload));
      console.log('JWT payload:', decodedPayload);

      return decodedPayload.companyId || null;
    } catch (error) {
      console.error('Error decoding JWT token:', error);
      return null;
    }
  }
}
