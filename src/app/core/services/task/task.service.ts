import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  REVIEW = 'REVIEW',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ON_HOLD = 'ON_HOLD',
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum TaskType {
  GENERAL = 'GENERAL',
  HR_TASK = 'HR_TASK',
  ONBOARDING = 'ONBOARDING',
  PERFORMANCE_REVIEW = 'PERFORMANCE_REVIEW',
  DOCUMENT_REVIEW = 'DOCUMENT_REVIEW',
  COMPLIANCE = 'COMPLIANCE',
  TRAINING = 'TRAINING',
  PROJECT = 'PROJECT',
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  type: TaskType;
  dueDate?: string;
  startDate?: string;
  completedDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  progress: number;
  assigneeId: string;
  assignedById: string;
  companyId: string;
  projectId?: string;
  parentTaskId?: string;
  tags: string[];
  attachments: string[];
  assignee?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  assignedBy?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  subtasks?: Task[];
  comments?: TaskComment[];
  createdAt: string;
  updatedAt: string;
}

export interface TaskComment {
  id: string;
  content: string;
  taskId: string;
  authorId: string;
  author?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface TaskProject {
  id: string;
  name: string;
  description?: string;
  status: 'ACTIVE' | 'COMPLETED' | 'ON_HOLD' | 'CANCELLED';
  startDate: string;
  endDate?: string;
  companyId: string;
  managerId: string;
  teamMembers: string[];
  tasks?: Task[];
  manager?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateTaskDto {
  title: string;
  description?: string;
  priority: TaskPriority;
  type: TaskType;
  dueDate?: string;
  startDate?: string;
  estimatedHours?: number;
  assigneeId: string;
  companyId: string;
  projectId?: string;
  parentTaskId?: string;
  tags?: string[];
}

export interface UpdateTaskDto {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  dueDate?: string;
  startDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  progress?: number;
  assigneeId?: string;
  projectId?: string;
  tags?: string[];
}

export interface CreateTaskCommentDto {
  content: string;
  taskId: string;
}

export interface CreateTaskProjectDto {
  name: string;
  description?: string;
  startDate: string;
  endDate?: string;
  companyId: string;
  managerId: string;
  teamMembers?: string[];
}

export interface UpdateTaskProjectDto {
  name?: string;
  description?: string;
  status?: TaskProject['status'];
  startDate?: string;
  endDate?: string;
  managerId?: string;
  teamMembers?: string[];
}

export interface TaskQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  assigneeId?: string;
  assignedById?: string;
  projectId?: string;
  dueDate?: string;
  tags?: string[];
}

export interface TaskStatistics {
  // Propriétés de base du backend
  totalTasks: number;
  byStatus: Record<TaskStatus, number>;
  byPriority: Record<TaskPriority, number>;
  byType: Record<TaskType, number>;
  overdueTasks: number;
  dueTodayTasks: number;
  completedThisWeek: number;
  averageCompletionTime: number;
  completionRate: number;

  // Propriétés calculées pour compatibilité frontend
  completedTasks?: number;
  inProgressTasks?: number;
  tasksByStatus?: Array<{
    status: TaskStatus;
    count: number;
  }>;
  tasksByPriority?: Array<{
    priority: TaskPriority;
    count: number;
  }>;
  tasksByType?: Array<{
    type: TaskType;
    count: number;
  }>;
  productivityMetrics?: {
    tasksCompletedThisWeek: number;
    tasksCompletedLastWeek: number;
    averageTasksPerDay: number;
  };
}

@Injectable({
  providedIn: 'root',
})
export class TaskService {
  private apiUrl = `${environment.API_PREFIX}/tasks`;

  constructor(private http: HttpClient) {}

  // Task methods

  /**
   * Creates a new task
   */
  createTask(taskData: CreateTaskDto): Observable<Task> {
    return this.http.post<Task>(this.apiUrl, taskData).pipe(
      catchError((error) => {
        console.error('Error creating task', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets all tasks with filtering
   */
  getTasks(options?: TaskQueryOptions): Observable<Task[]> {
    let params = new HttpParams();

    if (options) {
      if (options.page) params = params.set('page', options.page.toString());
      if (options.limit) params = params.set('limit', options.limit.toString());
      if (options.search) params = params.set('search', options.search);
      if (options.status) params = params.set('status', options.status);
      if (options.priority) params = params.set('priority', options.priority);
      if (options.type) params = params.set('type', options.type);
      if (options.assigneeId)
        params = params.set('assigneeId', options.assigneeId);
      if (options.assignedById)
        params = params.set('assignedById', options.assignedById);
      if (options.projectId)
        params = params.set('projectId', options.projectId);
      if (options.dueDate) params = params.set('dueDate', options.dueDate);
      if (options.tags && options.tags.length > 0) {
        params = params.set('tags', options.tags.join(','));
      }
    }

    return this.http.get<Task[]>(this.apiUrl, { params }).pipe(
      catchError((error) => {
        console.error('Error fetching tasks', error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets a single task by ID
   */
  getTask(taskId: string): Observable<Task> {
    return this.http.get<Task>(`${this.apiUrl}/${taskId}`).pipe(
      catchError((error) => {
        console.error(`Error fetching task ${taskId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates a task
   */
  updateTask(taskId: string, updateData: UpdateTaskDto): Observable<Task> {
    return this.http.patch<Task>(`${this.apiUrl}/${taskId}`, updateData).pipe(
      catchError((error) => {
        console.error(`Error updating task ${taskId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Deletes a task
   */
  deleteTask(taskId: string): Observable<Task> {
    return this.http.delete<Task>(`${this.apiUrl}/${taskId}`).pipe(
      catchError((error) => {
        console.error(`Error deleting task ${taskId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates task progress
   */
  updateTaskProgress(taskId: string, progress: number): Observable<Task> {
    const data = { progress };
    return this.http
      .patch<Task>(`${this.apiUrl}/${taskId}/progress`, data)
      .pipe(
        catchError((error) => {
          console.error(`Error updating task progress ${taskId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Assigns a task to a user
   */
  assignTask(taskId: string, assigneeId: string): Observable<Task> {
    const data = { assigneeId };
    return this.http.patch<Task>(`${this.apiUrl}/${taskId}/assign`, data).pipe(
      catchError((error) => {
        console.error(`Error assigning task ${taskId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Completes a task
   */
  completeTask(taskId: string, actualHours?: number): Observable<Task> {
    const data = { actualHours };
    return this.http
      .patch<Task>(`${this.apiUrl}/${taskId}/complete`, data)
      .pipe(
        catchError((error) => {
          console.error(`Error completing task ${taskId}`, error);
          return throwError(error);
        })
      );
  }

  // Task Comment methods

  /**
   * Adds a comment to a task
   */
  addComment(commentData: CreateTaskCommentDto): Observable<TaskComment> {
    return this.http
      .post<TaskComment>(`${this.apiUrl}/comments`, commentData)
      .pipe(
        catchError((error) => {
          console.error('Error adding task comment', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets comments for a task
   */
  getTaskComments(taskId: string): Observable<TaskComment[]> {
    return this.http
      .get<TaskComment[]>(`${this.apiUrl}/${taskId}/comments`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching comments for task ${taskId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates a task comment
   */
  updateComment(commentId: string, content: string): Observable<TaskComment> {
    const data = { content };
    return this.http
      .patch<TaskComment>(`${this.apiUrl}/comments/${commentId}`, data)
      .pipe(
        catchError((error) => {
          console.error(`Error updating comment ${commentId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a task comment
   */
  deleteComment(commentId: string): Observable<TaskComment> {
    return this.http
      .delete<TaskComment>(`${this.apiUrl}/comments/${commentId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error deleting comment ${commentId}`, error);
          return throwError(error);
        })
      );
  }

  // Task Project methods

  /**
   * Creates a new task project
   */
  createProject(projectData: CreateTaskProjectDto): Observable<TaskProject> {
    return this.http
      .post<TaskProject>(`${this.apiUrl}/projects`, projectData)
      .pipe(
        catchError((error) => {
          console.error('Error creating task project', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets all task projects
   */
  getProjects(companyId: string): Observable<TaskProject[]> {
    const params = new HttpParams().set('companyId', companyId);
    return this.http
      .get<TaskProject[]>(`${this.apiUrl}/projects`, { params })
      .pipe(
        catchError((error) => {
          console.error('Error fetching task projects', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets a single task project by ID
   */
  getProject(projectId: string): Observable<TaskProject> {
    return this.http
      .get<TaskProject>(`${this.apiUrl}/projects/${projectId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching task project ${projectId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates a task project
   */
  updateProject(
    projectId: string,
    updateData: UpdateTaskProjectDto
  ): Observable<TaskProject> {
    return this.http
      .patch<TaskProject>(`${this.apiUrl}/projects/${projectId}`, updateData)
      .pipe(
        catchError((error) => {
          console.error(`Error updating task project ${projectId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a task project
   */
  deleteProject(projectId: string): Observable<TaskProject> {
    return this.http
      .delete<TaskProject>(`${this.apiUrl}/projects/${projectId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error deleting task project ${projectId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets task statistics
   */
  getTaskStatistics(
    companyId: string,
    userId?: string
  ): Observable<TaskStatistics> {
    let params = new HttpParams().set('companyId', companyId);
    if (userId) {
      params = params.set('userId', userId);
    }

    return this.http
      .get<TaskStatistics>(`${this.apiUrl}/statistics/${companyId}`)
      .pipe(
        catchError((error) => {
          console.error('Error fetching task statistics', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets tasks assigned to a user
   */
  getUserTasks(userId: string, status?: TaskStatus): Observable<Task[]> {
    let params = new HttpParams().set('assigneeId', userId);
    if (status) {
      params = params.set('status', status);
    }

    return this.http.get<Task[]>(this.apiUrl, { params }).pipe(
      catchError((error) => {
        console.error(`Error fetching tasks for user ${userId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Gets overdue tasks
   */
  getOverdueTasks(companyId: string): Observable<Task[]> {
    const params = new HttpParams()
      .set('companyId', companyId)
      .set('overdue', 'true');
    return this.http.get<Task[]>(`${this.apiUrl}/overdue`, { params }).pipe(
      catchError((error) => {
        console.error('Error fetching overdue tasks', error);
        return throwError(error);
      })
    );
  }

  /**
   * Uploads task attachment
   */
  uploadAttachment(taskId: string, file: File): Observable<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http
      .post<{ url: string }>(`${this.apiUrl}/${taskId}/attachments`, formData)
      .pipe(
        catchError((error) => {
          console.error(`Error uploading attachment for task ${taskId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Exports task data
   */
  export(companyId: string, format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    const params = new HttpParams()
      .set('companyId', companyId)
      .set('format', format);
    return this.http
      .get(`${this.apiUrl}/export`, {
        params,
        responseType: 'blob',
      })
      .pipe(
        catchError((error) => {
          console.error('Error exporting tasks', error);
          return throwError(error);
        })
      );
  }
}
