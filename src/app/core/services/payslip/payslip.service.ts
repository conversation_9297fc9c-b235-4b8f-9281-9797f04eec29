// payslip.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { EmployeeData } from 'src/app/modules/dashboard/models/employee';

export interface Payslip {
  id?: string;
  month: number;
  year: number;
  grossSalary: number;
  taxDeductions: number;
  socialSecurity: number;
  otherDeductions: number;
  netSalary: number;
  employeeId: string;
  employee?: Partial<EmployeeData> | null;
}

export interface CreatePayslipDto {
  month: number;
  year: number;
  grossSalary: number;
  taxDeductions: number;
  socialSecurity: number;
  otherDeductions: number;
  netSalary: number;
  employeeId: string;
}

export interface UpdatePayslipDto {
  month?: number;
  year?: number;
  grossSalary?: number;
  taxDeductions?: number;
  socialSecurity?: number;
  otherDeductions?: number;
  netSalary?: number;
}

@Injectable({
  providedIn: 'root',
})
export class PayslipService {
  downloadPayslip(payslipId: string | undefined): Observable<Blob> {
    if (!payslipId) {
      throw new Error('Payslip ID est requis');
    }

    const url = `${this.apiUrl}/download/${payslipId}`;
    return this.http.get(url, { responseType: 'blob' }); // Retourne un Blob (fichier PDF)
  }
  private apiUrl = `${environment.API_PREFIX}/payslips`;

  constructor(private http: HttpClient) {}

  /**
   * Crée une nouvelle fiche de paie
   * @param payslipData Les données de la fiche de paie à créer
   * @returns Une observable de la fiche de paie créée
   */
  createPayslip(payslipData: CreatePayslipDto): Observable<Payslip> {
    return this.http
      .post<Payslip>(this.apiUrl, payslipData)
      .pipe(catchError(this.handleError));
  }

  /**
   * Récupère toutes les fiches de paie d'un employé
   * @param employeeId L'ID de l'employé
   * @returns Une observable de la liste des fiches de paie
   */
  getEmployeePayslips(employeeId: string): Observable<Payslip[]> {
    return this.http
      .get<Payslip[]>(`${this.apiUrl}/${employeeId}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Récupère une fiche de paie par son ID
   * @param payslipId L'ID de la fiche de paie
   * @returns Une observable de la fiche de paie
   */
  getPayslipById(payslipId: number): Observable<Payslip> {
    return this.http
      .get<Payslip>(`${this.apiUrl}/${payslipId}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Met à jour une fiche de paie
   * @param payslipId L'ID de la fiche de paie à mettre à jour
   * @param payslipData Les données de mise à jour
   * @returns Une observable de la fiche de paie mise à jour
   */
  updatePayslip(
    payslipId: string,
    payslipData: UpdatePayslipDto
  ): Observable<Payslip> {
    return this.http
      .patch<Payslip>(`${this.apiUrl}/${payslipId}`, payslipData)
      .pipe(catchError(this.handleError));
  }

  /**
   * Supprime une fiche de paie
   * @param payslipId L'ID de la fiche de paie à supprimer
   * @returns Une observable du résultat de la suppression
   */
  deletePayslip(payslipId: string): Observable<any> {
    return this.http
      .delete<any>(`${this.apiUrl}/${payslipId}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Calcule le total des déductions pour une fiche de paie
   * @param payslip La fiche de paie
   * @returns Le montant total des déductions
   */
  calculateTotalDeductions(payslip: Payslip): number {
    return (
      payslip.taxDeductions + payslip.socialSecurity + payslip.otherDeductions
    );
  }

  /**
   * Calcule le salaire net basé sur le salaire brut et les déductions
   * @param grossSalary Le salaire brut
   * @param taxDeductions Les déductions fiscales
   * @param socialSecurity Les cotisations de sécurité sociale
   * @param otherDeductions Les autres déductions
   * @returns Le salaire net calculé
   */
  calculateNetSalary(
    grossSalary: number,
    taxDeductions: number,
    socialSecurity: number,
    otherDeductions: number
  ): number {
    return grossSalary - taxDeductions - socialSecurity - otherDeductions;
  }

  /**
   * Gère les erreurs HTTP
   * @param error L'erreur HTTP
   * @returns Une observable d'erreur
   */
  private handleError(error: HttpErrorResponse) {
    let errorMessage =
      'Une erreur est survenue lors du traitement de votre demande.';

    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      errorMessage = `Code: ${error.status}, Message: ${
        error.error.message || error.statusText
      }`;
    }

    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
