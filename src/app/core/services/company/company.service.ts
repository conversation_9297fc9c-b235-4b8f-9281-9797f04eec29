import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Store } from '@ngrx/store';
import { AppState } from '../../store/app.state';

import {
  loadUserFailure,
  loadUserSuccess,
} from '../../store/user/user.actions';
import { loadCompanySuccess } from '../../store/company/company.actions';
import {
  resetForm,
  updateForm,
  updateStep,
} from '../../store/company/company-form.actions';

export interface Address {
  id: string;
  street: string;
  city: string;
  postalCode: string;
  country: string;
}

export interface CompanyTaxSettings {
  id: string;
  incomeTaxRate: number;
  socialSecurityRate: number;
  unEmploymentInsuranceRate: number;
  healthInsuranceRate: number;
  pensionContributionRate: number;
  incomeTaxThreshold?: number;
  socialSecurityThreshold?: number;
  standardDeduction?: number;
  familyAllowance?: number;
}

export enum PayrollCycleEnum {
  WEEKLY = 'WEEKLY',
  BIWEEKLY = 'BIWEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
}

export interface PayrollConfiguration {
  id: string;
  payrollCycle: PayrollCycleEnum;
  paymentDay: number;
  overtimeMultiplier: number;
  maxOvertimeHours?: number;
  bonusType?: string;
  performanceBonusRate?: number;
  paidTimeOffAccrualRate?: number;
}

export interface Company {
  id: string;
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website?: string;
  logo?: string;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
  address?: Address;
  taxSettings?: CompanyTaxSettings;
  payrollConfiguration?: PayrollConfiguration;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCompanyDto {
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website?: string;
  logo?: string;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
  address?: Omit<Address, 'id'>;
}

export interface UpdateCompanyDto {
  companyName?: string;
  email?: string;
  phoneNumbers?: string[];
  website?: string;
  logo?: string;
  officialName?: string;
  taxIdentificationNumber?: string;
  industry?: string;
  description?: string;
  address?: Partial<Omit<Address, 'id'>>;
}

@Injectable({
  providedIn: 'root',
})
export class CompanyService {
  private apiUrl = `${environment.API_PREFIX}/companies`;
  private userApiUrl = `${environment.API_PREFIX}/auth/me`;
  constructor(private http: HttpClient, private store: Store<AppState>) {}

  /**
   * Creates a new company with the provided data and retrieves the user and company info after successful creation.
   * @param companyData - The company data.
   * @returns An Observable containing the server response.
   */
  createCompany(companyData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}`, companyData).pipe(
      switchMap(() => {
        // Après la création de l'entreprise, récupère les informations de l'utilisateur
        return this.http.get(this.userApiUrl).pipe(
          switchMap((userResponse: any) => {
            // Dispatch de l'utilisateur dans le store
            this.store.dispatch(loadUserSuccess({ user: userResponse }));

            // Après, récupère les informations de l'entreprise
            return this.getCompanyById(userResponse.companyId);
          }),
          catchError((error) => {
            console.error(
              'Error retrieving user or company after creation',
              error
            );
            this.store.dispatch(loadUserFailure({ error }));
            return throwError(error);
          })
        );
      }),
      switchMap((companyResponse: any) => {
        // Dispatch des informations de la company dans le store
        this.store.dispatch(loadCompanySuccess({ company: companyResponse }));

        // Réinitialise le formulaire après la création de l'entreprise
        this.store.dispatch(resetForm());
        return of(companyResponse); // Retourne les données de la compagnie
      }),
      catchError((error) => {
        console.error('Error creating company', error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates an existing company.
   * @param companyId - The ID of the company.
   * @param companyData - The new company data.
   * @returns An Observable containing the server response.
   */
  updateCompany(companyId: string, companyData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/${companyId}`, companyData).pipe(
      catchError((error) => {
        console.error('Error updating company', error);
        return throwError(error);
      })
    );
  }

  /**
   * Retrieves the details of a company.
   * @param companyId - The ID of the company.
   * @returns An Observable containing the company details.
   */
  getCompanyById(companyId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${companyId}`).pipe(
      catchError((error) => {
        console.error('Error fetching company details', error);
        return throwError(error);
      })
    );
  }

  /**
   * Updates the current step of the form in the store.
   * @param step - The new step.
   */
  updateStep(step: number): void {
    this.store.dispatch(updateStep({ step }));
  }

  /**
   * Saves the form data to the store.
   * @param formData - The form data.
   */
  saveFormData(formData: any): void {
    this.store.dispatch(updateForm({ form: formData }));
  }
}
