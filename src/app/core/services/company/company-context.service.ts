import { Injectable } from '@angular/core';
import { Observable, map, filter, take, switchMap, of } from 'rxjs';
import { Store, select } from '@ngrx/store';
import { AppState } from '../../store/app.state';
import { selectCurrentCompanyId } from '../../store/company/company.selector';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root',
})
export class CompanyContextService {
  constructor(
    private store: Store<AppState>,
    private authService: AuthService
  ) {}

  /**
   * Récupère le companyId actuel depuis le store ou localStorage
   * @returns Observable<string> - Le companyId ou null si non disponible
   */
  getCurrentCompanyId(): Observable<string | null> {
    return this.store.pipe(
      select(selectCurrentCompanyId),
      map((companyId) => {
        // Si pas de companyId dans le store, essayer localStorage
        if (!companyId) {
          return this.authService.getCurrentCompanyId();
        }
        return companyId;
      })
    );
  }

  /**
   * Récupère le companyId actuel et attend qu'il soit disponible
   * @returns Observable<string> - Le companyId (filtre les valeurs null)
   */
  getRequiredCompanyId(): Observable<string> {
    return this.getCurrentCompanyId().pipe(
      filter((id): id is string => !!id),
      take(1)
    );
  }

  /**
   * Exécute une fonction avec le companyId actuel
   * @param callback - Fonction à exécuter avec le companyId
   * @returns Observable du résultat de la fonction
   */
  withCompanyId<T>(
    callback: (companyId: string) => Observable<T>
  ): Observable<T> {
    return this.getRequiredCompanyId().pipe(
      switchMap((companyId) => callback(companyId))
    );
  }

  /**
   * Récupère le companyId de manière synchrone depuis localStorage
   * @returns Le companyId ou null si non disponible
   */
  getCurrentCompanyIdSync(): string | null {
    const companyId = this.authService.getCurrentCompanyId();
    console.log('CompanyContextService - getCurrentCompanyIdSync:', companyId);
    return companyId;
  }
}
