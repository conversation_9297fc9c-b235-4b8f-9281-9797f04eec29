import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import {
  ApplicationResponse,
  CreateApplicationResponseDto,
} from './job-offer.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ApplicationResponseService {
  private apiUrl = `${environment.API_PREFIX}/application-responses`;

  constructor(private http: HttpClient) {}

  create(
    createApplicationResponseDto: CreateApplicationResponseDto
  ): Observable<ApplicationResponse> {
    return this.http.post<ApplicationResponse>(
      this.apiUrl,
      createApplicationResponseDto
    );
  }

  findAll(applicationId:string): Observable<ApplicationResponse[]> {
    return this.http.get<ApplicationResponse[]>(`${this.apiUrl}/${applicationId}`);
  }

  findOne(id: string): Observable<ApplicationResponse> {
    return this.http.get<ApplicationResponse>(`${this.apiUrl}/${id}`);
  }

  findByApplication(applicationId: string): Observable<ApplicationResponse[]> {
    return this.http.get<ApplicationResponse[]>(
      `${this.apiUrl}/application/${applicationId}`
    );
  }
}
