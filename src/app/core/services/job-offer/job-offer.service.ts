import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import {
  CreateJobOfferDto,
  JobOffer,
  UpdateJobOfferDto,
} from './job-offer.model';
import { environment } from 'src/environments/environment';

export enum JobOfferStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CLOSED = 'CLOSED',
  PAUSED = 'PAUSED',
  ARCHIVED = 'ARCHIVED',
}

export enum ApplicationStatus {
  PENDING = 'PENDING',
  UNDER_REVIEW = 'UNDER_REVIEW',
  SHORTLISTED = 'SHORTLISTED',
  INTERVIEWED = 'INTERVIEWED',
  OFFERED = 'OFFERED',
  HIRED = 'HIRED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN',
}

export interface JobApplication {
  id: string;
  jobOfferId: string;
  candidateName: string;
  candidateEmail: string;
  candidatePhone?: string;
  resumeUrl?: string;
  coverLetter?: string;
  status: ApplicationStatus;
  appliedAt: string;
  reviewedAt?: string;
  interviewedAt?: string;
  notes?: string;
  rating?: number;
  jobOffer?: {
    id: string;
    title: string;
  };
  createdAt: string;
  updatedAt: string;
}

@Injectable({
  providedIn: 'root',
})
export class JobOffersService {
  private apiBaseUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  create(
    companyId: string,
    createJobOfferDto: CreateJobOfferDto
  ): Observable<JobOffer> {
    return this.http.post<JobOffer>(
      `${this.apiBaseUrl}/${companyId}/job-offers`,
      createJobOfferDto
    );
  }

  findAll(companyId?: string): Observable<JobOffer[]> {
    if (companyId) {
      return this.http.get<JobOffer[]>(
        `${this.apiBaseUrl}/${companyId}/job-offers`
      );
    }
    // Pour récupérer toutes les offres sans filtrer par entreprise
    // Note: Cette fonctionnalité doit être supportée par le backend
    return this.http.get<JobOffer[]>(`${environment.API_PREFIX}/job-offers`);
  }

  findOne(companyId: string, id: string): Observable<JobOffer> {
    return this.http.get<JobOffer>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${id}`
    );
  }

  update(
    companyId: string,
    id: string,
    updateJobOfferDto: UpdateJobOfferDto
  ): Observable<JobOffer> {
    return this.http.patch<JobOffer>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${id}`,
      updateJobOfferDto
    );
  }

  remove(companyId: string, id: string): Observable<void> {
    return this.http.delete<void>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${id}`
    );
  }

  saveJobOffer(companyId: string, jobOfferId: string): Observable<void> {
    return this.http.post<void>(
      `${this.apiBaseUrl}/${companyId}/job-offers/${jobOfferId}/save`,
      {}
    );
  }

  getSavedJobOffers(companyId: string): Observable<JobOffer[]> {
    return this.http.get<JobOffer[]>(
      `${this.apiBaseUrl}/${companyId}/job-offers/saved`
    );
  }

  /**
   * Publishes a job offer
   */
  publish(companyId: string, id: string): Observable<JobOffer> {
    return this.http
      .patch<JobOffer>(
        `${this.apiBaseUrl}/${companyId}/job-offers/${id}/publish`,
        {}
      )
      .pipe(
        catchError((error) => {
          console.error(`Error publishing job offer ${id}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Closes a job offer
   */
  close(companyId: string, id: string): Observable<JobOffer> {
    return this.http
      .patch<JobOffer>(
        `${this.apiBaseUrl}/${companyId}/job-offers/${id}/close`,
        {}
      )
      .pipe(
        catchError((error) => {
          console.error(`Error closing job offer ${id}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets job offer statistics
   */
  getStatistics(companyId: string): Observable<any> {
    return this.http
      .get(`${this.apiBaseUrl}/${companyId}/job-offers/statistics`)
      .pipe(
        catchError((error) => {
          console.error('Error fetching job offer statistics', error);
          return throwError(error);
        })
      );
  }

  /**
   * Creates a new job application
   */
  createApplication(
    companyId: string,
    applicationData: any,
    resumeFile?: File
  ): Observable<JobApplication> {
    const formData = new FormData();
    formData.append('data', JSON.stringify(applicationData));
    if (resumeFile) {
      formData.append('resume', resumeFile);
    }

    return this.http
      .post<JobApplication>(
        `${this.apiBaseUrl}/${companyId}/job-applications`,
        formData
      )
      .pipe(
        catchError((error) => {
          console.error('Error creating job application', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets all job applications with filtering
   */
  getApplications(
    companyId: string,
    jobOfferId?: string
  ): Observable<JobApplication[]> {
    let params = new HttpParams();
    if (jobOfferId) {
      params = params.set('jobOfferId', jobOfferId);
    }

    return this.http
      .get<JobApplication[]>(
        `${this.apiBaseUrl}/${companyId}/job-applications`,
        { params }
      )
      .pipe(
        catchError((error) => {
          console.error('Error fetching job applications', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets a single job application by ID
   */
  getApplication(
    companyId: string,
    applicationId: string
  ): Observable<JobApplication> {
    return this.http
      .get<JobApplication>(
        `${this.apiBaseUrl}/${companyId}/job-applications/${applicationId}`
      )
      .pipe(
        catchError((error) => {
          console.error(
            `Error fetching job application ${applicationId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Updates a job application
   */
  updateApplication(
    companyId: string,
    applicationId: string,
    updateData: any
  ): Observable<JobApplication> {
    return this.http
      .patch<JobApplication>(
        `${this.apiBaseUrl}/${companyId}/job-applications/${applicationId}`,
        updateData
      )
      .pipe(
        catchError((error) => {
          console.error(
            `Error updating job application ${applicationId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Downloads a candidate's resume
   */
  downloadResume(companyId: string, applicationId: string): Observable<Blob> {
    return this.http
      .get(
        `${this.apiBaseUrl}/${companyId}/job-applications/${applicationId}/resume`,
        {
          responseType: 'blob',
        }
      )
      .pipe(
        catchError((error) => {
          console.error(
            `Error downloading resume for application ${applicationId}`,
            error
          );
          return throwError(error);
        })
      );
  }

  /**
   * Searches job offers
   */
  search(companyId: string, query: string): Observable<JobOffer[]> {
    const params = new HttpParams().set('search', query);
    return this.http
      .get<JobOffer[]>(`${this.apiBaseUrl}/${companyId}/job-offers/search`, {
        params,
      })
      .pipe(
        catchError((error) => {
          console.error('Error searching job offers', error);
          return throwError(error);
        })
      );
  }

  /**
   * Exports job offer data
   */
  export(companyId: string, format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    const params = new HttpParams().set('format', format);
    return this.http
      .get(`${this.apiBaseUrl}/${companyId}/job-offers/export`, {
        params,
        responseType: 'blob',
      })
      .pipe(
        catchError((error) => {
          console.error('Error exporting job offers', error);
          return throwError(error);
        })
      );
  }
}
