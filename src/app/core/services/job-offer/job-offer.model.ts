export interface JobOffer {
  id: string;
  title: string;
  description: string;
  publishDate: Date;
  expirationDate: Date;
  status?: JobOfferStatusEnum;
  location: string;
  contractTypes: ContractTypeEnum[];
  minSalary?: number;
  maxSalary?: number;
  requiredSkills?: string[];
  departmentId?: string;
  positionId?: string;
  companyId: string;
  company?: any;
  applications?: Application[];
  savedByUsers?: any[];
}

export interface CreateJobOfferDto {
  title: string;
  description: string;
  publishDate: Date;
  expirationDate: Date;
  status?: JobOfferStatusEnum;
  location: string;
  contractTypes: ContractTypeEnum[];
  minSalary?: number;
  maxSalary?: number;
  requiredSkills?: string[];
  departmentId?: string;
  positionId?: string;
}

export interface UpdateJobOfferDto extends Partial<CreateJobOfferDto> {}

// enums.ts
export enum ContractTypeEnum {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  TEMPORARY = 'TEMPORARY',
  INTERNSHIP = 'INTERNSHIP',
  REMOTE = 'REMOTE',
  FREELANCE = 'FREELANCE',
  CONTRACT = 'CONTRACT',
}

export enum JobOfferStatusEnum {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
  EXPIRED = 'EXPIRED',
  FILLED = 'FILLED',
}

export enum ApplicationStatusEnum {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

// application.model.ts

export interface Application {
  id: string;
  applicationDate: Date;
  status: ApplicationStatusEnum;
  jobId: string;
  userId: string;
  coverLetter: string;
  resume: string;
  references?: string;
  additionalDocuments?: string[];
  preferredStartDate?: Date;
  currentEmploymentStatus?: string;
  desiredSalary?: number;
  user?: any;
  jobOffer?: any;
  applicationResponses?: ApplicationResponse[];
}

export interface CreateApplicationDto {
  jobId: string;
  coverLetter: string;
  resume: string;
  references?: string;
  additionalDocuments?: string[];
  preferredStartDate?: Date;
  currentEmploymentStatus?: string;
  desiredSalary?: number;
  status?: ApplicationStatusEnum;
}

export interface UpdateApplicationDto extends Partial<CreateApplicationDto> {}

// application-response.model.ts
export interface ApplicationResponse {
  id: string;
  applicationResponseDate: Date;
  content: string;
  applicationId: string;
  application?: any;
}

export interface CreateApplicationResponseDto {
  applicationId: string;
  content: string;
}
