import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import {
  Application,
  CreateApplicationDto,
  UpdateApplicationDto,
} from './job-offer.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ApplicationService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  create(createApplicationDto: CreateApplicationDto): Observable<Application> {
    return this.http.post<Application>(this.apiUrl, createApplicationDto);
  }

  findAll(companyId: string): Observable<Application[]> {
    return this.http.get<Application[]>(
      `${this.apiUrl}/${companyId}/applications`
    );
  }

  findOne(id: string): Observable<Application> {
    return this.http.get<Application>(`${this.apiUrl}/${id}`);
  }

  update(
    id: string,
    updateApplicationDto: UpdateApplicationDto
  ): Observable<Application> {
    return this.http.patch<Application>(
      `${this.apiUrl}/${id}`,
      updateApplicationDto
    );
  }

  remove(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
  findByUser(userId: string): Observable<Application[]> {
    return this.http.get<Application[]>(`${this.apiUrl}/user/${userId}`);
  }

  findByJobOffer(jobOfferId: string): Observable<Application[]> {
    return this.http.get<Application[]>(
      `${this.apiUrl}/job-offer/${jobOfferId}`
    );
  }

  updateStatus(id: string, status: string): Observable<Application> {
    return this.http.patch<Application>(`${this.apiUrl}/${id}/status`, {
      status,
    });
  }
}
