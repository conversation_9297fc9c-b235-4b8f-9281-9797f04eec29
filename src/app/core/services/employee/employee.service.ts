import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  CreateEmployeeDto,
  UpdateEmployeeDto,
  Employee,
  EmployeeQueryOptions,
  EmployeeStatistics,
  PaginatedResult,
} from '../../models/employee.model';

@Injectable({
  providedIn: 'root',
})
export class EmployeeService {
  private baseUrl = `${environment.API_PREFIX}/companies`;
  private authBaseUrl = `${environment.API_PREFIX}/auth`;

  constructor(private http: HttpClient) {}

  /**
   * Retrieves the list of employees for a specific company with pagination and filtering.
   * @param companyId - The ID of the company
   * @param options - Query options including pagination and filters
   * @returns An Observable containing the paginated employees list
   */
  getEmployees(
    companyId: string,
    options?: EmployeeQueryOptions
  ): Observable<PaginatedResult<Employee>> {
    let params = new HttpParams();
    console.log(options);
    if (options) {
      if (options.pagination?.page)
        params = params.set('page', options.pagination.page.toString());
      if (options.pagination?.limit)
        params = params.set('limit', options.pagination.limit.toString());
      if (options.search) params = params.set('search', options.search);
      if (options.departmentId)
        params = params.set('departmentId', options.departmentId);
      if (options.positionId)
        params = params.set('positionId', options.positionId);
      if (options.status) params = params.set('status', options.status);
      if (options.employmentType)
        params = params.set('employmentType', options.employmentType);
      if (options.includeRelations) {
        const relations = Object.entries(options.includeRelations)
          .filter(([_, value]) => value)
          .map(([key, _]) => key)
          .join(',');

        if (relations) {
          params = params.set('includeRelations', relations);
        }
      }
    }
    return this.http
      .get<PaginatedResult<Employee>>(
        `${this.baseUrl}/${companyId}/employees`,
        { params }
      )
      .pipe(
        catchError((error) => {
          console.error('Error fetching employees', error);
          return throwError(error);
        })
      );
  }

  /**
   * Retrieves the details of an employee by ID.
   * @param companyId - The ID of the company
   * @param employeeId - The ID of the employee
   * @returns An Observable containing the employee details
   */
  getEmployeeById(companyId: string, employeeId: string): Observable<Employee> {
    return this.http
      .get<Employee>(`${this.baseUrl}/${companyId}/employees/${employeeId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching employee ${employeeId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Creates a new employee in a company.
   * @param companyId - The ID of the company
   * @param employeeData - The employee data
   * @returns An Observable containing the created employee
   */
  createEmployee(
    companyId: string,
    employeeData: CreateEmployeeDto
  ): Observable<Employee> {
    return this.http
      .post<Employee>(`${this.baseUrl}/${companyId}/employees`, employeeData)
      .pipe(
        catchError((error) => {
          console.error('Error creating employee', error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates an existing employee.
   * @param companyId - The ID of the company
   * @param employeeId - The ID of the employee
   * @param employeeData - The updated employee data
   * @returns An Observable containing the updated employee
   */
  updateEmployee(
    companyId: string,
    employeeId: string,
    employeeData: UpdateEmployeeDto
  ): Observable<Employee> {
    return this.http
      .patch<Employee>(
        `${this.baseUrl}/${companyId}/employees/${employeeId}`,
        employeeData
      )
      .pipe(
        catchError((error) => {
          console.error(`Error updating employee ${employeeId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Retrieves comprehensive employee statistics for a company.
   * @param companyId - The ID of the company
   * @returns An Observable containing the employee statistics
   */
  getEmployeeStatistics(companyId: string): Observable<EmployeeStatistics> {
    return this.http
      .get<EmployeeStatistics>(
        `${this.baseUrl}/${companyId}/employees/statistics/overview`
      )
      .pipe(
        catchError((error) => {
          console.error('Error fetching employee statistics', error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes an employee
   * @param companyId - The ID of the company
   * @param employeeId - The ID of the employee
   * @returns An Observable containing the deleted employee
   */
  deleteEmployee(companyId: string, employeeId: string): Observable<Employee> {
    return this.http
      .delete<Employee>(`${this.baseUrl}/${companyId}/employees/${employeeId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error deleting employee ${employeeId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Searches employees by name or email
   * @param companyId - The ID of the company
   * @param query - Search query
   * @returns An Observable containing matching employees
   */
  searchEmployees(companyId: string, query: string): Observable<Employee[]> {
    const params = new HttpParams().set('search', query);
    return this.http
      .get<Employee[]>(`${this.baseUrl}/${companyId}/employees/search`, {
        params,
      })
      .pipe(
        catchError((error) => {
          console.error('Error searching employees', error);
          return throwError(error);
        })
      );
  }

  /**
   * Retrieves monthly statistics: new hires, resigned, and on leave for each month of a year.
   * @param companyId - The ID of the company
   * @param year - Optional year for statistics (defaults to current year)
   * @returns An Observable containing the monthly statistics
   */
  getMonthlyEmployeeStatistics(
    companyId: string,
    year?: number
  ): Observable<{
    monthlyStats: {
      month: number;
      newlyHired: number;
      resigned: number;
      onLeave: number;
    }[];
  }> {
    const targetYear = year || new Date().getFullYear();
    const url = `${this.baseUrl}/${companyId}/employees/statistics/monthly?year=${targetYear}`;

    return this.http
      .get<{
        monthlyStats: {
          month: number;
          newlyHired: number;
          resigned: number;
          onLeave: number;
        }[];
      }>(url)
      .pipe(
        catchError((error) => {
          console.error('Error fetching monthly employee statistics', error);
          return throwError(error);
        })
      );
  }
  /**
   * Generates a new password for a user and sends it by email
   * @param userId - The ID of the user
   * @returns An Observable containing the server response
   */
  generateAndSendPassword(userId: string): Observable<{ message: string }> {
    return this.http
      .post<{ message: string }>(
        `${this.authBaseUrl}/generate-password/${userId}`,
        {}
      )
      .pipe(
        catchError((error) => {
          console.error('Error generating password', error);
          return throwError(error);
        })
      );
  }
}
