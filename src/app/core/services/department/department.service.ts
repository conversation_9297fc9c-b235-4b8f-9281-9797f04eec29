import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  UpdateDepartmentDto,
} from '../../models/company.model';

export interface Position {
  id: string;
  title: string;
  description?: string;
  departmentId: string;
  requiredSkills: string[];
  employees?: Array<{
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePositionDto {
  title: string;
  description?: string;
  departmentId: string;
  requiredSkills?: string[];
}

export interface UpdatePositionDto {
  title?: string;
  description?: string;
  requiredSkills?: string[];
}

@Injectable({
  providedIn: 'root',
})
export class DepartmentService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  createDepartment(
    companyId: string,
    data: CreateDepartmentDto
  ): Observable<DepartmentResponseDto> {
    return this.http.post<DepartmentResponseDto>(
      `${this.apiUrl}/${companyId}/departments`,
      data
    );
  }

  getAllDepartments(companyId: string): Observable<DepartmentResponseDto[]> {
    return this.http.get<DepartmentResponseDto[]>(
      `${this.apiUrl}/${companyId}/departments`
    );
  }

  getDepartmentById(
    companyId: string,
    id: string
  ): Observable<DepartmentResponseDto> {
    return this.http.get<DepartmentResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${id}`
    );
  }

  updateDepartment(
    companyId: string,
    id: string,
    data: UpdateDepartmentDto
  ): Observable<DepartmentResponseDto> {
    return this.http.put<DepartmentResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${id}`,
      data
    );
  }

  deleteDepartment(companyId: string, id: string): Observable<void> {
    return this.http.delete<void>(
      `${this.apiUrl}/${companyId}/departments/${id}`
    );
  }

  // Position methods

  /**
   * Creates a new position
   */
  createPosition(
    companyId: string,
    positionData: CreatePositionDto
  ): Observable<Position> {
    return this.http
      .post<Position>(`${this.apiUrl}/${companyId}/positions`, positionData)
      .pipe(
        catchError((error) => {
          console.error('Error creating position', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets all positions for a company
   */
  getPositions(
    companyId: string,
    departmentId?: string
  ): Observable<Position[]> {
    let params = new HttpParams();
    if (departmentId) {
      params = params.set('departmentId', departmentId);
    }

    return this.http
      .get<Position[]>(`${this.apiUrl}/${companyId}/positions`, { params })
      .pipe(
        catchError((error) => {
          console.error('Error fetching positions', error);
          return throwError(error);
        })
      );
  }

  /**
   * Gets a single position by ID
   */
  getPosition(companyId: string, positionId: string): Observable<Position> {
    return this.http
      .get<Position>(`${this.apiUrl}/${companyId}/positions/${positionId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error fetching position ${positionId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Updates a position
   */
  updatePosition(
    companyId: string,
    positionId: string,
    updateData: UpdatePositionDto
  ): Observable<Position> {
    return this.http
      .patch<Position>(
        `${this.apiUrl}/${companyId}/positions/${positionId}`,
        updateData
      )
      .pipe(
        catchError((error) => {
          console.error(`Error updating position ${positionId}`, error);
          return throwError(error);
        })
      );
  }

  /**
   * Deletes a position
   */
  deletePosition(companyId: string, positionId: string): Observable<Position> {
    return this.http
      .delete<Position>(`${this.apiUrl}/${companyId}/positions/${positionId}`)
      .pipe(
        catchError((error) => {
          console.error(`Error deleting position ${positionId}`, error);
          return throwError(error);
        })
      );
  }
}
