// position.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  CreatePositionDto,
  PositionResponseDto,
  UpdatePositionDto,
} from '../../models/company.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PositionService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  createPosition(
    companyId: string,
    departmentId: string,
    data: CreatePositionDto
  ): Observable<PositionResponseDto> {
    return this.http.post<PositionResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${departmentId}/positions`,
      data
    );
  }

  getAllPositions(
    companyId: string,
    departmentId: string
  ): Observable<PositionResponseDto[]> {
    return this.http.get<PositionResponseDto[]>(
      `${this.apiUrl}/${companyId}/departments/${departmentId}/positions`
    );
  }

  getPositionById(
    companyId: string,
    departmentId: string,
    id: string
  ): Observable<PositionResponseDto> {
    return this.http.get<PositionResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${departmentId}/positions/${id}`
    );
  }

  updatePosition(
    companyId: string,
    departmentId: string,
    id: string,
    data: UpdatePositionDto
  ): Observable<PositionResponseDto> {
    return this.http.put<PositionResponseDto>(
      `${this.apiUrl}/${companyId}/departments/${departmentId}/positions/${id}`,
      data
    );
  }

  deletePosition(
    companyId: string,
    departmentId: string,
    id: string
  ): Observable<void> {
    return this.http.delete<void>(
      `${this.apiUrl}/${companyId}/departments/${departmentId}/positions/${id}`
    );
  }
}
