import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import {
  Report,
  ReportTemplate,
  ReportRequest,
  ReportExportRequest,
  ReportType,
  ReportCategory,
  ExportFormat,
  EmployeeReport,
  AttendanceReport,
  PayrollReport,
} from '../../models/report.model';

@Injectable({
  providedIn: 'root',
})
export class ReportService {
  private reportsSubject = new BehaviorSubject<Report[]>([]);
  private templatesSubject = new BehaviorSubject<ReportTemplate[]>([]);

  public reports$ = this.reportsSubject.asObservable();
  public templates$ = this.templatesSubject.asObservable();

  constructor() {
    this.loadMockData();
  }

  // Gestion des rapports
  getReports(): Observable<Report[]> {
    return this.reports$;
  }

  getReportById(id: string): Observable<Report | null> {
    const report = this.reportsSubject.value.find((r) => r.id === id);
    return of(report || null);
  }

  generateReport(request: ReportRequest): Observable<Report> {
    const newReport: Report = {
      id: this.generateId(),
      name: request.name,
      description: request.description || '',
      type: ReportType.CUSTOM,
      category: ReportCategory.OPERATIONAL,
      templateId: request.templateId,
      parameters: Object.entries(request.parameters).map(([key, value]) => ({
        key,
        label: key,
        type: 'TEXT' as any,
        required: false,
        defaultValue: value,
      })),
      filters: request.filters,
      columns: [],
      charts: [],
      data: this.generateMockData(request),
      summary: {
        totalRecords: 100,
        aggregations: {},
        trends: [],
        insights: [
          'Données générées automatiquement',
          'Rapport créé avec succès',
        ],
      },
      metadata: {
        generatedAt: new Date(),
        generatedBy: 'current-user-id',
        executionTime: 1.5,
        dataSource: 'LuminaHR Database',
        recordCount: 100,
        filters: request.filters.map(
          (f) => `${f.field} ${f.operator} ${f.value}`
        ),
        version: '1.0',
      },
      status: 'COMPLETED' as any,
      visibility: 'PRIVATE' as any,
      isScheduled: false,
      availableFormats: [
        ExportFormat.PDF,
        ExportFormat.EXCEL,
        ExportFormat.CSV,
      ],
      createdBy: 'current-user-id',
      createdAt: new Date(),
      lastGeneratedAt: new Date(),
      generationCount: 1,
      exportCount: 0,
    };

    const reports = [...this.reportsSubject.value, newReport];
    this.reportsSubject.next(reports);

    return of(newReport);
  }

  // Gestion des templates
  getReportTemplates(): Observable<ReportTemplate[]> {
    return this.templates$;
  }

  getTemplateById(id: string): Observable<ReportTemplate | null> {
    const template = this.templatesSubject.value.find((t) => t.id === id);
    return of(template || null);
  }

  // Export de rapports
  exportReport(request: ReportExportRequest): Observable<Blob> {
    const report = this.reportsSubject.value.find(
      (r) => r.id === request.reportId
    );
    if (!report) {
      throw new Error('Rapport non trouvé');
    }

    // Simuler la génération du fichier selon le format
    let content = '';
    let mimeType = '';

    switch (request.format) {
      case ExportFormat.PDF:
        content = this.generatePDFContent(report, request);
        mimeType = 'application/pdf';
        break;
      case ExportFormat.EXCEL:
        content = this.generateExcelContent(report, request);
        mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case ExportFormat.CSV:
        content = this.generateCSVContent(report, request);
        mimeType = 'text/csv';
        break;
      case ExportFormat.HTML:
        content = this.generateHTMLContent(report, request);
        mimeType = 'text/html';
        break;
      default:
        content = JSON.stringify(report.data, null, 2);
        mimeType = 'application/json';
    }

    // Mettre à jour le compteur d'export
    this.updateReport(request.reportId, {
      exportCount: report.exportCount + 1,
      lastExportedAt: new Date(),
    });

    const blob = new Blob([content], { type: mimeType });
    return of(blob);
  }

  // Rapports prédéfinis pour RH
  generateEmployeeReport(dateRange?: {
    from: Date;
    to: Date;
  }): Observable<EmployeeReport> {
    // Simuler la génération d'un rapport d'employés
    const report: EmployeeReport = {
      totalEmployees: 150,
      activeEmployees: 142,
      newHires: 8,
      terminations: 3,
      byDepartment: {
        IT: 45,
        Sales: 35,
        Marketing: 25,
        HR: 15,
        Finance: 22,
        Operations: 8,
      },
      byPosition: {
        Developer: 30,
        'Sales Rep': 25,
        Manager: 20,
        Analyst: 18,
        Coordinator: 15,
        Director: 8,
        Other: 34,
      },
      averageAge: 32.5,
      genderDistribution: {
        Male: 78,
        Female: 64,
        Other: 8,
      },
      tenureDistribution: {
        '0-1 years': 45,
        '1-3 years': 52,
        '3-5 years': 28,
        '5-10 years': 18,
        '10+ years': 7,
      },
    };

    return of(report);
  }

  generateAttendanceReport(dateRange: {
    from: Date;
    to: Date;
  }): Observable<AttendanceReport> {
    // Simuler la génération d'un rapport de présence
    const report: AttendanceReport = {
      totalWorkingDays: 22,
      averageAttendance: 94.5,
      lateArrivals: 15,
      earlyDepartures: 8,
      absences: 12,
      overtimeHours: 145,
      byEmployee: [
        {
          employeeId: 'emp_1',
          employeeName: 'Jean Dupont',
          workingDays: 22,
          presentDays: 21,
          absentDays: 1,
          lateCount: 2,
          overtimeHours: 8,
          attendanceRate: 95.5,
        },
      ],
      trends: [
        {
          date: '2024-01-01',
          attendanceRate: 95.2,
          totalEmployees: 150,
          presentEmployees: 143,
        },
      ],
    };

    return of(report);
  }

  generatePayrollReport(period: {
    month: number;
    year: number;
  }): Observable<PayrollReport> {
    // Simuler la génération d'un rapport de paie
    const report: PayrollReport = {
      totalPayroll: 750000,
      averageSalary: 5000,
      medianSalary: 4500,
      totalBenefits: 112500,
      totalDeductions: 187500,
      byDepartment: [
        {
          departmentId: 'dept_1',
          name: 'IT',
          employeeCount: 45,
          totalSalary: 270000,
          averageSalary: 6000,
          totalBenefits: 40500,
        },
      ],
      salaryDistribution: [
        { range: '2000-3000', count: 25, percentage: 16.7 },
        { range: '3000-4000', count: 35, percentage: 23.3 },
        { range: '4000-5000', count: 40, percentage: 26.7 },
        { range: '5000-7000', count: 30, percentage: 20.0 },
        { range: '7000+', count: 20, percentage: 13.3 },
      ],
      trends: [
        {
          period: '2024-01',
          totalPayroll: 750000,
          averageSalary: 5000,
          employeeCount: 150,
        },
      ],
    };

    return of(report);
  }

  // Impression de rapports
  printReport(reportId: string): Observable<boolean> {
    // Simuler l'impression
    const report = this.reportsSubject.value.find((r) => r.id === reportId);
    if (!report) {
      return of(false);
    }

    // Ouvrir une nouvelle fenêtre pour l'impression
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const htmlContent = this.generatePrintableHTML(report);
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      printWindow.print();
    }

    return of(true);
  }

  // Méthodes utilitaires privées
  private updateReport(id: string, updates: Partial<Report>): void {
    const reports = this.reportsSubject.value.map((report) =>
      report.id === id ? { ...report, ...updates } : report
    );
    this.reportsSubject.next(reports);
  }

  private generateMockData(request: ReportRequest): any[] {
    // Générer des données de test selon le type de rapport
    const mockData = [];
    for (let i = 0; i < 100; i++) {
      mockData.push({
        id: i + 1,
        name: `Item ${i + 1}`,
        value: Math.floor(Math.random() * 1000),
        date: new Date(2024, 0, Math.floor(Math.random() * 30) + 1),
        category: ['A', 'B', 'C'][Math.floor(Math.random() * 3)],
      });
    }
    return mockData;
  }

  private generatePDFContent(
    report: Report,
    request: ReportExportRequest
  ): string {
    // Simuler la génération PDF
    return `PDF Content for report: ${report.name}`;
  }

  private generateExcelContent(
    report: Report,
    request: ReportExportRequest
  ): string {
    // Simuler la génération Excel
    return `Excel Content for report: ${report.name}`;
  }

  private generateCSVContent(
    report: Report,
    request: ReportExportRequest
  ): string {
    // Générer du contenu CSV réel
    let csv = 'ID,Name,Value,Date,Category\n';
    report.data.forEach((row) => {
      csv += `${row.id},${row.name},${row.value},${row.date},${row.category}\n`;
    });
    return csv;
  }

  private generateHTMLContent(
    report: Report,
    request: ReportExportRequest
  ): string {
    // Générer du contenu HTML
    let html = `
      <html>
        <head>
          <title>${report.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .header { margin-bottom: 20px; }
            .summary { margin: 20px 0; padding: 10px; background-color: #f9f9f9; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${report.name}</h1>
            <p>${report.description}</p>
            <p>Généré le: ${report.metadata.generatedAt.toLocaleDateString(
              'fr-FR'
            )}</p>
          </div>
    `;

    if (request.includeSummary) {
      html += `
        <div class="summary">
          <h2>Résumé</h2>
          <p>Total d'enregistrements: ${report.summary.totalRecords}</p>
          <ul>
            ${report.summary.insights
              .map((insight) => `<li>${insight}</li>`)
              .join('')}
          </ul>
        </div>
      `;
    }

    html += `
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Nom</th>
                <th>Valeur</th>
                <th>Date</th>
                <th>Catégorie</th>
              </tr>
            </thead>
            <tbody>
    `;

    report.data.forEach((row) => {
      html += `
        <tr>
          <td>${row.id}</td>
          <td>${row.name}</td>
          <td>${row.value}</td>
          <td>${new Date(row.date).toLocaleDateString('fr-FR')}</td>
          <td>${row.category}</td>
        </tr>
      `;
    });

    html += `
            </tbody>
          </table>
        </body>
      </html>
    `;

    return html;
  }

  private generatePrintableHTML(report: Report): string {
    return this.generateHTMLContent(report, {
      reportId: report.id,
      format: ExportFormat.HTML,
      includeCharts: true,
      includeSummary: true,
    });
  }

  private generateId(): string {
    return 'report_' + Math.random().toString(36).substr(2, 9);
  }

  private loadMockData(): void {
    const mockReports: Report[] = [
      {
        id: 'report_1',
        name: 'Rapport Employés Janvier 2024',
        description: 'Rapport mensuel des employés',
        type: ReportType.EMPLOYEE,
        category: ReportCategory.OPERATIONAL,
        parameters: [],
        filters: [],
        columns: [],
        charts: [],
        data: [],
        summary: {
          totalRecords: 150,
          aggregations: {},
          trends: [],
          insights: ['150 employés actifs', 'Croissance de 5% ce mois'],
        },
        metadata: {
          generatedAt: new Date(),
          generatedBy: 'hr_manager',
          executionTime: 2.1,
          dataSource: 'LuminaHR Database',
          recordCount: 150,
          filters: [],
          version: '1.0',
        },
        status: 'COMPLETED' as any,
        visibility: 'DEPARTMENT' as any,
        isScheduled: true,
        schedule: {
          frequency: 'MONTHLY' as any,
          time: '09:00',
          dayOfMonth: 1,
          recipients: ['<EMAIL>'],
          format: ExportFormat.PDF,
          isActive: true,
          nextRun: new Date(2024, 1, 1, 9, 0),
        },
        availableFormats: [
          ExportFormat.PDF,
          ExportFormat.EXCEL,
          ExportFormat.CSV,
        ],
        createdBy: 'hr_manager',
        createdAt: new Date('2024-01-01'),
        lastGeneratedAt: new Date(),
        generationCount: 5,
        exportCount: 12,
      },
    ];

    const mockTemplates: ReportTemplate[] = [
      {
        id: 'template_1',
        name: 'Rapport Employés Standard',
        description: "Template pour les rapports d'employés",
        type: ReportType.EMPLOYEE,
        category: ReportCategory.OPERATIONAL,
        defaultParameters: [],
        defaultFilters: [],
        defaultColumns: [],
        defaultCharts: [],
        isSystem: true,
        isActive: true,
        tags: ['employés', 'standard'],
        createdBy: 'system',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        usageCount: 25,
      },
    ];

    this.reportsSubject.next(mockReports);
    this.templatesSubject.next(mockTemplates);
  }
}
