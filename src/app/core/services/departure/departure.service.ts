import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DepartureService {
  private apiUrl = `${environment.API_PREFIX}/company/departures`;

  constructor(private http: HttpClient) {}

  /**
   * Crée un nouveau départ.
   * @param departureData - Données du départ.
   * @returns Un Observable contenant la réponse du serveur.
   */
  createDeparture(departureData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}`, departureData).pipe(
      catchError((error) => {
        console.error('Erreur lors de la création du départ', error);
        return throwError(error);
      })
    );
  }

  /**
   * Récupère tous les départs d'une entreprise donnée.
   * @param companyId - L'ID de l'entreprise.
   * @returns Un Observable contenant la liste des départs.
   */
  getDepartures(companyId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${companyId}`).pipe(
      catchError((error) => {
        console.error('Erreur lors de la récupération des départs', error);
        return throwError(error);
      })
    );
  }

  /**
   * Récupère les détails d'un départ par ID.
   * @param departureId - L'ID du départ.
   * @returns Un Observable contenant les détails du départ.
   */
  getDepartureById(departureId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${departureId}`).pipe(
      catchError((error) => {
        console.error(`Erreur lors de la récupération du départ ${departureId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Met à jour un départ existant.
   * @param departureId - L'ID du départ.
   * @param departureData - Les nouvelles données du départ.
   * @returns Un Observable contenant la réponse du serveur.
   */
  updateDeparture(departureId: number, departureData: any): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${departureId}`, departureData).pipe(
      catchError((error) => {
        console.error(`Erreur lors de la mise à jour du départ ${departureId}`, error);
        return throwError(error);
      })
    );
  }

  /**
   * Supprime un départ par ID.
   * @param departureId - L'ID du départ.
   * @returns Un Observable contenant la réponse du serveur.
   */
  deleteDeparture(departureId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${departureId}`).pipe(
      catchError((error) => {
        console.error(`Erreur lors de la suppression du départ ${departureId}`, error);
        return throwError(error);
      })
    );
  }
}
