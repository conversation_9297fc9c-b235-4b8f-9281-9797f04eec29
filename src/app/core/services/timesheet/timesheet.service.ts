import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

/**
 * Represents a timesheet entity.
 */
export interface Timesheet {
  id: string;
  periodStart: string; // Start date of the timesheet period (ISO format)
  periodEnd: string; // End date of the timesheet period (ISO format)
  totalRegularHours: number; // Total number of regular hours worked
  totalOvertimeHours: number; // Total number of overtime hours
  totalUndertimeHours: number; // Total number of undertime hours
  employeeId: string; // The employee's unique ID
  employee?: {
    id: string;
    user: {
      profile: {
        firstName: string;
        lastName: string;
      };
    };
  };
  workDays: WorkDay[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateWorkday {
  date: string; // La date de la journée de travail (format 'YYYY-MM-DD')
  arrivalTime: string; // L'heure d'arrivée (format 'HH:mm')
  departureTime: string; // L'heure de départ (format 'HH:mm')
}

/**
 * Represents a workday entry within a timesheet.
 */
export interface WorkDay extends CreateWorkday {
  id: string;
  date: string; // The date of the workday (ISO format)
  regularHours: number; // Number of regular hours worked
  overtimeHours: number; // Number of overtime hours worked
  undertimeHours: number; // Number of undertime hours worked
}

export interface CreateTimesheetDto {
  employeeId: string;
  periodStart: string;
  periodEnd: string;
}

export interface UpdateTimesheetDto {
  periodStart?: string;
  periodEnd?: string;
}

export interface CreateWorkDayDto {
  date: string;
  arrivalTime: string;
  departureTime: string;
}

export interface UpdateWorkDayDto {
  arrivalTime?: string;
  departureTime?: string;
}

@Injectable({
  providedIn: 'root',
})
export class TimesheetService {
  private apiUrl = `${environment.API_PREFIX}/companies`;

  constructor(private http: HttpClient) {}

  /**
   * Gets all timesheets for a company with filtering
   */
  getTimesheets(
    companyId: string,
    employeeId?: string
  ): Observable<Timesheet[]> {
    let params = new HttpParams();
    if (employeeId) {
      params = params.set('employeeId', employeeId);
    }

    return this.http
      .get<Timesheet[]>(`${this.apiUrl}/${companyId}/timesheets`, { params })
      .pipe(
        catchError((error) => {
          console.error('Error fetching timesheets', error);
          return throwError(error);
        })
      );
  }

  /**
   * Creates a new timesheet for an employee.
   * @param companyId - The ID of the company
   * @param timesheet - The timesheet data to be created.
   * @returns An observable of the created timesheet.
   */
  createTimesheet(
    companyId: string,
    timesheet: CreateTimesheetDto
  ): Observable<Timesheet> {
    return this.http
      .post<Timesheet>(`${this.apiUrl}/${companyId}/timesheets`, timesheet)
      .pipe(
        catchError((error) => {
          console.error('Error creating timesheet', error);
          return throwError(error);
        })
      );
  }

  /**
   * Retrieves all timesheets associated with a given employee.
   * @param employeeId - The ID of the employee.
   * @returns An observable containing an array of timesheets.
   */
  getEmployeeTimesheets(employeeId: string): Observable<Timesheet[]> {
    return this.http.get<Timesheet[]>(`${this.apiUrl}/${employeeId}`);
  }

  /**
   * Retrieves a specific timesheet by its ID.
   * @param id - The ID of the timesheet.
   * @returns An observable containing the timesheet details.
   */
  getTimesheetById(id: string): Observable<Timesheet> {
    return this.http.get<Timesheet>(`${this.apiUrl}/timesheet/${id}`);
  }

  /**
   * Updates an existing timesheet.
   * @param id - The ID of the timesheet to be updated.
   * @param updates - The updated timesheet data.
   * @returns An observable containing the updated timesheet.
   */
  updateTimesheet(
    id: string,
    updates: Partial<Timesheet>
  ): Observable<Timesheet> {
    return this.http.patch<Timesheet>(
      `${this.apiUrl}/timesheet/${id}`,
      updates
    );
  }

  /**
   * Deletes a timesheet by its ID.
   * @param id - The ID of the timesheet to delete.
   * @returns An observable confirming the deletion.
   */
  deleteTimesheet(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/timesheet/${id}`);
  }

  /**
   * Adds workdays to an existing timesheet.
   * @param timesheetId - The ID of the timesheet to which workdays will be added.
   * @param workDays - The array of workday entries to add.
   * @returns An observable containing the added workdays.
   */
  addWorkDays(timesheetId: string, workDays: WorkDay[]): Observable<WorkDay[]> {
    return this.http.post<WorkDay[]>(
      `${this.apiUrl}/timesheet/${timesheetId}/workdays`,
      workDays
    );
  }

  /**
   * Updates an existing workday entry.
   * @param workdayId - The ID of the workday to update.
   * @param updates - The updated workday data.
   * @returns An observable containing the updated workday.
   */
  updateWorkDay(
    workdayId: string,
    updates: Partial<WorkDay>
  ): Observable<WorkDay> {
    return this.http.patch<WorkDay>(
      `${this.apiUrl}/workday/${workdayId}`,
      updates
    );
  }
}
