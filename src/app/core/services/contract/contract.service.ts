import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface Contract {
  id: string;
  employeeId: string;
  templateId: string;
  title: string;
  content: string;
  status: 'DRAFT' | 'ACTIVE' | 'EXPIRED' | 'TERMINATED';
  startDate: string;
  endDate?: string;
  variables: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ContractTemplate {
  id: string;
  name: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

@Injectable({
  providedIn: 'root',
})
export class ContractService {
  private apiUrl = `${environment.apiUrl}/contracts`;

  constructor(private http: HttpClient) {}

  getContracts(filter?: any): Observable<Contract[]> {
    // Mock data pour l'instant
    return of([]);
  }

  getContractById(id: string): Observable<Contract> {
    // Mock data pour l'instant
    const mockContract: Contract = {
      id,
      employeeId: '1',
      templateId: '1',
      title: 'Contrat de travail',
      content: 'Contenu du contrat...',
      status: 'ACTIVE',
      startDate: new Date().toISOString(),
      variables: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return of(mockContract);
  }

  getTemplates(): Observable<ContractTemplate[]> {
    // Mock data pour l'instant
    return of([]);
  }

  getContractStats(): Observable<any> {
    // Mock data pour l'instant
    return of({
      total: 0,
      active: 0,
      expired: 0,
    });
  }

  createContract(data: any): Observable<Contract> {
    // Mock data pour l'instant
    const mockContract: Contract = {
      id: '1',
      employeeId: data.employeeId,
      templateId: data.templateId,
      title: data.title,
      content: data.content,
      status: 'DRAFT',
      startDate: data.startDate,
      variables: data.variables || {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return of(mockContract);
  }

  updateContract(id: string, data: any): Observable<Contract> {
    return this.getContractById(id);
  }

  deleteContract(id: string): Observable<void> {
    return of(void 0);
  }

  /**
   * Dupliquer un contrat
   */
  duplicateContract(id: string): Observable<Contract> {
    return of({
      id: 'new-contract-id',
      title: 'Contrat dupliqué',
      content: 'Contenu du contrat dupliqué',
      employeeId: 'employee-id',
      companyId: 'company-id',
      templateId: 'template-id',
      startDate: new Date().toISOString(),
      status: 'DRAFT',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      variables: {},
    });
  }

  /**
   * Créer un template de contrat
   */
  createTemplate(template: any): Observable<ContractTemplate> {
    return of({
      id: 'new-template-id',
      name: template.name,
      content: template.content,
      companyId: 'company-id',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      variables: [],
    });
  }

  /**
   * Supprimer un template de contrat
   */
  deleteTemplate(id: string): Observable<void> {
    return of(void 0);
  }
}
