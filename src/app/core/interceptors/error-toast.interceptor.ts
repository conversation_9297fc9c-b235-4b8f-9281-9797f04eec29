import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastService } from '../../shared/services/toast.service';

@Injectable()
export class ErrorToastInterceptor implements HttpInterceptor {
  
  constructor(private toastService: ToastService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        // Ne pas afficher de toast pour certaines routes ou codes d'erreur spécifiques
        if (this.shouldSkipErrorToast(req, error)) {
          return throwError(() => error);
        }

        // Utiliser notre service de toast pour afficher l'erreur
        this.toastService.handleHttpError(error);

        return throwError(() => error);
      })
    );
  }

  private shouldSkipErrorToast(req: HttpRequest<any>, error: HttpErrorResponse): boolean {
    // Skip pour les routes d'authentification silencieuses
    if (req.url.includes('/auth/refresh') || req.url.includes('/auth/check')) {
      return true;
    }

    // Skip pour les erreurs 401 sur les routes publiques
    if (error.status === 401 && req.url.includes('/public/')) {
      return true;
    }

    // Skip si l'en-tête indique de ne pas afficher de toast
    if (req.headers.get('X-Skip-Error-Toast') === 'true') {
      return true;
    }

    return false;
  }
}
