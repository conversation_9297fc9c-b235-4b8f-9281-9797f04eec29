import { Injectable } from '@angular/core';
import {
  HttpRe<PERSON>,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { AuthService } from '../../services/auth/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  /**
   * Intercepts HTTP requests to add the access token.
   * If the token is expired, attempts to refresh it before resending the request.
   * @param request - The intercepted HTTP request.
   * @param next - The next request handler.
   * @returns An Observable of the HTTP event.
   */
  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    // URLs qui n'ont pas besoin d'authentification
    const publicUrls = [
      '/signin',
      '/signup',
      '/forgot-password',
      '/reset-password',
      '/verify-email',
      '/refresh-tokens',
    ];

    // Vérifier si l'URL est publique
    const isPublicUrl = publicUrls.some((url) => request.url.includes(url));

    // Si ce n'est pas une URL publique, ajouter le token
    const token = this.authService.getAccessToken();
    if (token && !isPublicUrl) {
      request = this.addTokenToRequest(request, token);
    }

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401 && token && !isPublicUrl) {
          return this.handle401Error(request, next);
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Adds the access token to the request header.
   * @param request - The HTTP request.
   * @param token - The access token.
   * @returns The cloned request with the token added.
   */
  private addTokenToRequest(
    request: HttpRequest<unknown>,
    token: string
  ): HttpRequest<unknown> {
    return request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  /**
   * Handles 401 errors (unauthorized) by refreshing the token.
   * @param request - The original HTTP request.
   * @param next - The next request handler.
   * @returns An Observable of the HTTP event after token refresh.
   */
  private handle401Error(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    return this.authService.refreshToken().pipe(
      switchMap(() => {
        const newToken = this.authService.getAccessToken();
        if (newToken) {
          request = this.addTokenToRequest(request, newToken);
        }
        return next.handle(request);
      }),
      catchError((error) => {
        this.authService.logout();
        return throwError(() => error);
      })
    );
  }
}
