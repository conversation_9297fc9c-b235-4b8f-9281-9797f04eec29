import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoaderService } from 'src/app/shared/components/loader/loade.service';

@Injectable()
export class HttpLoaderInterceptor implements HttpInterceptor {
  private requests = new Set<HttpRequest<any>>();

  constructor(private loader: LoaderService) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    if (this.requests.size === 0) {
      this.loader.show();
    }
    this.requests.add(req);

    return next.handle(req).pipe(
      finalize(() => {
        this.requests.delete(req);
        if (this.requests.size === 0) {
          this.loader.hide();
        }
      })
    );
  }
}
