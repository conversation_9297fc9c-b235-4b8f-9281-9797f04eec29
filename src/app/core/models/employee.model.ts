import { QueryOptions } from './base.model';

export interface AddressDto {
  street: string;
  city: string;
  postalCode: string;
  country: string;
}

export enum RoleEnum {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN_HR = 'ADMIN_HR',
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  EMPLOYEE = 'EMPLOYEE',
}

export enum EmploymentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  TERMINATED = 'TERMINATED',
  ON_LEAVE = 'ON_LEAVE',
}

export enum EmploymentType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT',
  INTERN = 'INTERN',
  CONSULTANT = 'CONSULTANT',
}

export interface Employee {
  id: string;
  userId: string;
  status: EmploymentStatus;
  hireDate: string;
  companyId: string;
  departmentId?: string;
  positionId?: string;
  employmentType: EmploymentType;
  workSchedule: string;
  user: {
    id: string;
    email: string;
    role: RoleEnum;
    profile: {
      firstName: string;
      lastName: string;
      phoneNumber?: string;
      birthDate?: string;
      avatar?: string;
    };
  };
  department?: {
    id: string;
    name: string;
  };
  position?: {
    id: string;
    title: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateEmployeeDto {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  birthDate?: string;
  hireDate: string;
  departmentId?: string;
  positionId?: string;
  employmentType: EmploymentType;
  workSchedule: string;
  baseSalary?: number;
  role?: RoleEnum;
}

export interface UpdateEmployeeDto {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  birthDate?: string;
  departmentId?: string;
  positionId?: string;
  employmentType?: EmploymentType;
  workSchedule?: string;
  status?: EmploymentStatus;
}

export interface EmployeeQueryOptions extends QueryOptions {
  /** Filter by company ID */
  companyId?: string;
  /** Filter by department ID */
  departmentId?: string;
  /** Filter by position ID */
  positionId?: string;
  /** Filter by employment status */
  status?: string;
  /** Filter by employment type */
  employmentType?: string;

  search?: string;
  /** Include related data */
  includeRelations?: {
    department?: boolean;
    position?: boolean;
    company?: boolean;
    salaries?: boolean;
    leaves?: boolean;
    timesheets?: boolean;
    payslips?: boolean;
    performanceEvaluations?: boolean;
    trainings?: boolean;
  };
}

export interface EmployeeStatistics {
  // Propriétés de base du backend
  total: number;
  active: number;
  inactive: number;
  terminated: number;
  recentHires: number;
  upcomingAnniversaries: number;

  // Propriétés calculées ajoutées par le service
  averageYearsOfService?: number;
  turnoverRate?: number;

  // Répartitions
  byDepartment: Array<{
    departmentId: string;
    departmentName: string;
    count: number;
  }>;
  byPosition: Array<{
    positionId: string;
    positionTitle: string;
    count: number;
  }>;

  // Propriétés legacy pour compatibilité
  totalEmployees?: number;
  activeEmployees?: number;
  inactiveEmployees?: number;
  newHiresThisMonth?: number;
  employeesByDepartment?: Array<{
    departmentName: string;
    count: number;
  }>;
  employeesByEmploymentType?: Array<{
    type: string;
    count: number;
  }>;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Legacy interface for backward compatibility
export interface EmployeeResponseDto extends Employee {}
