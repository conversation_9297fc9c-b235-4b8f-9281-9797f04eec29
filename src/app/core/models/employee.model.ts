import { QueryOptions } from './base.model';

export interface AddressDto {
  street: string;
  city: string;
  postalCode: string;
  country: string;
}

export enum RoleEnum {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN_HR = 'ADMIN_HR',
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  EMPLOYEE = 'EMPLOYEE',
}

export enum EmploymentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  TERMINATED = 'TERMINATED',
  ON_LEAVE = 'ON_LEAVE',
}

export enum EmploymentType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT',
  INTERN = 'INTERN',
  CONSULTANT = 'CONSULTANT',
}

export enum Sex {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

// Nouveau type basé sur le format de données reçu du backend
export interface EmployeeBackendResponse {
  _domainEvents: any[];
  id: string;
  createdAt: string;
  updatedAt: string;
  _personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    birthDate: string | null;
    phoneNumber: string | null;
    avatar: string | null;
    sex: Sex | null;
  };
  _employmentDetails: {
    hireDate: string;
    companyId: string;
    company: {
      id: string;
      companyName: string;
      email: string;
      phoneNumbers: string[];
      website: string;
      logo: string;
      officialName: string;
      taxIdentificationNumber: string;
      industry: string;
      description: string;
    };
    status: EmploymentStatus;
    employmentType: string; // ID du type d'emploi
    departmentId: string;
    department: {
      id: string;
      name: string;
      description: string;
      companyId: string;
    };
    position: {
      id: string;
      title: string;
      description: string;
      departmentId: string;
      requiredSkills: string[];
    };
    salaries: any[];
    leaves: any[];
    timesheets: any[];
    payslips: any[];
    performanceEvaluations: any[];
    trainings: any[];
    documents: any[];
    departures: any[];
  };
}

// Interfaces pour les types spécifiques utilisés dans les relations
export interface CompanyInfo {
  id: string;
  companyName: string;
  email: string;
  phoneNumbers: string[];
  website: string;
  logo: string;
  officialName: string;
  taxIdentificationNumber: string;
  industry: string;
  description: string;
}

export interface DepartmentInfo {
  id: string;
  name: string;
  description: string;
  companyId: string;
}

export interface PositionInfo {
  id: string;
  title: string;
  description: string;
  departmentId: string;
  requiredSkills: string[];
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  birthDate: string | null;
  phoneNumber: string | null;
  avatar?: string | null;
  sex: Sex | null;
}

export interface EmploymentDetails {
  hireDate: string;
  companyId: string;
  company: CompanyInfo;
  status: EmploymentStatus;
  employmentType: string;
  departmentId: string;
  department: DepartmentInfo;
  position: PositionInfo;
  salaries: any[];
  leaves: any[];
  timesheets: any[];
  payslips: any[];
  performanceEvaluations: any[];
  trainings: any[];
  documents: any[];
  departures: any[];
}

// Interface pour l'employé avec le format backend complet
export interface EmployeeResponse {
  _domainEvents: any[];
  id: string;
  createdAt: string;
  updatedAt: string;
  _personalInfo: PersonalInfo;
  _employmentDetails: EmploymentDetails;
}

export interface Employee {
  id: string;
  userId: string;
  status: EmploymentStatus;
  hireDate: string;
  companyId: string;
  departmentId?: string;
  positionId?: string;
  employmentType: EmploymentType;
  workSchedule: string;
  user: {
    id: string;
    email: string;
    role: RoleEnum;
    profile: {
      firstName: string;
      lastName: string;
      phoneNumber?: string;
      birthDate?: string;
      avatar?: string;
    };
  };
  department?: {
    id: string;
    name: string;
  };
  position?: {
    id: string;
    title: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateEmployeeDto {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  birthDate?: string;
  hireDate: string;
  departmentId?: string;
  positionId?: string;
  employmentType: EmploymentType;
  workSchedule: string;
  baseSalary?: number;
  role?: RoleEnum;
}

export interface UpdateEmployeeDto {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  birthDate?: string;
  departmentId?: string;
  positionId?: string;
  employmentType?: EmploymentType;
  workSchedule?: string;
  status?: EmploymentStatus;
}

export interface EmployeeQueryOptions extends QueryOptions {
  /** Filter by company ID */
  companyId?: string;
  /** Filter by department ID */
  departmentId?: string;
  /** Filter by position ID */
  positionId?: string;
  /** Filter by employment status */
  status?: string;
  /** Filter by employment type */
  employmentType?: string;

  search?: string;
  /** Include related data */
  includeRelations?: {
    department?: boolean;
    position?: boolean;
    company?: boolean;
    salaries?: boolean;
    leaves?: boolean;
    timesheets?: boolean;
    payslips?: boolean;
    performanceEvaluations?: boolean;
    trainings?: boolean;
  };
}

export interface EmployeeStatistics {
  // Propriétés de base du backend
  total: number;
  active: number;
  inactive: number;
  terminated: number;
  recentHires: number;
  upcomingAnniversaries: number;

  // Propriétés calculées ajoutées par le service
  averageYearsOfService?: number;
  turnoverRate?: number;

  // Répartitions
  byDepartment: Array<{
    departmentId: string;
    departmentName: string;
    count: number;
  }>;
  byPosition: Array<{
    positionId: string;
    positionTitle: string;
    count: number;
  }>;

  // Propriétés legacy pour compatibilité
  totalEmployees?: number;
  activeEmployees?: number;
  inactiveEmployees?: number;
  newHiresThisMonth?: number;
  employeesByDepartment?: Array<{
    departmentName: string;
    count: number;
  }>;
  employeesByEmploymentType?: Array<{
    type: string;
    count: number;
  }>;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Legacy interface for backward compatibility
export interface EmployeeResponseDto extends Employee {}
