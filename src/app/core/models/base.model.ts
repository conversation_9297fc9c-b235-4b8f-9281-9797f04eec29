/**
 * Pagination options for repository queries
 */
export interface PaginationOptions {
  /** Page number (1-based) */
  page: number;
  /** Number of items per page */
  limit: number;
  /** Field to sort by */
  sortBy?: string;
  /** Sort direction */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Pagination result containing data and metadata
 */
export interface PaginatedResult<T> {
  /** Array of items for the current page */
  data: T[];
  /** Pagination metadata */
  meta: {
    /** Current page number */
    page: number;
    /** Number of items per page */
    limit: number;
    /** Total number of items */
    total: number;
    /** Total number of pages */
    totalPages: number;
    /** Whether there is a next page */
    hasNext: boolean;
    /** Whether there is a previous page */
    hasPrevious: boolean;
  };
}

/**
 * Filter options for repository queries
 */
export interface FilterOptions {
  /** Field to filter by */
  field: string;
  /** Filter operator */
  operator:
    | 'eq'
    | 'ne'
    | 'gt'
    | 'gte'
    | 'lt'
    | 'lte'
    | 'in'
    | 'nin'
    | 'like'
    | 'ilike';
  /** Filter value */
  value: any;
}

/**
 * Query options for repository operations
 */
export interface QueryOptions {
  /** Pagination options */
  pagination?: PaginationOptions;
  /** Filter conditions */
  filters?: FilterOptions[];
  /** Relations to include */
  include?: string[];
  /** Fields to select */
  select?: string[];
}
