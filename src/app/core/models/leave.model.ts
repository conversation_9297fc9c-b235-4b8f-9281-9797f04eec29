import { EmployeeData } from 'src/app/modules/dashboard/models/employee';

export interface Leave {
  id?: string;
  leaveType: LeaveTypeEnum;
  startDate: Date;
  endDate: Date;
  reason: string;
  status: LeaveStatusEnum;
  employeeId: string;
  employee: EmployeeData;
}

export enum LeaveTypeEnum {
  ANNUAL = 'ANNUAL',
  SICK = 'SICK',
  MATERNITY = 'MATERNITY',
  PATERNITY = 'PATERNITY',
  UNPAID = 'UNPAID',
  OTHER = 'OTHER',
}

export enum LeaveStatusEnum {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}
