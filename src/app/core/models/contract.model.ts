import { Employee } from './company.model';

export interface Contract {
  id: string;
  employeeId: string;
  employee?: Employee;
  templateId?: string;
  template?: ContractTemplate;
  contractType: ContractType;
  title: string;
  startDate: Date;
  endDate?: Date;
  salary: number;
  currency: string;
  workingHours: number;
  status: ContractStatus;
  content: string; // Contenu HTML du contrat
  variables: ContractVariable[];
  signedDate?: Date;
  signedByEmployee?: boolean;
  signedByEmployer?: boolean;
  employeeSignature?: string;
  employerSignature?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
  version: number;
  isActive: boolean;
  notes?: string;
  attachments?: ContractAttachment[];
}

export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  contractType: ContractType;
  content: string; // Template HTML avec variables
  variables: TemplateVariable[];
  isDefault: boolean;
  isActive: boolean;
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy?: string;
  version: number;
  usageCount: number;
  aiGenerated?: boolean; // Pour les futurs templates générés par IA
  aiPrompt?: string; // Prompt utilisé pour générer le template
}

export interface ContractVariable {
  key: string;
  value: string;
  type: VariableType;
  label: string;
  required: boolean;
}

export interface TemplateVariable {
  key: string;
  label: string;
  type: VariableType;
  required: boolean;
  defaultValue?: string;
  options?: string[]; // Pour les select
  validation?: string; // Regex de validation
  description?: string;
}

export interface ContractAttachment {
  id: string;
  contractId: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  filePath: string;
  uploadedAt: Date;
  uploadedBy: string;
}

export enum ContractType {
  CDI = 'CDI',
  CDD = 'CDD',
  STAGE = 'STAGE',
  FREELANCE = 'FREELANCE',
  APPRENTISSAGE = 'APPRENTISSAGE',
  INTERIM = 'INTERIM',
  CONSULTANT = 'CONSULTANT',
}

export enum ContractStatus {
  DRAFT = 'DRAFT',
  PENDING_SIGNATURE = 'PENDING_SIGNATURE',
  SIGNED = 'SIGNED',
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED',
  CANCELLED = 'CANCELLED',
}

export enum VariableType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  SELECT = 'SELECT',
  BOOLEAN = 'BOOLEAN',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE',
}

export interface ContractStats {
  total: number;
  active: number;
  pending: number;
  expired: number;
  byType: { [key in ContractType]: number };
  byStatus: { [key in ContractStatus]: number };
  recentContracts: Contract[];
  expiringContracts: Contract[];
}

export interface ContractFilter {
  search?: string;
  contractType?: ContractType;
  status?: ContractStatus;
  employeeId?: string;
  startDate?: Date;
  endDate?: Date;
  createdBy?: string;
  isActive?: boolean;
}

export interface ContractFormData {
  employeeId: string;
  templateId?: string;
  contractType: ContractType;
  title: string;
  startDate: Date;
  endDate?: Date;
  salary: number;
  currency: string;
  workingHours: number;
  variables: ContractVariable[];
  notes?: string;
}

// Interface pour l'intégration IA future
export interface AIContractRequest {
  employeeId: string;
  contractType: ContractType;
  position: string;
  salary: number;
  startDate: Date;
  endDate?: Date;
  specialRequirements?: string;
  companyPolicies?: string[];
  language: 'fr' | 'en';
}

export interface AIContractResponse {
  generatedContent: string;
  suggestedVariables: TemplateVariable[];
  confidence: number;
  suggestions: string[];
  warnings: string[];
}
