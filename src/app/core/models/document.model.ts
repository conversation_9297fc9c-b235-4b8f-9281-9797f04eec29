import { Employee } from './company.model';

export interface DocumentModel {
  id: string;
  name: string;
  description?: string;
  fileName: string;
  originalFileName: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
  category: DocumentCategory;
  type: DocumentType;
  status: DocumentStatus;
  visibility: DocumentVisibility;
  tags: string[];

  // Relations
  employeeId?: string;
  employee?: Employee;
  departmentId?: string;
  companyId: string;

  // Métadonnées
  uploadedBy: string;
  uploadedAt: Date;
  updatedBy?: string;
  updatedAt?: Date;
  version: number;
  isActive: boolean;

  // Propriétés spécifiques
  expirationDate?: Date;
  isConfidential: boolean;
  requiresSignature: boolean;
  isSigned?: boolean;
  signedBy?: string;
  signedAt?: Date;

  // Workflow
  approvalRequired: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  rejectedBy?: string;
  rejectedAt?: Date;
  rejectionReason?: string;

  // Accès et permissions
  accessLog: DocumentAccess[];
  downloadCount: number;
  lastAccessedAt?: Date;

  // Archivage
  isArchived: boolean;
  archivedAt?: Date;
  archivedBy?: string;
  retentionPeriod?: number; // en mois
}

export interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  category: DocumentCategory;
  type: DocumentType;
  templatePath: string;
  variables: DocumentVariable[];
  isActive: boolean;
  isDefault: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
}

export interface DocumentVariable {
  key: string;
  label: string;
  type: VariableType;
  required: boolean;
  defaultValue?: string;
  options?: string[];
  validation?: string;
  description?: string;
}

export interface DocumentAccess {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  action: DocumentAction;
  accessedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface DocumentFolder {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  path: string;
  color?: string;
  icon?: string;
  isSystem: boolean;
  permissions: FolderPermission[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface FolderPermission {
  userId: string;
  role: string;
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canShare: boolean;
}

export enum DocumentCategory {
  CONTRACTS = 'CONTRACTS',
  POLICIES = 'POLICIES',
  PROCEDURES = 'PROCEDURES',
  FORMS = 'FORMS',
  CERTIFICATES = 'CERTIFICATES',
  TRAINING = 'TRAINING',
  COMPLIANCE = 'COMPLIANCE',
  PERSONAL = 'PERSONAL',
  ADMINISTRATIVE = 'ADMINISTRATIVE',
  LEGAL = 'LEGAL',
  FINANCIAL = 'FINANCIAL',
  REPORTS = 'REPORTS',
  TEMPLATES = 'TEMPLATES',
  OTHER = 'OTHER',
}

export enum DocumentType {
  PDF = 'PDF',
  WORD = 'WORD',
  EXCEL = 'EXCEL',
  POWERPOINT = 'POWERPOINT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  TEXT = 'TEXT',
  ARCHIVE = 'ARCHIVE',
  OTHER = 'OTHER',
}

export enum DocumentStatus {
  DRAFT = 'DRAFT',
  PENDING_REVIEW = 'PENDING_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
  EXPIRED = 'EXPIRED',
}

export enum DocumentVisibility {
  PUBLIC = 'PUBLIC',
  INTERNAL = 'INTERNAL',
  CONFIDENTIAL = 'CONFIDENTIAL',
  RESTRICTED = 'RESTRICTED',
  PRIVATE = 'PRIVATE',
}

export enum DocumentAction {
  VIEW = 'VIEW',
  DOWNLOAD = 'DOWNLOAD',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  SHARE = 'SHARE',
  PRINT = 'PRINT',
  SIGN = 'SIGN',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
}

export enum VariableType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  SELECT = 'SELECT',
  BOOLEAN = 'BOOLEAN',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE',
}

export interface DocumentFilter {
  search?: string;
  category?: DocumentCategory;
  type?: DocumentType;
  status?: DocumentStatus;
  visibility?: DocumentVisibility;
  employeeId?: string;
  departmentId?: string;
  uploadedBy?: string;
  dateFrom?: Date;
  dateTo?: Date;
  tags?: string[];
  isExpired?: boolean;
  requiresSignature?: boolean;
  isArchived?: boolean;
}

export interface DocumentStats {
  total: number;
  byCategory: { [key in DocumentCategory]: number };
  byType: { [key in DocumentType]: number };
  byStatus: { [key in DocumentStatus]: number };
  totalSize: number;
  recentUploads: DocumentModel[];
  expiringDocuments: DocumentModel[];
  pendingApprovals: DocumentModel[];
  mostDownloaded: DocumentModel[];
}

export interface DocumentUploadRequest {
  file: File;
  name: string;
  description?: string;
  category: DocumentCategory;
  visibility: DocumentVisibility;
  employeeId?: string;
  departmentId?: string;
  tags: string[];
  expirationDate?: Date;
  isConfidential: boolean;
  requiresSignature: boolean;
  approvalRequired: boolean;
}

export interface BulkDocumentOperation {
  documentIds: string[];
  operation: BulkOperation;
  targetFolderId?: string;
  newCategory?: DocumentCategory;
  newVisibility?: DocumentVisibility;
  newTags?: string[];
}

export enum BulkOperation {
  MOVE = 'MOVE',
  COPY = 'COPY',
  DELETE = 'DELETE',
  ARCHIVE = 'ARCHIVE',
  CHANGE_CATEGORY = 'CHANGE_CATEGORY',
  CHANGE_VISIBILITY = 'CHANGE_VISIBILITY',
  ADD_TAGS = 'ADD_TAGS',
  REMOVE_TAGS = 'REMOVE_TAGS',
}
