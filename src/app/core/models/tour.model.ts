export interface TourStep {
  id: string;
  title: string;
  content: string;
  target: string; // CSS selector
  position: 'top' | 'bottom' | 'left' | 'right';
  showNext?: boolean;
  showPrev?: boolean;
  showSkip?: boolean;
  action?: () => void;
  route?: string; // Route à naviguer avant d'afficher l'étape
}

export interface TourConfig {
  steps: TourStep[];
  backdrop?: boolean;
  highlightPadding?: number;
  scrollToTarget?: boolean;
  scrollOffset?: number;
}

export interface TourState {
  isActive: boolean;
  currentStepIndex: number;
  isCompleted: boolean;
  isFirstVisit: boolean;
}
