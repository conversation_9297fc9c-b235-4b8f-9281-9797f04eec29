# Format de données des employés

## Nouveau format de données (EmployeeNew)

Le nouveau format de données des employés reçu du backend suit cette structure :

```typescript
interface EmployeeNew {
  _domainEvents: any[];
  id: string;
  createdAt: string;
  updatedAt: string;
  _personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    birthDate: string | null;
    phoneNumber: string | null;
    sex: Sex | null;
  };
  _employmentDetails: {
    hireDate: string;
    companyId: string;
    company: CompanyInfo;
    status: EmploymentStatus;
    employmentType: string;
    departmentId: string;
    department: DepartmentInfo;
    position: PositionInfo;
    salaries: any[];
    leaves: any[];
    timesheets: any[];
    payslips: any[];
    performanceEvaluations: any[];
    trainings: any[];
    documents: any[];
    departures: any[];
  };
}
```

## Exemple de données reçues

```json
{
  "_domainEvents": [],
  "id": "91383cc4-3e26-4ebc-b92a-2c175489cfcf",
  "createdAt": "2025-07-31T20:10:07.984Z",
  "updatedAt": "2025-07-31T20:10:07.984Z",
  "_personalInfo": {
    "firstName": "Blaine",
    "lastName": "Ellis",
    "email": "<EMAIL>",
    "birthDate": "1984-08-18T00:00:00.000Z",
    "phoneNumber": null,
    "sex": null
  },
  "_employmentDetails": {
    "hireDate": "2018-08-01T00:00:00.000Z",
    "companyId": "9b040bb9-a0dc-43f0-8666-f7ed6b1ca724",
    "company": {
      "id": "9b040bb9-a0dc-43f0-8666-f7ed6b1ca724",
      "companyName": "Perspiciatis eligen",
      "email": "<EMAIL>",
      "phoneNumbers": [],
      "website": "https://www.nyvu.biz",
      "logo": "https://www.cefumoqoronuhe.in",
      "officialName": "Hic aliqua Voluptas",
      "taxIdentificationNumber": "Enim ratione qui fug",
      "industry": "Finance",
      "description": "Voluptate sed rerum "
    },
    "status": "ACTIVE",
    "employmentType": "e96d4a87-7191-4162-9c29-e7e5d374bd64",
    "departmentId": "7528eaa7-662d-4dbe-a449-218505adb78c",
    "department": {
      "id": "e96d4a87-7191-4162-9c29-e7e5d374bd64",
      "name": "Jaime Burch up",
      "description": "Hic cumque tempor iu",
      "companyId": "9b040bb9-a0dc-43f0-8666-f7ed6b1ca724"
    },
    "position": {
      "id": "7528eaa7-662d-4dbe-a449-218505adb78c",
      "title": "Non adipisicing volu",
      "description": "Molestias mollit et ",
      "departmentId": "e96d4a87-7191-4162-9c29-e7e5d374bd64",
      "requiredSkills": [
        "Eos aliquip ut illu",
        "kjkkiho"
      ]
    },
    "salaries": [],
    "leaves": [],
    "timesheets": [],
    "payslips": [],
    "performanceEvaluations": [],
    "trainings": [],
    "documents": [],
    "departures": []
  }
}
```

## Migration de l'ancien format

### Ancien format (Employee)
```typescript
interface Employee {
  id: string;
  user: {
    email: string;
    profile: {
      firstName: string;
      lastName: string;
      phoneNumber?: string;
      birthDate?: string;
    };
  };
  hireDate: string;
  department?: { id: string; name: string; };
  position?: { id: string; title: string; };
}
```

### Correspondances de migration

| Ancien format | Nouveau format |
|---------------|----------------|
| `employee.user.email` | `employee._personalInfo.email` |
| `employee.user.profile.firstName` | `employee._personalInfo.firstName` |
| `employee.user.profile.lastName` | `employee._personalInfo.lastName` |
| `employee.user.profile.phoneNumber` | `employee._personalInfo.phoneNumber` |
| `employee.user.profile.birthDate` | `employee._personalInfo.birthDate` |
| `employee.hireDate` | `employee._employmentDetails.hireDate` |
| `employee.department` | `employee._employmentDetails.department` |
| `employee.position` | `employee._employmentDetails.position` |

## Composants mis à jour

Les composants suivants ont été mis à jour pour utiliser le nouveau format :

1. **EmployeeProfileComponent** - Profil détaillé d'un employé
2. **OverviewEmployeesTableComponent** - Table de liste des employés
3. **OverviewEmployeesTableItemComponent** - Élément de ligne dans la table
4. **CompanyProfileComponent** - Profil de l'entreprise (section employés)

## Services mis à jour

- **EmployeeService.getEmployeeById()** - Retourne maintenant `EmployeeNew`
- **EmployeeService.getEmployees()** - Retourne maintenant `PaginatedResult<EmployeeNew>`

## Notes importantes

1. **Génération de mot de passe** : La fonctionnalité de génération de mot de passe nécessite un ID utilisateur qui n'est pas disponible dans le nouveau format. Cette fonctionnalité doit être adaptée.

2. **Types de données** : Le nouveau format utilise des propriétés privées préfixées par `_` qui reflètent l'architecture du backend.

3. **Relations** : Les relations (company, department, position) sont maintenant incluses directement dans les données au lieu d'être des références simples.

## Changements effectués

### ✅ Composants mis à jour
- [x] **EmployeeProfileComponent** - Utilise maintenant `EmployeeNew`
- [x] **OverviewEmployeesTableComponent** - Utilise maintenant `EmployeeNew`
- [x] **OverviewEmployeesTableItemComponent** - Utilise maintenant `EmployeeNew`
- [x] **CompanyProfileComponent** - Section employés mise à jour
- [x] **PayslipListComponent** - Utilise maintenant `EmployeeNew`
- [x] **TimesheetListComponent** - Utilise maintenant `EmployeeNew`

### ✅ Services mis à jour
- [x] **EmployeeService.getEmployeeById()** - Retourne maintenant `EmployeeNew`
- [x] **EmployeeService.getEmployees()** - Retourne maintenant `PaginatedResult<EmployeeNew>`

### ✅ Modèles mis à jour
- [x] **Company.employees** - Utilise maintenant `EmployeeNew[]`
- [x] Création des interfaces `PersonalInfo`, `EmploymentDetails`, `CompanyInfo`, etc.

### ⚠️ Fonctionnalités temporairement désactivées
- **Génération de mot de passe** - Nécessite adaptation au nouveau format
- **Sauvegarde du profil employé** - Nécessite adaptation au nouveau format

## TODO

- [ ] Adapter la fonctionnalité de génération de mot de passe
- [ ] Adapter la fonctionnalité de sauvegarde du profil employé
- [ ] Mettre à jour ContractViewComponent (utilise encore l'ancien format)
- [ ] Mettre à jour EmployeesWithSkeletonComponent (composant d'exemple)
- [ ] Mettre à jour les tests unitaires
- [ ] Mettre à jour la documentation API
- [ ] Ajouter l'avatar dans PersonalInfo (actuellement manquant)

## Composants restants à vérifier

1. **ContractViewComponent** - Utilise `employee.user.profile`
2. **EmployeesWithSkeletonComponent** - Composant d'exemple qui utilise l'ancien format
3. Autres composants qui pourraient référencer des employés

## Notes de migration

- Les propriétés sont maintenant préfixées par `_` (ex: `_personalInfo`, `_employmentDetails`)
- Les relations sont incluses directement dans les données
- Le format est plus structuré et reflète l'architecture du backend
