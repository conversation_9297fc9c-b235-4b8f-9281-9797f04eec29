<div>
  <app-loader
    *ngIf="loader.isLoading$ | async"
    [message]="'Traitement en cours'"
    [subMessage]="'Veuillez patienter...'"
    [blockNavigation]="true"
    [overlay]="true"
    [isDark]="themeService.isDark"
  />
  <router-outlet></router-outlet>

  <!-- Notre système de toast personnalisé -->
  <app-toast-container></app-toast-container>

  <!-- Tour guidé -->
  <app-tour-welcome></app-tour-welcome>
  <app-tour-step></app-tour-step>
  <app-tour-help></app-tour-help>
</div>
