{"name": "hr-solution", "displayName": "Hr Solution", "version": "0.0.1", "description": "Human ressource solution", "author": "<PERSON>", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve --open", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:e2e": "npx playwright test --ui", "prettier": "prettier --config ./.prettierrc --write \"src/{app,environments}/**/*{.ts,.html,.scss,.json}\"", "prettier:verify": "prettier --config ./.prettierrc --check \"src/{app,environments}/**/*{.ts,.html,.scss,.json}\"", "prettier:staged": "pretty-quick --staged", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.2.2", "@angular/common": "^19.2.2", "@angular/compiler": "^19.2.2", "@angular/core": "^19.2.2", "@angular/forms": "^19.2.2", "@angular/material": "^19.2.7", "@angular/platform-browser": "^19.2.2", "@angular/platform-browser-dynamic": "^19.2.2", "@angular/router": "^19.2.2", "@auth0/angular-jwt": "^5.2.0", "@material/dialog": "^14.0.0", "@ngrx/store": "^19.0.1", "@tailwindcss/aspect-ratio": "^0.4.0", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/typography": "^0.5.4", "angular-svg-icon": "^13.0.0", "apexcharts": "^3.35.3", "date-fns": "^4.1.0", "dialog": "link:@angular/material/dialog", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "ng-apexcharts": "^1.7.1", "ngx-cookie-service": "^19.1.2", "ngx-sonner": "^2.0.1", "ngx-toastr": "^19.0.0", "rxjs": "^7.5.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.2", "@angular/cli": "^19.2.2", "@angular/compiler-cli": "^19.2.2", "@ngrx/eslint-plugin": "^19.0.1", "@playwright/test": "^1.42.1", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "autoprefixer": "^10.4.7", "cz-conventional-changelog": "^3.3.0", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "postcss": "^8.4.14", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.12", "tailwind-scrollbar": "^1.3.1", "tailwindcss": "^3.1.6", "typescript": "~5.8.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}