# Documentation Technique - LuminaHR Global

## 🏗️ Architecture de l'Application

### Vue d'ensemble
LuminaHR Global suit une architecture modulaire basée sur Angular 18 avec des composants standalone, permettant une meilleure séparation des responsabilités et un lazy loading optimisé.

### Patterns Architecturaux
- **Standalone Components** : Composants autonomes sans NgModules
- **Lazy Loading** : Chargement à la demande des modules
- **Service-Oriented Architecture** : Services métier centralisés
- **Reactive Programming** : RxJS pour la gestion d'état
- **Feature-First Structure** : Organisation par fonctionnalités

## 📁 Structure Détaillée

### Core Module (`src/app/core/`)
```
core/
├── constants/
│   ├── menu.ts              # Configuration du menu principal
│   ├── routes.ts            # Constantes de routage
│   └── api.ts               # URLs et endpoints API
├── guards/
│   ├── auth.guard.ts        # Protection des routes authentifiées
│   └── role.guard.ts        # Contrôle d'accès par rôles
├── models/
│   ├── company.model.ts     # Modèles entreprise et employés
│   ├── leave.model.ts       # Modèles de congés et absences
│   ├── goal.model.ts        # Modèles d'objectifs et évaluations
│   ├── contract.model.ts    # Modèles de contrats
│   ├── document.model.ts    # Modèles documentaires
│   └── report.model.ts      # Modèles de rapports
└── services/
    ├── employee/
    │   └── employee.service.ts
    ├── leave/
    │   └── leave.service.ts
    ├── goal/
    │   └── goal.service.ts
    ├── contract/
    │   └── contract.service.ts
    ├── document/
    │   └── document.service.ts
    └── report/
        └── report.service.ts
```

### Modules Fonctionnels

#### Module d'Authentification (`src/app/modules/auth/`)
- **Composants** : Login, Register, Forgot Password
- **Services** : AuthService, TokenService
- **Guards** : AuthGuard, GuestGuard
- **Interceptors** : AuthInterceptor, ErrorInterceptor

#### Module Dashboard (`src/app/modules/dashboard/`)
```
dashboard/
├── components/              # Composants partagés
│   ├── sidebar/            # Navigation latérale
│   ├── header/             # En-tête avec notifications
│   ├── breadcrumb/         # Fil d'Ariane
│   └── widgets/            # Widgets réutilisables
└── pages/                  # Pages de l'application
    ├── employees/          # Gestion des employés
    ├── leave/              # Gestion des congés
    ├── goals/              # Gestion des objectifs
    ├── contracts/          # Gestion des contrats
    ├── documents/          # Gestion documentaire
    └── reports/            # Rapports et analytics
```

## 🔧 Services et Architecture de Données

### Services Métier

#### EmployeeService
```typescript
class EmployeeService {
  // CRUD Operations
  getEmployees(): Observable<Employee[]>
  getEmployeeById(id: string): Observable<Employee>
  createEmployee(employee: CreateEmployeeRequest): Observable<Employee>
  updateEmployee(id: string, updates: Partial<Employee>): Observable<Employee>
  deleteEmployee(id: string): Observable<boolean>
  
  // Business Logic
  getEmployeesByDepartment(departmentId: string): Observable<Employee[]>
  searchEmployees(query: string): Observable<Employee[]>
  getEmployeeHierarchy(): Observable<EmployeeHierarchy>
  generateEmployeeReport(): Observable<EmployeeReport>
}
```

#### DocumentService
```typescript
class DocumentService {
  // Document Management
  getDocuments(filter?: DocumentFilter): Observable<DocumentModel[]>
  uploadDocument(request: DocumentUploadRequest): Observable<DocumentModel>
  downloadDocument(id: string): Observable<Blob>
  deleteDocument(id: string): Observable<boolean>
  
  // Advanced Features
  searchDocuments(query: string): Observable<DocumentModel[]>
  bulkOperation(operation: BulkDocumentOperation): Observable<boolean>
  getDocumentStats(): Observable<DocumentStats>
  approveDocument(id: string): Observable<DocumentModel>
}
```

#### ReportService
```typescript
class ReportService {
  // Report Generation
  generateReport(request: ReportRequest): Observable<Report>
  exportReport(request: ReportExportRequest): Observable<Blob>
  printReport(reportId: string): Observable<boolean>
  
  // Predefined Reports
  generateEmployeeReport(): Observable<EmployeeReport>
  generateAttendanceReport(): Observable<AttendanceReport>
  generatePayrollReport(): Observable<PayrollReport>
}
```

### Gestion d'État
- **BehaviorSubject** : État local des services
- **Observable Streams** : Communication réactive
- **Error Handling** : Gestion centralisée des erreurs
- **Loading States** : États de chargement unifiés

## 🎨 Composants et UI

### Composants Standalone
Tous les composants utilisent l'approche standalone d'Angular 18 :

```typescript
@Component({
  selector: 'app-document-library',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './document-library.component.html'
})
export class DocumentLibraryComponent {
  // Component logic
}
```

### Design System
- **Tailwind CSS** : Framework CSS utilitaire
- **Composants réutilisables** : Boutons, formulaires, modales
- **Thème sombre** : Support complet avec CSS variables
- **Responsive Design** : Mobile-first approach

### Patterns UI
- **Container/Presenter** : Séparation logique/présentation
- **Smart/Dumb Components** : Composants intelligents et de présentation
- **Event Emitters** : Communication parent-enfant
- **Template-driven Forms** : Formulaires avec validation

## 🔄 Routing et Navigation

### Configuration des Routes
```typescript
const routes: Routes = [
  {
    path: 'dashboard',
    loadComponent: () => import('./dashboard.component'),
    children: [
      {
        path: 'documents',
        loadChildren: () => import('./documents/documents.routes')
      },
      {
        path: 'reports',
        loadChildren: () => import('./reports/reports.routes')
      }
    ]
  }
];
```

### Guards et Resolvers
- **AuthGuard** : Protection des routes authentifiées
- **RoleGuard** : Contrôle d'accès par rôles
- **DataResolver** : Pré-chargement des données

## 📊 Gestion des Données

### Modèles de Données

#### Document Model
```typescript
interface DocumentModel {
  id: string;
  name: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  category: DocumentCategory;
  status: DocumentStatus;
  uploadedAt: Date;
  // ... autres propriétés
}
```

#### Report Model
```typescript
interface Report {
  id: string;
  name: string;
  type: ReportType;
  data: any[];
  metadata: ReportMetadata;
  availableFormats: ExportFormat[];
  // ... autres propriétés
}
```

### Validation des Données
- **TypeScript Interfaces** : Typage fort
- **Runtime Validation** : Validation côté client
- **Error Boundaries** : Gestion des erreurs de données

## 🔒 Sécurité

### Authentification
- **JWT Tokens** : Stockage sécurisé des tokens
- **Token Refresh** : Renouvellement automatique
- **Route Protection** : Guards sur les routes sensibles

### Autorisation
- **Role-Based Access Control** : Contrôle par rôles
- **Feature Flags** : Activation conditionnelle de fonctionnalités
- **Data Filtering** : Filtrage des données par permissions

### Sécurité Frontend
- **XSS Protection** : Sanitisation des données
- **CSRF Protection** : Protection contre les attaques CSRF
- **Content Security Policy** : Politique de sécurité du contenu

## 🧪 Tests

### Stratégie de Tests
- **Unit Tests** : Tests unitaires avec Jest
- **Integration Tests** : Tests d'intégration des services
- **E2E Tests** : Tests end-to-end avec Cypress
- **Component Tests** : Tests des composants Angular

### Configuration des Tests
```typescript
// jest.config.js
module.exports = {
  preset: 'jest-preset-angular',
  setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
  testMatch: ['**/*.spec.ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.spec.ts',
    '!src/main.ts'
  ]
};
```

## 🚀 Performance

### Optimisations
- **Lazy Loading** : Chargement à la demande
- **OnPush Strategy** : Détection de changements optimisée
- **TrackBy Functions** : Optimisation des listes
- **Bundle Splitting** : Division des bundles
- **Tree Shaking** : Élimination du code mort

### Monitoring
- **Core Web Vitals** : Métriques de performance
- **Bundle Analysis** : Analyse de la taille des bundles
- **Memory Profiling** : Profilage mémoire
- **Network Monitoring** : Surveillance réseau

## 🔧 Outils de Développement

### Configuration ESLint
```json
{
  "extends": [
    "@angular-eslint/recommended",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "warn",
    "@angular-eslint/component-selector": ["error", {
      "prefix": "app",
      "style": "kebab-case"
    }]
  }
}
```

### Configuration Prettier
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

### Scripts NPM
```json
{
  "scripts": {
    "start": "ng serve",
    "build": "ng build",
    "test": "jest",
    "test:watch": "jest --watch",
    "e2e": "cypress run",
    "lint": "ng lint",
    "format": "prettier --write src/**/*.{ts,html,scss}"
  }
}
```

## 📦 Déploiement

### Build de Production
```bash
# Build optimisé
ng build --configuration=production

# Analyse des bundles
ng build --stats-json
npx webpack-bundle-analyzer dist/stats.json
```

### Configuration Docker
```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Variables d'Environnement
```typescript
// environment.prod.ts
export const environment = {
  production: true,
  apiUrl: 'https://api.lumina-hr.com',
  enableAnalytics: true,
  logLevel: 'error'
};
```

## 🔄 CI/CD

### Pipeline GitHub Actions
```yaml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test
      - run: npm run lint
      - run: npm run build
```

### Déploiement Automatisé
- **Staging** : Déploiement automatique sur merge
- **Production** : Déploiement manuel avec validation
- **Rollback** : Procédure de retour en arrière
- **Health Checks** : Vérifications post-déploiement

---

Cette documentation technique fournit une vue d'ensemble complète de l'architecture et des patterns utilisés dans LuminaHR Global. Elle sera mise à jour régulièrement pour refléter les évolutions du projet.
